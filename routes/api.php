<?php

use App\Http\Controllers\ConnectionRequestController;
use App\Http\Controllers\ConnectionRequestStatusController;
use App\Http\Controllers\FollowupQuestionSendController;
use App\Http\Controllers\InvitationController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\QuestionActivityController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuestionSendController;
use App\Http\Controllers\QuestionStatusController;
use App\Http\Controllers\RelationshipController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TagQuestionController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VideoCommentController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\VideoViewController;
use App\Http\Controllers\MembershipController;
use App\Http\Controllers\MembershipTypeController;
use App\Http\Controllers\MembershipGiftController;
use App\Http\Controllers\OfferCodeController;
use App\Http\Controllers\PaywallActivityController;
use App\Http\Controllers\PaywallActivityTypeController;
use App\Http\Controllers\RevenueCatWebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
 */

Route::group(['middleware' => ['auth:api']], function () {
    Route::resource('user', UserController::class);
    Route::get('user/{userId}/profile', [UserController::class, 'getProfile']);
    Route::post('user/find-by-contacts', [UserController::class, 'findByContacts']);
    Route::post('profile-pic', [UserController::class, 'uploadOrUpdateProfilePic']);

    Route::resource('tag', TagController::class);
    Route::resource('video', VideoController::class);
    Route::resource('video-comment', VideoCommentController::class);
    Route::resource('video-view', VideoViewController::class);
    Route::post('/video-view/like', [VideoViewController::class, 'likeVideo']);
    Route::resource('question', QuestionController::class);
    Route::resource('tag-question', TagQuestionController::class);
    Route::resource('invitation', InvitationController::class);
    Route::post('invitation/create', [InvitationController::class, 'create']);
    Route::post('invitation/delete', [InvitationController::class, 'delete']);
    Route::resource('notification', NotificationController::class);
    Route::resource('relationship', RelationshipController::class);
    Route::resource('question-send', QuestionSendController::class);
    Route::resource('question-status', QuestionStatusController::class);
    Route::resource('question-activity', QuestionActivityController::class);
    Route::resource('connection-request', ConnectionRequestController::class);
    Route::post('connection-request/create-or-update', [ConnectionRequestController::class, 'createOrUpdate']);
    Route::resource('connection-request-status', ConnectionRequestStatusController::class);

    Route::post('/video/create-upload-url', [VideoController::class, 'createUploadUrl']);
    Route::post('/video/upload', [VideoController::class, 'uploadVideo']);
    Route::get('home-feed', [VideoController::class, 'homeFeed']);
    Route::get('inbox-outbox', [QuestionSendController::class, 'getInboxOutbox']);
    Route::get('/inbox-outbox-counts', [QuestionSendController::class, 'getCounts']);

    Route::patch('question-sends/{questionSend}/discard', [QuestionSendController::class, 'discard']);

    Route::get('connection-requests/discarded', [ConnectionRequestController::class, 'discardedList']);

    Route::delete('connection-requests/{id}/delete-with-related',
        [ConnectionRequestController::class, 'deleteWithRelatedData']);

    Route::middleware('admin')->group(function () {
        Route::resource('followup-question-send', FollowupQuestionSendController::class);
    });

    Route::post('followup-question-send/create', [FollowupQuestionSendController::class, 'create']);
    Route::post('add-admin', [UserController::class, 'addAdmin']);


    Route::resource('membership-type', MembershipTypeController::class);
    Route::resource('membership', MembershipController::class);
    Route::resource('membership-gift', MembershipGiftController::class);
    Route::resource('offer-code', OfferCodeController::class);
    Route::resource('paywall-activity', PaywallActivityController::class);
    Route::resource('paywall-activity-type', PaywallActivityTypeController::class);

});

Route::post('register', [UserController::class, 'register']);
Route::post('verify', [UserController::class, 'verify']);
Route::post('login', [UserController::class, 'login']);
Route::post('resend-verification-code', [UserController::class, 'resendVerificationCode']);
Route::post('forgot-password', [UserController::class, 'forgotPassword']);
Route::post('reset-password', [UserController::class, 'resetPassword']);
Route::post('find-by-phone-number', [UserController::class, 'findByPhoneNumber']);

Route::post('/mux-webhook', [VideoController::class, 'handleWebhook']);

Route::post('/refresh-token', [UserController::class, 'refreshToken']);

Route::post('revenuecat/webhook', [RevenueCatWebhookController::class, 'handle']);
