<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OfferCodeUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'offer_code_id',
        'redeemed_at',
    ];

    protected $casts = [
        'redeemed_at' => 'datetime',
    ];

    /**
     * Get the user who redeemed the offer code.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the offer code that was redeemed.
     */
    public function offerCode(): BelongsTo
    {
        return $this->belongsTo(OfferCode::class);
    }
}
