<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionActivity extends Model
{
    use HasFactory;
    protected $fillable = ['question_id', 'question_status_id', 'question_send_id'];

    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id');
    }
    public function questionSends()
    {
        return $this->belongsTo(QuestionSend::class, 'question_send_id');
    }
    public function questionStatus()
    {
        return $this->belongsTo(QuestionStatus::class);
    }
}
