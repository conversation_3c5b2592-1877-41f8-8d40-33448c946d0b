<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Membership extends Model
{
    protected $fillable = [
        'membership_type_id',
        'started_at',
        'end_at',
        'is_autopay_enabled',
        'free_trials_used_count',
        'available_gifts_count'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'end_at' => 'datetime',
        'is_autopay_enabled' => 'boolean'
    ];

    public function membershipType(): BelongsTo
    {
        return $this->belongsTo(MembershipType::class);
    }

    public function user(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }
}
