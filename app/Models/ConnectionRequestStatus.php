<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConnectionRequestStatus extends Model
{
    use HasFactory;

    protected $fillable = ['title'];

    const STATUS_PENDING = "pending";
    const STATUS_APPROVED = "approved";
    const STATUS_DISCARDED = "discarded";
    const STATUS_INVITED = "invited";
    const STATUS_RESENT = "resent";
    const STATUS_RECOVER = "recover";

    const ALL_STATUSES = [
        self::STATUS_PENDING,
        self::STATUS_APPROVED,
        self::STATUS_DISCARDED,
        self::STATUS_INVITED,
        self::STATUS_RESENT,
        self::STATUS_RECOVER
    ];

}
