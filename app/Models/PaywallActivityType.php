<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaywallActivityType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description'
    ];

    // Define paywall activity type constants (all lowercase, underscores for spaces)
    const PURCHASE = 'purchase';
    const RENEWAL = 'renewal';
    const CANCELLATION = 'cancellation';
    const GIFT_SENT = 'gift_sent';
    const GIFT_RECEIVED = 'gift_received';
    const TRIAL_STARTED = 'trial_started';
    const TRIAL_ENDED = 'trial_ended';
    const GIFT_COUNT_PURCHASE = 'gift_count_purchase';
    const PAYMENT_FAILED = 'payment_failed';
    const REFUND = 'refund';
    const GIFT_REJECTED = 'gift_rejected';
    const GIFT_ACCEPTED = 'gift_accepted';
    const GIFT_CANCELLED = 'gift_cancelled';
    

    // Group all types in an array
    const ALL_TYPES = [
        self::PURCHASE,
        self::RENEWAL,
        self::CANCELLATION,
        self::GIFT_SENT,
        self::GIFT_RECEIVED,
        self::TRIAL_STARTED,
        self::TRIAL_ENDED,
        self::GIFT_COUNT_PURCHASE,
        self::PAYMENT_FAILED,
        self::REFUND,
        self::GIFT_REJECTED,
        self::GIFT_ACCEPTED,
        self::GIFT_CANCELLED,
    ];

    /**
     * Get all activities of this type.
     */
    public function activities()
    {
        return $this->hasMany(PaywallActivity::class, 'activity_type_id');
    }

   
}