<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TagQuestion extends Model
{
    use HasFactory;
    protected $fillable = ['tag_id', 'question_id'];

    public function tag()
    {
        return $this->belongsTo(Tag::class);
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }
}
