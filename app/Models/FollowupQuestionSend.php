<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FollowupQuestionSend extends Model
{
    use HasFactory;
    protected $fillable = [
        'question_send_id',
        'question_id',
    ];

    public function questionSend()
    {
        return $this->belongsTo(QuestionSend::class);
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }
}
