<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Video extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = ['owner_user_id', 'mux_id', 'status', 'video_file','transcript_file', 'duration', 'question_id', 'trashed_at'];

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_user_id');
    }

    public function videoComments()
    {
        return $this->hasMany(VideoComment::class);
    }

    public function videoViews()
    {
        return $this->hasMany(VideoView::class);
    }

    public function questionSend()
    {
        return $this->hasone(QuestionSend::class);
    }
    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function getLikeCount()
    {
        return $this->videoViews()->whereNotNull('liked_at')->count();
    }

    public function getLikedAtByAuthenticatedUser()
    {
        $videoView = $this->videoViews()
            ->where('viewer_user_id', Auth::user()->id)
            ->whereNotNull('liked_at')
            ->first();

        return $videoView ? $videoView->liked_at : null;
    }

    public function followupQuestions()
    {
        return $this->hasMany(VideoFollowupQuestion::class)->with('followupQuestion');
    }

}
