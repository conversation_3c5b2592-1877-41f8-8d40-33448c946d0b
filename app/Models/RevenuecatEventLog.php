<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RevenuecatEventLog extends Model
{
    protected $fillable = [
        'type',
        'external_id', // use RevenueCat event.id here
        'status',
        'app_user_id',
        'product_id',
        'store',
        'price',
        'period_type',
        'data', // raw JSON
    ];

    protected $casts = [
        'data' => 'array',
        'price' => 'float',
        'app_user_id' => 'integer',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSED = 'processed';
    const STATUS_ERROR = 'error';

    const ALL_STATUSES = [
        self::STATUS_PENDING,
        self::STATUS_PROCESSED,
        self::STATUS_ERROR,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'app_user_id');
    }
} 