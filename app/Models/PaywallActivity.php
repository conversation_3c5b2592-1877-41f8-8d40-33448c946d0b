<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaywallActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'activity_type_id',
        'membership_gift_id',
        'price',
        'gift_count_purchased'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'gift_count_purchased' => 'integer',
    ];

    /**
     * Get the user who performed the activity.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the activity type.
     */
    public function activityType()
    {
        return $this->belongsTo(PaywallActivityType::class, 'activity_type_id');
    }

    /**
     * Get the related membership gift.
     */
    public function membershipGift()
    {
        return $this->belongsTo(MembershipGift::class);
    }
    
}