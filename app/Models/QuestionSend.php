<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionSend extends Model
{
    use HasFactory;
    protected $fillable = [
        'question_id',
        'relationship_id',
        'video_id',
        'sent_at', 'sent_from_user_id', 'sent_to_user_id', 'invitation_id',
        'trashed_at',
    ];

    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function sentFromUser()
    {
        return $this->belongsTo(User::class, 'sent_from_user_id');
    }

    public function sentToUser()
    {
        return $this->belongsTo(User::class, 'sent_to_user_id');
    }

    public function relationship()
    {
        return $this->belongsTo(Relationship::class);
    }

    public function video()
    {
        return $this->belongsTo(Video::class);
    }

    public function questionActivity()
    {
        return $this->hasmany(QuestionActivity::class);
    }

    public function followupQuestionSend()
    {
        return $this->hasmany(FollowupQuestionSend::class);
    }
}
