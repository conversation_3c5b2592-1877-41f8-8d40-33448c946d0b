<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;
    protected $fillable = ['question_text', 'source_question_id', 'creator__user_id', 'question_type', 'to_user_id'];

    const PRIVATE_QUESTION_TYPE = 'private';
    const PUBLIC_QUESTION_TYPE = 'public';
    const CUSTOM_QUESTION_TYPE = 'custom';

    const ALL_QUESTION_TYPES = [
        self::PRIVATE_QUESTION_TYPE,
        self::PUBLIC_QUESTION_TYPE,
        self::CUSTOM_QUESTION_TYPE,
    ];

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'creator__user_id');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'tag_questions')->withTimestamps();
    }

    public function questionSends()
    {
        return $this->hasMany(QuestionSend::class);
    }
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function questionActivities()
    {
        return $this->hasMany(QuestionActivity::class);
    }
    public function followupQuestionSend()
    {
        return $this->hasmany(FollowupQuestionSend::class);
    }

}
