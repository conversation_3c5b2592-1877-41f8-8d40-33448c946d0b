<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OfferCodeUser extends Model
{
    use HasFactory;

    protected $table = 'offer_code_users';

    protected $fillable = [
        'user_id',
        'offer_code_id',
        'redeemed_at',
    ];

    protected $casts = [
        'redeemed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function offerCode(): BelongsTo
    {
        return $this->belongsTo(OfferCode::class);
    }
}
