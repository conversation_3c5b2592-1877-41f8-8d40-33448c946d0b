<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoFollowupQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'video_id',
        'followup_question_id',
    ];

    public function video()
    {
        return $this->belongsTo(Video::class);
    }

    public function followupQuestion()
    {
        return $this->belongsTo(Question::class, 'followup_question_id');
    }
} 