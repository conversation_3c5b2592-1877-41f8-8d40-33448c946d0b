<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;
    protected $fillable = [
        'courier_id',
        'from_user_id',
        'to_user_id',
        'cta_type',
        'question_id',
        'video_id',
        'seen_at',
    ];

    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function video()
    {
        return $this->belongsTo(Video::class);
    }

}
