<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Relationship extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'from_user_id',
        'to_user_id',
        'reciprocal_relationship_id',
        'to_invitation_id',
        'is_hidden',
        'name_as_from',
        'name_as_outsider',
    ];

    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    public function reciprocalRelationship()
    {
        return $this->belongsTo(Relationship::class, 'reciprocal_relationship_id');
    }
    public function invitation()
    {
        return $this->belongsTo(Invitation::class, 'to_invitation_id');
    }

    public function questionSends()
    {
        return $this->hasMany(QuestionSend::class);
    }
    public function connectionRequest()
    {
        return $this->hasOne(ConnectionRequest::class);
    }
}
