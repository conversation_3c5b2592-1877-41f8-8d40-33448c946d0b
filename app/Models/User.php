<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Model
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'phone_number',
        'sub',
        'full_name',
        'birthday',
        'profile_pic',
        'registration_status',
        'is_admin',
    ];

    const REGISTRATION_STATUS_UNCONFIRMED = 'unconfirmed';
    const REGISTRATION_STATUS_VERIFIED = 'verified';
    const TEMPORARY_PASSWORD = '4^tyLy8jL';

    public function questions()
    {
        return $this->hasMany(Question::class, 'creator__user_id');
    }

    public function tags()
    {
        return $this->hasMany(Tag::class, 'creator__user_id');
    }

    public function videos()
    {
        return $this->hasMany(Video::class, 'owner_user_id');
    }

    public function questionSends()
    {
        return $this->hasMany(QuestionSend::class, 'sent_from_user_id');
    }

    public function fromUserRelationships()
    {
        return $this->hasMany(Relationship::class, 'from_user_id');
    }

    public function toUserRelationships()
    {
        return $this->hasMany(Relationship::class, 'to_user_id');
    }

    public function relationships()
    {
        return $this->hasMany(Relationship::class, 'from_user_id');
    }

    public function invitations()
    {
        return $this->hasMany(Invitation::class, 'from_user_id');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'from_user_id');
    }
    public function isAdmin()
    {
        return $this->is_admin;
    }
}
