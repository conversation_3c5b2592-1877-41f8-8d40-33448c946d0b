<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'phone_number',
        'sub',
        'full_name',
        'birthday',
        'profile_pic',
        'registration_status',
        'is_admin',
        'membership_id'
    ];

    const REGISTRATION_STATUS_UNCONFIRMED = 'unconfirmed';
    const REGISTRATION_STATUS_VERIFIED = 'verified';
    const TEMPORARY_PASSWORD = '4^tyLy8jL';

    public function questions()
    {
        return $this->hasMany(Question::class, 'creator__user_id');
    }

    public function tags()
    {
        return $this->hasMany(Tag::class, 'creator__user_id');
    }

    public function videos()
    {
        return $this->hasMany(Video::class, 'owner_user_id');
    }

    public function questionSends()
    {
        return $this->hasMany(QuestionSend::class, 'sent_from_user_id');
    }

    public function fromUserRelationships()
    {
        return $this->hasMany(Relationship::class, 'from_user_id');
    }

    public function toUserRelationships()
    {
        return $this->hasMany(Relationship::class, 'to_user_id');
    }

    public function relationships()
    {
        return $this->hasMany(Relationship::class, 'from_user_id');
    }

    public function invitations()
    {
        return $this->hasMany(Invitation::class, 'from_user_id');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'from_user_id');
    }

    public function isAdmin()
    {
        return $this->is_admin;
    }

    public function membership(): BelongsTo
    {
        return $this->belongsTo(Membership::class);
    }

    public function sentGifts(): HasMany
    {
        return $this->hasMany(MembershipGift::class, 'from_user_id');
    }

    public function receivedGifts(): HasMany
    {
        return $this->hasMany(MembershipGift::class, 'to_user_id');
    }

    /**
     * Get all connected users for this user.
     * Returns a collection of User models who have approved connections with this user.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getConnectedUsers()
    {
        $relationships = $this->fromUserRelationships->merge($this->toUserRelationships);

        $filteredRelationships = $relationships->filter(function ($relationship) {
            return $relationship->connectionRequest &&
                $relationship->connectionRequest->status &&
                !in_array(
                    $relationship->connectionRequest->status->title,
                    [
                        ConnectionRequestStatus::STATUS_DISCARDED,
                        ConnectionRequestStatus::STATUS_INVITED,
                        ConnectionRequestStatus::STATUS_PENDING,
                        ConnectionRequestStatus::STATUS_RESENT,
                    ]
                );
        });

        return $filteredRelationships->map(function ($relationship) {
            return ($relationship->from_user_id === $this->id) 
                ? $relationship->toUser 
                : $relationship->fromUser;
        })->filter();
    }
}
