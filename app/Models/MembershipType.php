<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MembershipType extends Model
{
    protected $fillable = [
        'name',
        'days_to_expire'
    ];

    // Define membership type constants
    const TRIAL_MEMBERSHIP = 'trial_membership';
    const FULL_MEMBERSHIP = 'full_membership';
    const GIFT_MEMBERSHIP = 'gift_membership';

    // Define all available membership types
    const ALL_TYPES = [
        self::TRIAL_MEMBERSHIP,
        self::FULL_MEMBERSHIP,
        self::GIFT_MEMBERSHIP,
    ];

    public function memberships(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Membership::class);
    }
} 
