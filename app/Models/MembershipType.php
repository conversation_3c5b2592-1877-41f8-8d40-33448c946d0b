<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MembershipType extends Model
{
    protected $fillable = [
        'name',
        'days_to_expire'
    ];

    // Define membership type constants
    const TRIAL_MEMBERSHIP = 'trial_membership';
    const FULL_MEMBERSHIP = 'full_membership';
    const GIFT_MEMBERSHIP = 'gift_membership';
    const PROMOTIONAL_MEMBERSHIP = 'promotional_membership';
    const FRIENDS_FAMILY_MEMBERSHIP = 'friends_family_membership';
    const TESTING_MEMBERSHIP = 'testing_membership';

    // Define all available membership types
    const ALL_TYPES = [
        self::TRIAL_MEMBERSHIP,
        self::FULL_MEMBERSHIP,
        self::GIFT_MEMBERSHIP,
    ];

    public function memberships(): HasMany
    {
        return $this->hasMany(Membership::class);
    }
} 
