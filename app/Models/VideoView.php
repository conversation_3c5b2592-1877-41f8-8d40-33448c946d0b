<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoView extends Model
{
    use HasFactory;
    protected $fillable = ['viewer_user_id', 'video_id', 'viewed_at', 'liked_at'];

    public function viewer()
    {
        return $this->belongsTo(User::class, 'viewer_user_id');
    }

    public function video()
    {
        return $this->belongsTo(Video::class);
    }
}
