<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VideoRecoveryWithPhone extends Model
{
    
    use HasFactory;

    protected $fillable = [
        'phone_number',
        'video_id',
        'recovered_at'
    ];

    protected $casts = [
        'recovered_at' => 'datetime'
    ];

    public function video(): BelongsTo
    {
        return $this->belongsTo(Video::class);
    }
} 