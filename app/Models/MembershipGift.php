<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MembershipGift extends Model
{
    protected $fillable = [
        'to_user_id',
        'from_user_id',
        'invitation_id',
        'sent_at',
        'accepted_at',
        'rejected_at',
        'expires_at'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'accepted_at' => 'datetime',
        'rejected_at' => 'datetime',
        'expires_at' => 'datetime'
    ];

    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function invitation(): BelongsTo
    {
        return $this->belongsTo(Invitation::class, 'invitation_id');
    }

    /**
     * Get the recipient of the gift (either user or invitation)
     */
    public function recipient()
    {
        return $this->toUser ?? $this->invitation;
    }
}
