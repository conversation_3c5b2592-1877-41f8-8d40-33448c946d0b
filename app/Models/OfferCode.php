<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OfferCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'is_auto_renew',
        'title',
        'regular_rate',
        'revenue_cat_product_id',
        'duration_num',
        'duration_units',
        'expires_at',
    ];

    protected $casts = [
        'is_auto_renew' => 'boolean',
        'regular_rate' => 'decimal:2',
        'expires_at' => 'datetime',
        'duration_num' => 'integer',
    ];

    // Duration unit constants
    const DURATION_UNIT_WEEK = 'week';
    const DURATION_UNIT_MONTH = 'month';
    const DURATION_UNIT_YEAR = 'year';

    // All available duration units
    const DURATION_UNITS = [
        self::DURATION_UNIT_WEEK,
        self::DURATION_UNIT_MONTH,
        self::DURATION_UNIT_YEAR,
    ];

    /**
     * Get the users who have used this offer code.
     */
    public function usages(): Has<PERSON>any
    {
        return $this->hasMany(OfferCodeUsage::class);
    }

    /**
     * Check if the offer code is expired.
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Calculate the end date based on duration.
     *
     * @param \DateTime|string $startDate
     * @return \DateTime
     */
    public function calculateEndDate($startDate = null)
    {
        $startDate = $startDate ? now()->parse($startDate) : now();

        switch ($this->duration_units) {
            case self::DURATION_UNIT_WEEK:
                return $startDate->addWeeks($this->duration_num);
            case self::DURATION_UNIT_MONTH:
                return $startDate->addMonths($this->duration_num);
            case self::DURATION_UNIT_YEAR:
                return $startDate->addYears($this->duration_num);
            default:
                return $startDate->addDays($this->duration_num * 7); // Default to weeks
        }
    }
}
