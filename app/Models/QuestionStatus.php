<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionStatus extends Model
{
    use HasFactory;
    protected $fillable = ['title', 'description'];

    // Define question status constants
    const STATUS_SENT = "sent";
    const STATUS_RECEIVED = "received";
    const STATUS_ANSWERED = "answered";
    const STATUS_RESENT = "resent";
    const STATUS_UNLOCKED = "unlocked";
    const STATUS_CREATED = "created";

    // Define all available statuses
    const ALL_STATUSES = [
        self::STATUS_SENT,
        self::STATUS_RECEIVED,
        self::STATUS_ANSWERED,
        self::STATUS_RESENT,
        self::STATUS_UNLOCKED,
        self::STATUS_CREATED,
    ];

    public function questionActivity()
    {
        return $this->hasOne(QuestionActivity::class);
    }

}
