<?php

namespace App\Listeners;

use App\Events\FollowupQuestionSendCreated;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class FollowupQuestionSentListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $questionSendRepository;

    public function __construct(NotificationService $notificationService, NotificationRepository $notificationRepository, QuestionSendRepository $questionSendRepository)
    {
        $this->notificationService = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->questionSendRepository = $questionSendRepository;

    }
    public function handle(FollowupQuestionSendCreated $event)
    {
        $questionSendId = $event->questionSendId;
        $followupQuestionId = $event->followupQuestionId;
        $questionSend = $this->questionSendRepository->findOne($questionSendId);

        // Send notification to sent_from_user_id
        $this->sendNotification($questionSend->sent_from_user_id, $questionSend, $followupQuestionId);

        // Send notification to sent_to_user_id
        $this->sendNotification($questionSend->sent_to_user_id, $questionSend, $followupQuestionId);
    }

    protected function sendNotification($userId, $questionSend, $followupQuestionId)
    {
        // Prepare dynamic data
        $dynamicData = [
            'from_user_id' => $questionSend->sent_from_user_id,
            'to_user_id' => $userId,
            'question_id' => $questionSend->question_id,
            'video_id' => $questionSend->video_id,
            'followup_question_id' => $followupQuestionId,
        ];

        // Create notification data using the service method
        $notificationData = $this->notificationService->createNotificationData('followup_question_send', $dynamicData);

        // Send the notification
        $courierId = $this->notificationService->sendNotification($notificationData);

        // Save notification details in the repository
        $this->notificationRepository->save([
            'courier_id' => $courierId,
            'from_user_id' => $questionSend->sent_from_user_id,
            'to_user_id' => $userId,
            'cta_type' => 'followup_question_send',
            'question_id' => $questionSend->question_id,
            'video_id' => $questionSend->video_id,
        ]);
    }

}
