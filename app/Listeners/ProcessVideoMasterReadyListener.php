<?php

namespace App\Listeners;

use App\Events\TranscriptionCompleted;
use App\Events\VideoMasterReadyForProcessing;
use App\Events\VideoUploaded;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\TranscribeService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessVideoMasterReadyListener implements ShouldQueue
{
    protected $transcribeService;
    protected $videoRepository;

    /**
     * Create the event listener.
     */
    public function __construct(TranscribeService $transcribeService, VideoRepository $videoRepository)
    {
        $this->transcribeService = $transcribeService;
        $this->videoRepository = $videoRepository;
    }

    /**
     * Handle the event.
     */
    public function handle(VideoMasterReadyForProcessing $event): void
    {
        $video = $event->video;
        $masterUrl = $event->masterUrl;

        // Download video from master URL
        $client = new \GuzzleHttp\Client();
        $response = $client->get($masterUrl);
        $videoContents = $response->getBody()->getContents();

        $filename = 'videos/' . uniqid() . '.mp4';
        Storage::disk('s3')->put($filename, $videoContents, 'public');

        $transcription = $this->transcribeService->transcribe($masterUrl);
        $transcriptFile = $this->transcribeService->saveTranscription($transcription);

        $this->videoRepository->update($video, [
            'video_file' => $filename,
            'transcript_file' => $transcriptFile,
        ]);

        $transcriptionText = json_encode($transcription);
        event(new TranscriptionCompleted($video, $transcriptionText));
        event(new VideoUploaded($video));
        Log::info('Processed video.asset.master.ready for muxId: ' . $video->mux_id);
    }
}
