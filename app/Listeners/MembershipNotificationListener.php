<?php

namespace App\Listeners;

use App\Events\MembershipNotification;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

/**
 * MembershipNotificationListener handles various membership-related notifications.
 */
class MembershipNotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $userRepository;

    /**
     * Create a new listener instance.
     *
     * @param NotificationService $notificationService
     * @param NotificationRepository $notificationRepository
     * @param UserRepository $userRepository
     */
    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        UserRepository $userRepository
    ) {
        $this->notificationService = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Handle the membership notification event.
     *
     * @param MembershipNotification $event
     * @return void
     */
    public function handle(MembershipNotification $event)
    {
        $notificationType = $event->getNotificationType();
        $eventTime = now()->toISOString();

        switch ($notificationType) {
            case 'membership_activated':
                $this->handleMembershipActivated($event, $eventTime);
                break;
            case 'gift_membership_sent':
                $this->handleGiftMembershipSent($event, $eventTime);
                break;
            case 'membership_downgraded':
                $this->handleMembershipDowngraded($event, $eventTime);
                break;
            case 'membership_expiring_soon':
                $this->handleMembershipExpiringSoon($event, $eventTime);
                break;
            case 'free_trial_expiring_soon':
                $this->handleFreeTrialExpiringSoon($event, $eventTime);
                break;
            case 'gift_membership_rejected':
                $this->handleGiftMembershipRejected($event, $eventTime);
                break;
            case 'gift_membership_accepted':
                $this->handleGiftMembershipAccepted($event, $eventTime);
                break;
        }
    }

    /**
     * Handles sending a notification when a membership is activated.
     *
     * @param MembershipNotification $event
     * @param string $eventTime
     * @return void
     */
    protected function handleMembershipActivated($event, $eventTime)
    {
        $user = Auth::user();
        $notificationTitle = "Capsle Stories";
        $notificationText = "Congratulations on becoming a member of Capsle Stories! Your membership comes with 3 free gift memberships. Tap here to give them to friends and family.";

        $this->sendNotification(
            $user->id,
            null,
            $eventTime,
            'membership_activated',
            $notificationTitle,
            $notificationText
        );
    }

    /**
     * Handles sending a notification when a membership is downgraded.
     *
     * @param MembershipNotification $event
     * @param string $eventTime
     * @return void
     */
    protected function handleMembershipDowngraded($event, $eventTime)
    {
        $membership = $event->getMembership();
        $user = $membership->user;
        if ($user instanceof Collection) {
            $user = $user->first();
        }
        $notificationTitle = "Capsle Stories";
        $notificationText = "Your account has been downgraded. Sorry to see you got! Download your recordings before it's too late or upgrade your account to continue storing them on Capsle Stories.";

        $this->sendNotification($user->id, null, $eventTime, 'membership_downgraded', $notificationTitle, $notificationText);
    }

    /**
     * Handles sending a notification when a membership is about to expire.
     *
     * @param MembershipNotification $event
     * @param string $eventTime
     * @return void
     */
    protected function handleMembershipExpiringSoon($event, $eventTime)
    {
        $membership = $event->getMembership();
        $user = $membership->user;
        if ($user instanceof Collection) {
            $user = $user->first();
        }
        $cost = $event->getMembershipCost();
        $notificationTitle = "Capsle Stories";
        $notificationText = "Your membership ends in 2 days, then you'll be charged {$cost}/year to keep your membership benefits. To make changes, go to your phone's subscription settings.";

        $this->sendNotification($user->id, null, $eventTime, 'membership_expiring_soon', $notificationTitle, $notificationText);
    }

    /**
     * Handles sending a notification when a free trial is about to expire.
     *
     * @param MembershipNotification $event
     * @param string $eventTime
     * @return void
     */
    protected function handleFreeTrialExpiringSoon($event, $eventTime)
    {
        $membership = $event->getMembership();
        $user = $membership->user;
        if ($user instanceof Collection) {
            $user = $user->first();
        }
        $cost = $event->getMembershipCost();
        $notificationTitle = "Capsle Stories";
        $notificationText = "Your free trial ends in 2 days, then you'll be charged {$cost}/year to keep your membership benefits. To make changes, go to your phone's subscription settings.";

        $this->sendNotification($user->id, null, $eventTime, 'free_trial_expiring_soon', $notificationTitle, $notificationText);
    }

    /**
     * Handles sending a notification to the gift sender when their gift membership is rejected.
     *
     * @param MembershipNotification $event The event containing gift membership details
     * @param string $eventTime ISO formatted timestamp of the event
     * @return void
     */
    protected function handleGiftMembershipRejected($event, $eventTime)
    {
        $giftMembership = $event->getGiftMembership();
        $recipient = $this->userRepository->findOne($giftMembership->to_user_id);
        $fullName = trim("{$recipient->first_name} {$recipient->middle_name} {$recipient->last_name}");

        $notificationTitle = "{$fullName}";
        $notificationText = "{$fullName} turned down your Gift! Tap here to send it to someone else.";

        $this->sendNotification($giftMembership->from_user_id, $giftMembership->to_user_id, $eventTime, 'gift_membership_rejected', $notificationTitle, $notificationText);
    }

    /**
     * Handles sending a notification to the gift sender when their gift membership is accepted.
     *
     * @param MembershipNotification $event The event containing gift membership details
     * @param string $eventTime ISO formatted timestamp of the event
     * @return void
     */
    protected function handleGiftMembershipAccepted($event, $eventTime)
    {
        $giftMembership = $event->getGiftMembership();
        $recipient = $this->userRepository->findOne($giftMembership->to_user_id);
        $fullName = trim("{$recipient->first_name} {$recipient->middle_name} {$recipient->last_name}");

        $notificationTitle = "{$fullName}";
        $notificationText = "{$fullName} accepted your Gift! Tap here to send them a question.";

        $this->sendNotification($giftMembership->from_user_id, $giftMembership->to_user_id, $eventTime, 'gift_membership_accepted', $notificationTitle, $notificationText);
    }

    /**
     * Handles sending a notification when a gift membership is sent.
     *
     * @param MembershipNotification $event
     * @param string $eventTime
     * @return void
     */
    protected function handleGiftMembershipSent($event, $eventTime)
    {
        $giftMembership = $event->getGiftMembership();
        $sender = $this->userRepository->findOne($giftMembership->from_user_id);
        $fullName = trim("{$sender->first_name} {$sender->middle_name} {$sender->last_name}");

        $notificationTitle = "{$fullName}";
        $notificationText = "{$fullName} sent you a Gift! Tap here to claim it.";

        $this->sendNotification($giftMembership->to_user_id, $giftMembership->from_user_id, $eventTime, 'gift_membership_accepted', $notificationTitle, $notificationText);
    }

    /**
     * Sends a notification to a user with the specified parameters.
     *
     * @param int $toUserId The ID of the user receiving the notification
     * @param int|null $fromUserId The ID of the user sending the notification (null for system notifications)
     * @param string $eventTime ISO formatted timestamp of the event
     * @param string $eventType Type of the notification event
     * @param string $notificationTitle Title for the push notification
     * @param string $notificationText Content of the notification
     * @return void
     */
    protected function sendNotification($toUserId, $fromUserId, $eventTime, $eventType, $notificationTitle, $notificationText)
    {
        // Initialize fromUser data with default values
        $fromUserData = [
            'first_name' => '',
            'from_user_profile_pic' => null
        ];

        // Only fetch user data if fromUserId is provided
        if ($fromUserId) {
            $fromUser = $this->userRepository->findOne($fromUserId);
            $fromUserData = [
                'first_name' => $fromUser->first_name ?? '',
                'from_user_profile_pic' => $fromUser->profile_pic ?
                    Storage::temporaryUrl($fromUser->profile_pic, now()->addDays(7)) : null
            ];
        }

        $dynamicData = json_encode([
            'eventTime' => $eventTime,
            'eventType' => $eventType,
            'notificationData' => [
                'userId' => $toUserId,
                'from_user_id' => $fromUserId,
                'first_name' => $fromUserData['first_name'],
                'from_user_profile_pic' => $fromUserData['from_user_profile_pic'],
                'title' => $notificationText,
                'push_title' => $notificationTitle,
                'push_text' => $notificationText,
            ],
        ]);

        $notificationData = $this->notificationService->createNotificationData($eventType, $notificationText, $dynamicData);
        $courierId = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox($eventType, $notificationText, $dynamicData);
        $courierIdForInbox = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id' => $courierIdForInbox,
            'from_user_id' => $fromUserId ?? $toUserId,
            'to_user_id' => $toUserId,
            'cta_type' => "{$eventType}_type",
        ]);
    }
}
