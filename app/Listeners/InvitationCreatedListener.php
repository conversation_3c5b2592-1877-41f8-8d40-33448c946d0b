<?php

namespace App\Listeners;

use App\Events\InvitationCreated;
use App\Models\Relationship;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;

class InvitationCreatedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */

    protected $relationshipRepository;
    protected $notificationService;
    protected $notificationRepository;

    public function __construct(RelationshipRepository $relationshipRepository, NotificationService $notificationService, NotificationRepository $notificationRepository)
    {
        $this->relationshipRepository = $relationshipRepository;
        $this->notificationService = $notificationService;
        $this->notificationRepository = $notificationRepository;
    }

    /**
     * Handle the event.
     */

    public function handle(InvitationCreated $event)
    {
        $invitation = $event->invitation;
        $relationshipData = $event->relationshipData;

        // Determine the type of invitation (phone or app user) and handle accordingly
        if ($invitation->to_user_id) {

            $this->handleAppUserInvitation($relationshipData);
        } else {
            // Phone number invitation
            $this->handlePhoneNumberInvitation($relationshipData);
        }
    }

    protected function handleAppUserInvitation($relationshipData)
    {
        $relationshipData = $this->relationshipRepository->save($relationshipData);
    }

    protected function handlePhoneNumberInvitation($relationshipData)
    {
        // Handle phone number invitation logic here
        $this->relationshipRepository->save($relationshipData);

    }
}
