<?php

namespace App\Listeners;

use App\Events\NotificationCreated;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class NotificationCreatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $userRepository;

    /**
     * Create the event listener.
     */
    public function __construct(NotificationService $notificationService, NotificationRepository $notificationRepository, UserRepository $userRepository)
    {
        $this->notificationService = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->userRepository = $userRepository;
    }
    /**
     * Handle the event.
     */
    public function handle(NotificationCreated $event)
    {
        $notificationData = $event->notificationData;

        switch ($event->notificationType) {
            case "admin-notification":
                $this->handleAdminNotification($notificationData);
                break;
        }
    }

    /**
     * Handle admin notification.
     */
    protected function handleAdminNotification($notificationData)
    {
        $questionId = $notificationData->question_id;
        $videoId = $notificationData->video_id;
        $fromUserId = $notificationData->sent_from_user_id;

        $admins = $this->userRepository->findBy(['is_admin' => 1]);

        foreach ($admins as $admin) {
            $dynamicData = [
                'to_user_id' => $admin->id,
                'question_id' => $questionId,
                'video_id' => $videoId,
            ];

            $eventType = 'admin-notification';
            $notification = $this->notificationService->createNotificationData($eventType, $dynamicData);

            $courierId = $this->notificationService->sendNotification($notification);
            $this->notificationRepository->save([
                'courier_id' => $courierId,
                'from_user_id' => $fromUserId,
                'to_user_id' => $admin->id,
                'cta_type' => $eventType,
                'question_id' => $questionId,
                'video_id' => $videoId,
            ]);
        }
    }
}
