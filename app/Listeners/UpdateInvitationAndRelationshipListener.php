<?php
namespace App\Listeners;

use App\Events\UpdateInvitationAndRelationship;
use App\Repositories\Contracts\ConnectionRequestRepository;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use App\Repositories\Contracts\InvitationRepository;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Support\Facades\Storage;

class UpdateInvitationAndRelationshipListener
{
    /**
     * Create the event listener.
     */
    protected $invitationRepository;
    protected $relationshipRepository;
    protected $connectionRequestRepository;
    protected $connectionRequestStatusRepository;
    protected $questionSendRepository;
    protected $notificationService;
    protected $notificationRepository;

    public function __construct(RelationshipRepository $relationshipRepository,
        InvitationRepository $invitationRepository,
        ConnectionRequestRepository $connectionRequestRepository,
        ConnectionRequestStatusRepository $connectionRequestStatusRepository,
        QuestionSendRepository $questionSendRepository,
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,

    ) {
        $this->invitationRepository              = $invitationRepository;
        $this->relationshipRepository            = $relationshipRepository;
        $this->connectionRequestStatusRepository = $connectionRequestStatusRepository;
        $this->connectionRequestRepository       = $connectionRequestRepository;
        $this->questionSendRepository            = $questionSendRepository;
        $this->notificationService               = $notificationService;
        $this->notificationRepository            = $notificationRepository;

    }

    /**
     * Handle the event.
     */
    public function handle(UpdateInvitationAndRelationship $event)
    {

        $invitations = $event->invitations;
        $user        = $event->user;

        foreach ($invitations as $invitation) {
            // Update the invitation
            $this->updateInvitation($invitation, $user);

            $relationship = $this->relationshipRepository->findOneBy(['to_invitation_id' => $invitation->id]);
            if ($relationship) {
                $this->updateRelationship($user, $relationship);
            }
            $this->sendInvitationAcceptedNotification($user, $invitation);
            // Update the question_sends entries
            $this->updateQuestionSends($user, $invitation);
        }

    }

    protected function sendInvitationAcceptedNotification($user, $invitation)
    {
        $fullName = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $title = "{$fullName} joined Capsle Stories! Tap here to send {$user->first_name} a question.";

        $dynamicData = json_encode([
            'eventTime'        => now()->toISOString(),
            'eventType'        => 'invitation_accepted',
            'notificationData' => [
                'userId'                => $invitation->from_user_id,
                'from_user_id'          => $user->id,
                'first_name'            => $user->first_name,
                'from_user_profile_pic' => $user->profile_pic ? Storage::temporaryUrl($user->profile_pic, now()->addDays(7)) : null,
                'invitation_id'         => $invitation->id,
                'title'                 => $title,
            ],
        ]);

        $notificationData = $this->notificationService->createNotificationData('invitation_accepted', $title, $dynamicData);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox('invitation_accepted', $title, $dynamicData);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $user->id,
            'to_user_id'   => $invitation->from_user_id,
            'cta_type'     => 'invitation_accepted',
        ]);
    }

    /**
     * Update the invitation.
     */
    protected function updateInvitation($invitation, $user)
    {
        $this->invitationRepository->update($invitation, ['to_user_id' => $user->id]);
    }

    /**
     * Update the relationship.
     */
    protected function updateRelationship($user, $relationship)
    {
        $relationship = $this->relationshipRepository->update($relationship, [
            'to_user_id' => $user->id,
        ]);
        $connectionRequestStatus = $this->connectionRequestStatusRepository->findOneBy([
            'title' => 'pending',
        ]);
        $connectionRequest = $this->connectionRequestRepository->findOneBy([
            'relationship_id' => $relationship->id,
        ]);
        $connectionRequest = $this->connectionRequestRepository->update($connectionRequest, [
            'relationship_id' => $relationship->id,
            'status_id'       => $connectionRequestStatus->id,
        ]);
        // event(new ConnectionRequestNotification($connectionRequest));

    }

    /**
     * Update question_sends.
     */
    protected function updateQuestionSends($user, $invitation)
    {
        // Find all question_sends associated with the invitation
        $questionSends = $this->questionSendRepository->findBy(['invitation_id' => $invitation->id]);

        // Update each question_send with the new to_user_id
        foreach ($questionSends as $questionSend) {
            $this->questionSendRepository->update($questionSend, [
                'sent_to_user_id' => $user->id,
            ]);
        }
    }
}
