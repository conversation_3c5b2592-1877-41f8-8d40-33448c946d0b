<?php

namespace App\Listeners;

use App\Events\QuestionUnlocked;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Repositories\Contracts\TagRepository;
use App\Repositories\Contracts\UserRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Support\Facades\Storage;

class QuestionUnlockedNotificationListener
{
    protected $notificationService;
    protected $notificationRepository;
    protected $userRepository;
    protected $questionSendRepository;
    protected $tagRepository;

    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        UserRepository $userRepository,
        QuestionSendRepository $questionSendRepository,
        TagRepository $tagRepository
    ) {
        $this->notificationService    = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->userRepository         = $userRepository;
        $this->questionSendRepository = $questionSendRepository;
        $this->tagRepository          = $tagRepository;
    }

    public function handle(QuestionUnlocked $event)
    {
        $toUser       = $this->userRepository->findOne($event->toUserId);
        $tag          = $this->tagRepository->findOne($event->tagId);
        $questionSend = $this->questionSendRepository->findOne($event->questionSendId);
        $questionText = $event->question->question_text;
        $title       = $this->getTitle($questionSend, $tag , $event->question);
        $dynamicData = $this->prepareDynamicData($toUser, $questionSend, $event->question, $tag, $title);

        $notificationData = $this->notificationService->createNotificationData('question_unlocked', $title, $dynamicData);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox('question_unlocked', $title, $dynamicData);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $toUser->id,
            'to_user_id'   => $event->toUserId,
            'cta_type'     => 'question_unlocked_type',
            'question_id'  => $event->question->id,
        ]);
    }

    private function getTitle($questionSend, $tag, $question): string
    {
        if (is_null($questionSend->sentFromUser)) {

            return "You unlocked a new question: \"{$question->question_text}\". Tap here to record your response.";
        } else {
            $sentToUser = $questionSend->sentToUser;

            return "You unlocked a followup question: \"{$question->question_text}\". Tap here to send it to {$sentToUser->first_name}.";
        }
    }

    private function prepareDynamicData($toUser, $questionSend, $question, $tag, $title)
    {
        $baseData = [
            'userId'           => $toUser->id,
            'first_name'       => $toUser->first_name,
            'question_id'      => $question->id,
            'question_text'    => $question->question_text,
            'question_send_id' => $questionSend->id,
            'sent_from_user_id'  => $questionSend->sent_from_user_id ?? null,
            'sent_to_user_id'  => $questionSend->sent_to_user_id,
            'tag_title'        => $tag->title,
            'tag_id'           => $tag->id,
            'title'            => $title,
            'push_title'       => 'Capsle Stories',
            // 'push_text'        => $title,
        ];

        if (is_null($questionSend->sentFromUser)) {
            $baseData['profile_pic'] = $toUser->profile_pic
                ? Storage::temporaryUrl($toUser->profile_pic, now()->addDays(7))
                : null;
        } else {
            $sentFromUser            = $questionSend->sentFromUser;
            $baseData['profile_pic'] = $sentFromUser->profile_pic
                ? Storage::temporaryUrl($sentFromUser->profile_pic, now()->addDays(7))
                : null;
        }

        return json_encode([
            'eventTime'        => now()->toISOString(),
            'eventType'        => "question_unlocked",
            'notificationData' => $baseData,
        ]);
    }
}
