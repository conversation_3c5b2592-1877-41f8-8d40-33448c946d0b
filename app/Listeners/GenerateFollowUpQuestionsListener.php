<?php
namespace App\Listeners;

use App\Events\TranscriptionCompleted;
use App\Models\Question;
use App\Repositories\Contracts\QuestionRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Repositories\Contracts\TagQuestionRepository;
use App\Repositories\Contracts\VideoFollowupQuestionRepository;
use App\Services\Contracts\AIService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GenerateFollowUpQuestionsListener implements ShouldQueue
{
    protected $aiService;
    protected $questionRepository;
    protected $tagQuestionRepository;
    protected $questionSendRepository;
    protected $relationshipRepository;
    protected $videoFollowupQuestionRepository;

    public function __construct(
        AIService $aiService,
        QuestionRepository $questionRepository,
        TagQuestionRepository $tagQuestionRepository,
        QuestionSendRepository $questionSendRepository,
        RelationshipRepository $relationshipRepository,
        VideoFollowupQuestionRepository $videoFollowupQuestionRepository
    ) {
        $this->aiService                       = $aiService;
        $this->questionRepository              = $questionRepository;
        $this->tagQuestionRepository           = $tagQuestionRepository;
        $this->questionSendRepository          = $questionSendRepository;
        $this->relationshipRepository          = $relationshipRepository;
        $this->videoFollowupQuestionRepository = $videoFollowupQuestionRepository;
    }
    public function handle(TranscriptionCompleted $event)
    {
        $video         = $event->video;
        $transcription = $event->transcription;
        $question      = $video->question;
        $tag           = $question->tags[0];

        try {
            // Check existing followup questions
            if ($video->followupQuestions()->exists()) {
                Log::info('Video already has follow-up questions', [
                    'video_id'    => $video->id,
                    'question_id' => $question->id,
                ]);
                return;
            }

            // Get question send info
            $questionSend = $this->questionSendRepository->findOneBy(['video_id' => $video->id]);

            // Get past questions
            $pastQuestions = $this->getPastQuestions($video, $questionSend);

            // Get category
            $category = $tag->title ?? 'general';

            // Get context
            $context = $this->getPromptContext($questionSend, $video);

            // Generate questions
            $followUpQuestions = $this->aiService->generateFollowUpQuestions(
                $question->question_text,
                $transcription,
                $context,
                $pastQuestions,
                $category
            );

            // Save questions
            foreach ($followUpQuestions as $questionText) {
                $this->saveFollowUpQuestion($questionText, $tag, $video);
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate follow-up questions: ' . $e->getMessage(), [
                'video_id'    => $video->id,
                'question_id' => $question->id,
            ]);
        }
    }

    /**
     * Get the appropriate prompt context based on video ownership
     */
    protected function getPromptContext($questionSend, $video): string
    {
        // Self-uploaded video (no question send or null from_user_id)
        if (! $questionSend || ! $questionSend->from_user_id) {
            return 'themselves';
        }

        // Question sent from another user
        return $this->determineRelationshipName($questionSend, $video);
    }

    /**
     * Determine the relationship name based on the question send and video owner
     *
     * @param mixed $questionSend
     * @param mixed $video
     * @return string
     */
    protected function determineRelationshipName($questionSend, $video): string
    {
        if (! $questionSend->from_user_id) {
            return 'loved one';
        }

        $relationship = $this->relationshipRepository->findRelationship(
            $questionSend->from_user_id,
            $questionSend->to_user_id
        );

        if (! $relationship) {
            return 'loved one';
        }

        return $video->owner_user_id === $relationship->from_user_id
        ? $relationship->name_as_from
        : $relationship->name_as_outsider;
    }

    protected function getPastQuestions($video, $questionSend): array
    {
        // Get user info
        $user = $video->owner;

        // Get past videos with necessary relationships
        $pastVideos = $this->questionSendRepository->findBy([
            'sent_to_user_id' => $user->id,
            'video_id'        => 'notNull',
            'order_by'        => 'created_at',
            'order_direction' => 'asc',
            'include'         => 'question.tags,video',
            'eagerLoad'       => [
                'question.tags' => 'question.tags',
                'video'         => 'video',
            ],
        ]);

        // Format past questions
        $questions = [];
        foreach ($pastVideos as $pastVideo) {
            if ($pastVideo->video && $pastVideo->video->transcript_file) {
                $questions[] = [
                    'text'             => $pastVideo->question->question_text,
                    'category'         => $pastVideo->question->tags[0]->title ?? 'general',
                    'transcript'       => $pastVideo->video->transcript_file ?
                    json_decode(Storage::get($pastVideo->video->transcript_file), true)['transcription'] ?? null : null,
                    'relationship'     => $this->determineRelationshipName($pastVideo, $pastVideo->video),
                    'is_self_recorded' => ! $pastVideo->from_user_id,
                ];
            }
        }

        // Get relationship info if sent video
        $relationship = null;
        $senderName   = null;
        if ($questionSend && $questionSend->from_user_id) {
            $relationship = $this->relationshipRepository->findRelationship(
                $questionSend->from_user_id,
                $questionSend->to_user_id
            );
            $senderName = $questionSend->fromUser->first_name;
        }

        return [
            'user_name'    => $user->first_name,
            'relationship' => $relationship ? $relationship->name_as_outsider : 'loved one',
            'sender_name'  => $senderName,
            'questions'    => $questions,
        ];
    }

    protected function saveFollowUpQuestion($questionText, $tag, $video)
    {
        $followupQuestion = $this->questionRepository->save([
            'question_text' => $questionText,
            'question_type' => Question::PRIVATE_QUESTION_TYPE,
        ]);

        $this->tagQuestionRepository->save([
            'tag_id'      => $tag->id,
            'question_id' => $followupQuestion->id,
        ]);

        $this->videoFollowupQuestionRepository->save([
            'video_id'             => $video->id,
            'followup_question_id' => $followupQuestion->id,
        ]);
    }
}
