<?php

namespace App\Listeners;

use App\Events\NotificationSent;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SendSlackNotificationForUserNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected $userRepository;

    /**
     * Create the event listener.
     */
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        try {
            $user = $this->userRepository->findOne($event->userId);

            if (!$user) {
                Log::error('User not found for notification slack message', [
                    'user_id' => $event->userId
                ]);
                return;
            }

            $message = "🔔 New Notification Sent\n\n";
            $message .= "User: {$user->first_name} {$user->last_name}\n";
            $message .= "Phone: {$user->phone_number}\n";
            $message .= "Notification: {$event->notificationText}\n";

            Http::post(config('services.slack.notification_webhook_url'), [
                'text' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send notification to Slack', [
                'error' => $e->getMessage(),
                'user_id' => $event->userId
            ]);
        }
    }
} 