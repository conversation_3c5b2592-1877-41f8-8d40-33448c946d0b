<?php

namespace App\Listeners;

use App\Events\VideoUploaded;
use App\Repositories\Contracts\RelationshipRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;

class SendSlackNotificationForVideoUpload implements ShouldQueue
{

    use InteractsWithQueue;

    protected $relationshipRepository;

    /**
     * Create the event listener.
     */
    public function __construct(
        RelationshipRepository $relationshipRepository,
    ) {
        $this->relationshipRepository = $relationshipRepository;
    }
    public function handle(VideoUploaded $event)
    {
        $video = $event->video;
        $user = $video->owner;
        $questionSend = $video->questionSend;

        $isSelfRecorded = is_null($questionSend->sent_from_user_id);
        $message = "The name of the user: {$user->first_name} {$user->last_name}\n";
        $message .= "Phone number of user: {$user->phone_number}\n\n";
        // $message .= "Is this a self-recorded video? " . ($isSelfRecorded ? 'True' : 'False') . "\n\n";

        if (!$isSelfRecorded) {
            $sender = $questionSend->sentFromUser;
            $receiver = $questionSend->sentToUser;
            $relationship = $this->relationshipRepository->findOneBy(['from_user_id' => $sender->id, 'to_user_id' => $receiver->id]);

            // Replace if 'relationship_name' is stored elsewhere
            $message .= "Name of the sender: {$sender->first_name} {$sender->last_name}\n";
            $message .= "Phone number of the sender: {$sender->phone_number}\n\n";
            $message .= "{$sender->first_name} is {$receiver->first_name}'s {$relationship["name_as_from"]}\n";
            $message .= "{$receiver->first_name} is {$sender->first_name}'s {$relationship["name_as_outsider"]}\n";
        }

        // Send the message to Slack
        Http::post(env('SLACK_WEBHOOK_URL'), [
            'text' => $message,
        ]);
    }
}
