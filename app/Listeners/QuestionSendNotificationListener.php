<?php
namespace App\Listeners;

use App\Events\QuestionAnswered;
use App\Events\QuestionSent;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class QuestionSendNotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $videoRepository;
    protected $userRepository;

    /**
     * Create the event listener.
     */
    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        VideoRepository $videoRepository,
        UserRepository $userRepository

    ) {
        $this->notificationService    = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->videoRepository        = $videoRepository;
        $this->userRepository         = $userRepository;

    }
    /**
     * Handle the QuestionSent event.
     */
    public function handleQuestionSent(QuestionSent $event)
    {
        $this->sendNotification($event, 'question_sent');
    }

    /**
     * Handle the QuestionAnswered event.
     */
    public function handleQuestionAnswered(QuestionAnswered $event)
    {
        $this->sendNotification($event, 'question_answered');
    }

    /**
     * Send and save notification for a user.
     *
     * @param $event
     * @param string $eventType
     */
    protected function sendNotification($event, string $eventType)
    {
        $fromUser     = Auth::user();
        $fromUserName = $fromUser->first_name ?? 'Someone';
        $fullName     = trim("{$fromUser->first_name} {$fromUser->middle_name} {$fromUser->last_name}");
        $questionText = $event->questionSend->question->question_text;

        if ($eventType === "question_sent") {
            $toUserId   = $event->questionSend->sent_to_user_id;
            $inAppTitle = "{$fromUserName} asked you: \"{$questionText}\" Tap here to record your response.";
            $pushTitle  = $fullName;
            $pushText   = "asked you: \"{$questionText}\" Tap here to record your response.";
        } else {
            $toUserId   = $event->questionSend->sent_from_user_id;
            $inAppTitle = "{$fromUserName} answered your question: \"{$questionText}\" Tap here to view it.";
            $pushTitle  = $fullName;
            $pushText   = "answered your question: \"{$questionText}\" Tap here to view it.";
        }

        // Exit if toUserId is null
        if ($toUserId === null) {
            return;
        }

        $questionSend = $event->questionSend;
        $eventTime    = now()->toISOString();

        $dynamicData = json_encode([
            'eventTime'        => $eventTime,
            'eventType'        => $eventType,
            'notificationData' => [
                'userId'           => $toUserId,
                'from_user_id'     => $questionSend->sent_from_user_id,
                'to_user_id'       => $questionSend->sent_to_user_id,
                'first_name'       => $fromUser->first_name ?? '',
                'profile_pic'      => $fromUser->profile_pic ? Storage::temporaryUrl($fromUser->profile_pic, now()->addDays(7)) : null,
                'question_id'      => $questionSend->question_id,
                'question_send_id' => $questionSend->id,
                'video_id'         => $questionSend->video_id ?? null,
                'title'            => $inAppTitle,
                'push_title'       => $pushTitle,
                'push_text'        => $pushText,
            ],
        ]);

        $notificationData = $this->notificationService->createNotificationData($eventType, $pushTitle, $dynamicData);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox($eventType, $inAppTitle, $dynamicData);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $fromUser->id,
            'to_user_id'   => $toUserId,
            'cta_type'     => "{$eventType}_type",
            'question_id'  => $questionSend->question_id,
            'video_id'     => $questionSend->video_id ?? null,
        ]);
    }

}
