<?php
namespace App\Listeners;

use App\Events\ConnectionRequestNotification;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Repositories\Contracts\UserRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ConnectionRequestNotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $relationshipRepository;
    protected $userRepository;
    protected $connectionRequestStatusRepository;

    /**
     * Create the event listener.
     */
    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        RelationshipRepository $relationshipRepository,
        UserRepository $userRepository,
        ConnectionRequestStatusRepository $connectionRequestStatusRepository
    ) {
        $this->notificationService               = $notificationService;
        $this->notificationRepository            = $notificationRepository;
        $this->relationshipRepository            = $relationshipRepository;
        $this->userRepository                    = $userRepository;
        $this->connectionRequestStatusRepository = $connectionRequestStatusRepository;
    }

    /**
     * Handle the event.
     */
    public function handle(ConnectionRequestNotification $event)
    {
        $user = Auth::user();

        $connectionRequest       = $event->connectionRequest;
        $relationship            = $this->relationshipRepository->findOne($connectionRequest->relationship_id);
        $connectionRequestStatus = $this->connectionRequestStatusRepository->findOne($connectionRequest->status_id);

        if ($relationship) {
            $fromUser  = $this->userRepository->findOne($relationship->from_user_id);
            $toUser    = $this->userRepository->findOne($relationship->to_user_id);
            $eventTime = now()->toISOString();

            $statusTitle = strtolower($connectionRequestStatus['title']);

            // Handle the 'discarded' case where only the authenticated user receives the notification
            if ($statusTitle === 'discarded') {
                $eventType = 'connection_request_discarded';

                // Send notification only to the authenticated user
                $this->sendAndSaveNotification(
                    $user->id,
                    $user->id === $relationship->from_user_id ? $relationship->to_user_id : $relationship->from_user_id,
                    $user->id === $relationship->from_user_id ? $toUser->first_name : $fromUser->first_name,
                    $user->id === $relationship->from_user_id ? $toUser->profile_pic : $fromUser->profile_pic,
                    $eventType,
                    $eventTime,
                    $relationship->id
                );
            } else {

                // Determine event type dynamically for 'from_user'
                $eventTypeForSender = $this->determineEventType($connectionRequestStatus['title'], true);
                // Send notification to 'from_user'
                $this->sendAndSaveNotification(
                    $relationship->from_user_id,
                    $relationship->to_user_id,
                    $toUser->first_name ?? '',
                    $toUser->profile_pic ?? '',
                    $eventTypeForSender,
                    $eventTime,
                    $relationship->id
                );

                // Determine event type dynamically for 'to_user'
                $eventTypeForReceiver = $this->determineEventType($connectionRequestStatus['title'], false);
                // Send notification to 'to_user'
                $this->sendAndSaveNotification(
                    $relationship->to_user_id,
                    $relationship->from_user_id,
                    $fromUser->first_name ?? '',
                    $fromUser->profile_pic ?? '',
                    $eventTypeForReceiver,
                    $eventTime,
                    $relationship->id
                );
            }
        }
    }

    /**
     * Determine the event type based on the connection request status and the user role.
     *
     * @param string $statusTitle
     * @param bool $isSender
     * @return string
     */
    protected function determineEventType(string $statusTitle, bool $isInitiator): string
    {
        switch (strtolower($statusTitle)) {
            case 'pending':
                return $isInitiator ? 'connection_request_sent_pending' : 'connection_request_receive_pending';
            case 'approved':
                return $isInitiator ? 'connection_request_receive_approved' : 'connection_request_sent_approved';
            case 'discarded':
                return 'connection_request_discarded';
            default:
                return 'connection_request_unknown';
        }
    }

    /**
     * Send and save notification for a user.
     *
     * @param int $toUserId
     * @param int $fromUserId
     * @param string $fromUserName
     * @param string $fromUserProfilePic
     * @param string $eventType
     * @param string $eventTime
     * @param int $relationshipId
     */
    protected function sendAndSaveNotification(
        int $toUserId,
        int $fromUserId,
        ?string $fromUserName,
        ?string $fromUserProfilePic,
        string $eventType,
        string $eventTime,
        int $relationshipId
    ) {
        // Get user data first to ensure we have the name
        $fromUser     = $this->userRepository->findOne($fromUserId);
        $fromUserName = $fromUser->first_name ?? 'User'; // Fallback if first_name is null
        $fullName     = trim("{$fromUser->first_name} {$fromUser->middle_name} {$fromUser->last_name}");

        switch ($eventType) {
            case 'connection_request_receive_pending':
                $inAppTitle = $this->getNotificationTitle($eventType, $fullName);
                $pushTitle  = $fullName;
                $pushText   = "wants to connect with you on Capsle Stories. Tap here to view request.";
                break;

            case 'connection_request_receive_approved':

                $inAppTitle = $this->getNotificationTitle($eventType, $fromUserName);
                $pushTitle  = $fullName;
                $pushText   = "accepted your request to connect! Tap here to send them a question.";
                break;

            case 'connection_request_sent_approved':

                $inAppTitle = $this->getNotificationTitle($eventType, $fromUserName);
                $pushTitle  = "Capsle Stories";
                $pushText   = "You're connected with {$fullName}! Tap here to send them a question.";
                break;

            default:
                $inAppTitle = $this->getNotificationTitle($eventType, $fromUserName);
                $pushTitle  = null;
                $pushText   = null;
        }

        $dynamicData = [
            'eventTime'        => $eventTime,
            'eventType'        => $eventType,
            'notificationData' => [
                'userId'          => $toUserId,
                'from_user_id'    => $fromUserId,
                'to_user_id'      => $toUserId,
                'first_name'      => $fromUserName,
                'profile_pic'     => $fromUserProfilePic ? Storage::temporaryUrl($fromUserProfilePic, now()->addDays(7)) : null,
                'relationship_id' => $relationshipId,
                'title'           => $inAppTitle,
                'push_title'      => $pushTitle,
                'push_text'       => $pushText,
            ],
        ];

        $dynamicDataJson = json_encode($dynamicData);

        // Use push title for connection requests, otherwise use regular title
        $notificationTitle = $pushTitle ?? $inAppTitle;

        $notificationData = $this->notificationService->createNotificationData($eventType, $notificationTitle, $dynamicDataJson);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox($eventType, $inAppTitle, $dynamicDataJson);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $fromUserId,
            'to_user_id'   => $toUserId,
            'cta_type'     => $eventType,
        ]);
    }

    /**
     * Get notification title based on event type
     */
    private function getNotificationTitle(string $eventType, string $userName): string
    {
        switch ($eventType) {
            case 'connection_request_sent_pending':
                return "You requested to connect with {$userName}";
            case 'connection_request_receive_pending':
                // return "{$userName} sent you a request to connect.";
                return "{$userName} wants to connect with you. Tap here to view request.";
            case 'connection_request_receive_approved':
                // return "You're connected with {$userName}! Tap here to send them a question.";
                return "{$userName} accepted your request to connect! Tap here to send them a question.";
            case 'connection_request_sent_approved':
                // return "You and {$userName} are now connected! Send them a question.";
                return "You're connected with {$userName}! Tap here to send them a question.";
            case 'connection_request_discarded':
                return "You discarded {$userName} to connect. Tap them to send an updated request.";
            default:
                return "New connection notification";
        }
    }
}
