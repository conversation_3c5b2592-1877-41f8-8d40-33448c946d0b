<?php
namespace App\Listeners;

use App\Events\VideoCommented;
use App\Events\VideoLiked;
use App\Events\VideoViewed;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class VideoNotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $videoRepository;
    protected $userRepository;

    /**
     * Create the event listener.
     */
    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        VideoRepository $videoRepository,
        UserRepository $userRepository

    ) {
        $this->notificationService    = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->videoRepository        = $videoRepository;
        $this->userRepository         = $userRepository;

    }

    /**
     * Handle the VideoViewed event.
     */
    public function handleVideoViewed(VideoViewed $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} viewed your video.";
        $pushTitle  = "Capsle Stories";
        $pushText   = "{$fullName} viewed your video.";
        $this->sendNotification($event, 'video_viewed', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Handle the VideoLiked event.
     */
    public function handleVideoLiked(VideoLiked $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} liked your video.";
        $pushTitle  = "Capsle Stories";
        $pushText   = "{$fullName} liked your video.";
        $this->sendNotification($event, 'video_liked', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Handle the VideoCommented event.
     */
    public function handleVideoCommented(VideoCommented $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} commented: {$event->videoComment->comment_text}";
        $pushTitle  = $fullName;
        $pushText   = "commented: \"{$event->videoComment->comment_text}\"";
        $this->sendNotification($event, 'video_commented', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Send and save notification for a user.
     */
    protected function sendNotification($event, string $eventType, string $inAppTitle, string $pushTitle, string $pushText)
    {
        $user  = Auth::user();
        $video = $this->videoRepository->findOne($event->videoComment->video_id ?? $event->videoView->video_id);

        $eventTime = now()->toISOString();

        if ($video->owner_user_id === $user->id) {
            return;
        }

        $dynamicData = json_encode([
            'eventTime'        => $eventTime,
            'eventType'        => $eventType,
            'notificationData' => [
                'userId'                => $video->owner_user_id,
                'from_user_id'          => $user->id,
                'first_name'            => $user->first_name ?? '',
                'from_user_profile_pic' => $user->profile_pic ? Storage::temporaryUrl($user->profile_pic, now()->addDays(7)) : null,
                'video_id'              => $video->id,
                'video_comment_id'      => $event->videoComment?->id ?? null,
                'video_comment_text'    => $event->videoComment?->comment_text ?? null,
                'title'                 => $inAppTitle,
                'push_title'            => $pushTitle,
                'push_text'             => $pushText,
            ],
        ]);

        $notificationData = $this->notificationService->createNotificationData($eventType, $inAppTitle, $dynamicData);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox($eventType, $inAppTitle, $dynamicData);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $user->id,
            'to_user_id'   => $video->owner_user_id,
            'cta_type'     => "{$eventType}_type",
            'video_id'     => $video->id,
        ]);
    }
}
