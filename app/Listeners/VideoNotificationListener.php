<?php
namespace App\Listeners;

use App\Events\VideoCommented;
use App\Events\VideoLiked;
use App\Events\VideoViewed;
use App\Events\VideoUploaded;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use App\Repositories\Contracts\RelationshipRepository;

class VideoNotificationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;
    protected $notificationRepository;
    protected $videoRepository;
    protected $userRepository;
    protected $relationshipRepository;

    /**
     * Create the event listener.
     */
    public function __construct(
        NotificationService $notificationService,
        NotificationRepository $notificationRepository,
        VideoRepository $videoRepository,
        UserRepository $userRepository,
        RelationshipRepository $relationshipRepository
    ) {
        $this->notificationService    = $notificationService;
        $this->notificationRepository = $notificationRepository;
        $this->videoRepository        = $videoRepository;
        $this->userRepository         = $userRepository;
        $this->relationshipRepository = $relationshipRepository;
    }

    /**
     * Handle the VideoViewed event.
     */
    public function handleVideoViewed(VideoViewed $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} viewed your video.";
        $pushTitle  = "Capsle Stories";
        $pushText   = "{$fullName} viewed your video.";
        $this->sendNotification($event, 'video_viewed', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Handle the VideoLiked event.
     */
    public function handleVideoLiked(VideoLiked $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} liked your video.";
        $pushTitle  = "Capsle Stories";
        $pushText   = "{$fullName} liked your video.";
        $this->sendNotification($event, 'video_liked', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Handle the VideoCommented event.
     */
    public function handleVideoCommented(VideoCommented $event)
    {
        $user       = Auth::user();
        $fullName   = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
        $inAppTitle = "{$user->first_name} commented: {$event->videoComment->comment_text}";
        $pushTitle  = $fullName;
        $pushText   = "commented: \"{$event->videoComment->comment_text}\"";
        $this->sendNotification($event, 'video_commented', $inAppTitle, $pushTitle, $pushText);
    }

    /**
     * Handle notification when a user uploads a video
     */
    public function handleVideoUploaded(VideoUploaded $event)
    {
        $videoOwner = $this->userRepository->findOne($event->video->owner_user_id);
        if (!$videoOwner) {
            return;
        }

        $fullName = trim("{$videoOwner->first_name} {$videoOwner->middle_name} {$videoOwner->last_name}");
        $questionText = $event->video->question->question_text ?? '';

        $inAppTitle = "{$user->first_name} answered: \"{$questionText}\" Tap here to view it.";
        $pushTitle = $fullName;
        $pushText = "{$fullName} answered: \"{$questionText}\" Tap here to view it.";

        // Get all approved connections
        $connectedUsers = $videoOwner->getConnectedUsers();
        
        foreach ($connectedUsers as $connectedUser) {
            if ($connectedUser->id === $videoOwner->id) {
                continue; // Skip the video owner
            }

            $eventTime = now()->toISOString();
            
            $dynamicData = json_encode([
                'eventTime' => $eventTime,
                'eventType' => 'video_uploaded',
                'notificationData' => [
                    'userId' => $connectedUser->id,
                    'from_user_id' => $videoOwner->id,
                    'first_name' => $videoOwner->first_name ?? '',
                    'from_user_profile_pic' => $videoOwner->profile_pic ? Storage::temporaryUrl($videoOwner->profile_pic, now()->addDays(7)) : null,
                    'video_id' => $event->video->id,
                    'title' => $inAppTitle,
                    'push_title' => $pushTitle,
                    'push_text' => $pushText,
                ],
            ]);

            $notificationData = $this->notificationService->createNotificationData('video_uploaded', $inAppTitle, $dynamicData);
            $courierId = $this->notificationService->sendNotification($notificationData);

            $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox('video_uploaded', $inAppTitle, $dynamicData);
            $courierIdForInbox = $this->notificationService->sendNotification($notificationDataForInbox);

            $this->notificationRepository->save([
                'courier_id' => $courierIdForInbox,
                'from_user_id' => $videoOwner->id,
                'to_user_id' => $connectedUser->id,
                'cta_type' => 'video_uploaded_type',
                'video_id' => $event->video->id,
            ]);

            // Send Slack notification
            $this->sendSlackNotification($event->video);
        }
    }

    /**
     * Send notification to Slack about video upload
     * 
     * @param \App\Models\Video $video
     * @return void
     */
    private function sendSlackNotification($video)
    {
        $user = $video->owner;
        $questionSend = $video->questionSend;

        $isSelfRecorded = is_null($questionSend->sent_from_user_id);
        $message = "The name of the user: {$user->first_name} {$user->last_name}\n";
        $message .= "Phone number of user: {$user->phone_number}\n\n";

        if (!$isSelfRecorded) {
            $sender = $questionSend->sentFromUser;
            $receiver = $questionSend->sentToUser;
            $relationship = $this->relationshipRepository->findOneBy(['from_user_id' => $sender->id, 'to_user_id' => $receiver->id]);

            $message .= "Name of the sender: {$sender->first_name} {$sender->last_name}\n";
            $message .= "Phone number of the sender: {$sender->phone_number}\n\n";
            $message .= "{$sender->first_name} is {$receiver->first_name}'s {$relationship["name_as_from"]}\n";
            $message .= "{$receiver->first_name} is {$sender->first_name}'s {$relationship["name_as_outsider"]}\n";
        }

        // Send the message to Slack
        Http::post(env('SLACK_WEBHOOK_URL'), [
            'text' => $message,
        ]);
    }

    /**
     * Send and save notification for a user.
     */
    protected function sendNotification($event, string $eventType, string $inAppTitle, string $pushTitle, string $pushText)
    {
        $user  = Auth::user();
        $video = $this->videoRepository->findOne($event->videoComment->video_id ?? $event->videoView->video_id);

        $eventTime = now()->toISOString();

        if ($video->owner_user_id === $user->id) {
            return;
        }

        $dynamicData = json_encode([
            'eventTime'        => $eventTime,
            'eventType'        => $eventType,
            'notificationData' => [
                'userId'                => $video->owner_user_id,
                'from_user_id'          => $user->id,
                'first_name'            => $user->first_name ?? '',
                'from_user_profile_pic' => $user->profile_pic ? Storage::temporaryUrl($user->profile_pic, now()->addDays(7)) : null,
                'video_id'              => $video->id,
                'video_comment_id'      => $event->videoComment?->id ?? null,
                'video_comment_text'    => $event->videoComment?->comment_text ?? null,
                'title'                 => $inAppTitle,
                'push_title'            => $pushTitle,
                'push_text'             => $pushText,
            ],
        ]);

        $notificationData = $this->notificationService->createNotificationData($eventType, $inAppTitle, $dynamicData);
        $courierId        = $this->notificationService->sendNotification($notificationData);

        $notificationDataForInbox = $this->notificationService->createNotificationDataForInbox($eventType, $inAppTitle, $dynamicData);
        $courierIdForInbox        = $this->notificationService->sendNotification($notificationDataForInbox);

        $this->notificationRepository->save([
            'courier_id'   => $courierIdForInbox,
            'from_user_id' => $user->id,
            'to_user_id'   => $video->owner_user_id,
            'cta_type'     => "{$eventType}_type",
            'video_id'     => $video->id,
        ]);
    }
}
