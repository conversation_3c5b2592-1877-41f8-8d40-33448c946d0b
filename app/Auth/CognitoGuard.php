<?php

namespace App\Auth;

use App\Cognito\CognitoClient;
use App\Models\User;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use Illuminate\Auth\TokenGuard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;

class CognitoGuard extends TokenGuard
{
    /**
     * @var CognitoClient
     */
    protected $client;

    /**
     * @param UserProvider $provider
     * @param Request $request
     * @param $inputKey
     * @param $storageKey
     * @param $hash
     */
    public function __construct(UserProvider $provider, Request $request, $inputKey = 'api_token', $storageKey = 'api_token', $hash = false)
    {
        parent::__construct($provider, $request, $inputKey, $storageKey, $hash);
    }

    /**
     * Get the currently authenticated user.
     *
     * @override
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        //return parent::user();
        if (! is_null($this->user)) {
            return $this->user;
        }

        $user = null;

        try {
            $token = $this->getTokenForRequest();

            if (! empty($token)) {
                $tokenDetails = getCognitoTokeDetails();
                $user = $this->provider->retrieveByCredentials([
                    'sub' => $tokenDetails['sub'],
                    'registration_status' => [User::REGISTRATION_STATUS_VERIFIED]
                ]);
            }
        } catch (\Exception $e) {
            $tokenDetails = null;
        }

        return $this->user = $user;
    }

}
