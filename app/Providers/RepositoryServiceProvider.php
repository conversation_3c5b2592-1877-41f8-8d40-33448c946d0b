<?php
namespace App\Providers;

use App\Models\ConnectionRequest;
use App\Models\ConnectionRequestStatus;
use App\Models\FollowupQuestionSend;
use App\Models\Invitation;
use App\Models\Membership;
use App\Models\Notification;
use App\Models\Question;
use App\Models\QuestionActivity;
use App\Models\QuestionSend;
use App\Models\QuestionStatus;
use App\Models\Relationship;
use App\Models\Tag;
use App\Models\TagQuestion;
use App\Models\User;
use App\Models\Video;
use App\Models\VideoComment;
use App\Models\VideoFollowupQuestion;
use App\Models\VideoView;
use App\Models\MembershipType;
use App\Models\MembershipGift;
use App\Models\OfferCode;
use App\Models\OfferCodeUser;
use App\Models\RevenuecatEventLog;
use App\Models\PaywallActivity;
use App\Models\PaywallActivityType;
use App\Repositories\Contracts\ConnectionRequestRepository;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use App\Repositories\Contracts\FamilyMemberRepository;
use App\Repositories\Contracts\FollowupQuestionSendRepository;
use App\Repositories\Contracts\InvitationRepository;
use App\Repositories\Contracts\MembershipTypeRepository;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionActivityRepository;
use App\Repositories\Contracts\QuestionRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Repositories\Contracts\QuestionStatusRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Repositories\Contracts\TagQuestionRepository;
use App\Repositories\Contracts\TagRepository;
use App\Repositories\Contracts\UserRepository;
use App\Repositories\Contracts\VideoCommentRepository;
use App\Repositories\Contracts\VideoFollowupQuestionRepository;
use App\Repositories\Contracts\VideoRepository;
use App\Repositories\Contracts\VideoViewRepository;
use App\Repositories\Contracts\MembershipGiftRepository;
use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\OfferCodeUserRepository;
use App\Repositories\Contracts\RevenuecatEventLogRepository;
use App\Repositories\Contracts\PaywallActivityRepository;
use App\Repositories\Contracts\PaywallActivityTypeRepository;
use App\Repositories\EloquentConnectionRequestRepository;
use App\Repositories\EloquentConnectionRequestStatusRepository;
use App\Repositories\EloquentFamilyMemberRepository;
use App\Repositories\EloquentFollowupQuestionSendRepository;
use App\Repositories\EloquentInvitationRepository;
use App\Repositories\EloquentNotificationRepository;
use App\Repositories\EloquentQuestionActivityRepository;
use App\Repositories\EloquentQuestionRepository;
use App\Repositories\EloquentQuestionSendRepository;
use App\Repositories\EloquentQuestionStatusRepository;
use App\Repositories\EloquentRelationshipRepository;
use App\Repositories\EloquentTagQuestionRepository;
use App\Repositories\EloquentTagRepository;
use App\Repositories\EloquentUserRepository;
use App\Repositories\EloquentVideoCommentRepository;
use App\Repositories\EloquentVideoFollowupQuestionRepository;
use App\Repositories\EloquentVideoRepository;
use App\Repositories\EloquentVideoViewRepository;
use App\Repositories\EloquentMembershipTypeRepository;
use App\Repositories\EloquentMembershipRepository;
use App\Repositories\EloquentMembershipGiftRepository;
use App\Repositories\EloquentOfferCodeRepository;
use App\Repositories\EloquentOfferCodeUserRepository;
use App\Repositories\EloquentRevenuecatEventLogRepository;
use App\Repositories\EloquentPaywallActivityRepository;
use App\Repositories\EloquentPaywallActivityTypeRepository;
use Carbon\Laravel\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(UserRepository::class, function () {
            return new EloquentUserRepository(new User());
        });
        $this->app->bind(TagRepository::class, function () {
            return new EloquentTagRepository(new Tag());
        });
        $this->app->bind(VideoRepository::class, function () {
            return new EloquentVideoRepository(new Video());
        });
        $this->app->bind(VideoCommentRepository::class, function () {
            return new EloquentVideoCommentRepository(new VideoComment());
        });
        $this->app->bind(VideoViewRepository::class, function () {
            return new EloquentVideoViewRepository(new VideoView());
        });
        $this->app->bind(QuestionRepository::class, function () {
            return new EloquentQuestionRepository(new Question());
        });
        $this->app->bind(TagQuestionRepository::class, function () {
            return new EloquentTagQuestionRepository(new TagQuestion());
        });
        $this->app->bind(InvitationRepository::class, function () {
            return new EloquentInvitationRepository(new Invitation());
        });
        $this->app->bind(NotificationRepository::class, function () {
            return new EloquentNotificationRepository(new Notification());
        });
        $this->app->bind(RelationshipRepository::class, function () {
            return new EloquentRelationshipRepository(new Relationship());
        });
        $this->app->bind(QuestionSendRepository::class, function () {
            return new EloquentQuestionSendRepository(new QuestionSend());
        });
        $this->app->bind(FamilyMemberRepository::class, function () {
            return new EloquentFamilyMemberRepository(new Relationship());
        });
        $this->app->bind(QuestionStatusRepository::class, function () {
            return new EloquentQuestionStatusRepository(new QuestionStatus());
        });
        $this->app->bind(QuestionActivityRepository::class, function () {
            return new EloquentQuestionActivityRepository(new QuestionActivity());
        });
        $this->app->bind(ConnectionRequestStatusRepository::class, function () {
            return new EloquentConnectionRequestStatusRepository(new ConnectionRequestStatus());
        });
        $this->app->bind(ConnectionRequestRepository::class, function () {
            return new EloquentConnectionRequestRepository(new ConnectionRequest());
        });
        $this->app->bind(FollowupQuestionSendRepository::class, function () {
            return new EloquentFollowupQuestionSendRepository(new FollowupQuestionSend());
        });
        $this->app->bind(VideoFollowupQuestionRepository::class, function () {
            return new EloquentVideoFollowupQuestionRepository(new VideoFollowupQuestion());
        });
        $this->app->bind(MembershipTypeRepository::class, function () {
            return new EloquentMembershipTypeRepository(new MembershipType());
        });
        $this->app->bind(MembershipRepository::class, function () {
            return new EloquentMembershipRepository(new Membership());
        });
        $this->app->bind(MembershipGiftRepository::class, function () {
            return new EloquentMembershipGiftRepository(new MembershipGift());
        });
        $this->app->bind(RevenuecatEventLogRepository::class, function () {
            return new EloquentRevenuecatEventLogRepository(new RevenuecatEventLog());
        });
        $this->app->bind(PaywallActivityRepository::class, function () {
            return new EloquentPaywallActivityRepository(new PaywallActivity());
        });
        $this->app->bind(PaywallActivityTypeRepository::class, function () {
            return new EloquentPaywallActivityTypeRepository(new PaywallActivityType());
        });
        $this->app->bind(OfferCodeRepository::class, function () {
            return new EloquentOfferCodeRepository(new OfferCode());
        });
        $this->app->bind(OfferCodeUserRepository::class, function () {
            return new EloquentOfferCodeUserRepository(new OfferCodeUser());
        });
    }
}
