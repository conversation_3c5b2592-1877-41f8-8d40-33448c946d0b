<?php

namespace App\Providers;

use App\Events\ConnectionRequestNotification;
use App\Events\FollowupQuestionSendCreated;
use App\Events\InvitationCreated;
use App\Events\NotificationCreated;
use App\Events\QuestionAnswered;
use App\Events\QuestionSent;
use App\Events\QuestionUnlocked;
use App\Events\UpdateInvitationAndRelationship;
use App\Events\VideoCommented;
use App\Events\VideoLiked;
use App\Events\VideoUploaded;
use App\Events\VideoViewed;
use App\Events\TranscriptionCompleted;
use App\Events\NotificationSent;
use App\Events\VideoMasterReadyForProcessing;
use App\Listeners\ConnectionRequestNotificationListener;
use App\Listeners\FollowupQuestionSentListener;
use App\Listeners\InvitationCreatedListener;
use App\Listeners\NotificationCreatedListener;
use App\Listeners\QuestionSendNotificationListener;
use App\Listeners\QuestionUnlockedNotificationListener;
use App\Listeners\SendSlackNotificationForVideoUpload;
use App\Listeners\UpdateInvitationAndRelationshipListener;
use App\Listeners\VideoNotificationListener;
use App\Listeners\GenerateFollowUpQuestionsListener;
use App\Listeners\ProcessVideoMasterReadyListener;
use App\Listeners\SendSlackNotificationForUserNotification;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        NotificationCreated::class => [
            NotificationCreatedListener::class,
        ],
        InvitationCreated::class => [
            InvitationCreatedListener::class,
        ],
        UpdateInvitationAndRelationship::class => [
            UpdateInvitationAndRelationshipListener::class,
        ],
        ConnectionRequestNotification::class => [
            ConnectionRequestNotificationListener::class,
        ],
        FollowupQuestionSendCreated::class => [
            FollowupQuestionSentListener::class,
        ],
        VideoViewed::class => [
            VideoNotificationListener::class . '@handleVideoViewed',
        ],
        VideoLiked::class => [
            VideoNotificationListener::class . '@handleVideoLiked',
        ],
        VideoCommented::class => [
            VideoNotificationListener::class . '@handleVideoCommented',
        ],
        QuestionSent::class => [
            QuestionSendNotificationListener::class . '@handleQuestionSent',
        ],
        QuestionAnswered::class => [
            QuestionSendNotificationListener::class . '@handleQuestionAnswered',
        ],
        QuestionUnlocked::class => [
            QuestionUnlockedNotificationListener::class,
        ],
        VideoUploaded::class => [
            SendSlackNotificationForVideoUpload::class,
        ],
        TranscriptionCompleted::class => [
            GenerateFollowUpQuestionsListener::class,
        ],
        NotificationSent::class => [
            SendSlackNotificationForUserNotification::class,
        ],
        VideoMasterReadyForProcessing::class => [
            ProcessVideoMasterReadyListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
