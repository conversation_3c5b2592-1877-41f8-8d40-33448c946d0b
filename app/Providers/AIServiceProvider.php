<?php

namespace App\Providers;

use App\Services\Contracts\AIService;
use App\Services\OpenAIService;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class AIServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(AIService::class, function () {
            return new OpenAIService(new Client());
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
} 