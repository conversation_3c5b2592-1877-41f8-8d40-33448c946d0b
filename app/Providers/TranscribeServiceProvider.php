<?php
namespace App\Providers;

use App\Services\Contracts\TranscribeService;
use App\Services\VideoTranscribeService;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class TranscribeServiceProvider extends ServiceProvider
{
    /**
     * Register the service into the container.
     */
    public function register()
    {

        $this->app->bind(TranscribeService::class, function () {
            return new VideoTranscribeService(new Client());
        });
    }

    /**
     * Perform post-registration booting of services.
     */
    public function boot()
    {
        // Bootstrapping specific to Deepgram services
    }
}
