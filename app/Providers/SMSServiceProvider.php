<?php

namespace App\Providers;

use App\Services\Contracts\SMSProvider;
use App\Services\TwilioSMSProvider;
use Illuminate\Support\ServiceProvider;

class SMSServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(SMSProvider::class, TwilioSMSProvider::class);
    }

    public function boot()
    {
        // Additional boot logic specific to SMS services, if needed
    }
}
