<?php

namespace App\Providers;

use App\Services\Contracts\NotificationService;
use App\Services\CourierNotificationService;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class NotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Bind the NotificationService to the service container
        $this->app->bind(NotificationService::class, function () {
            return new CourierNotificationService(new Client());
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
