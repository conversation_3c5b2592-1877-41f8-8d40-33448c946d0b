<?php

namespace App\Providers;

use App\Services\Contracts\VideoProcessor;
use App\Services\MuxVideoProcessor;
use Illuminate\Support\ServiceProvider;

class VideoServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(VideoProcessor::class, MuxVideoProcessor::class);
    }

    public function boot()
    {
        // Bootstrapping specific to video services
    }
}
