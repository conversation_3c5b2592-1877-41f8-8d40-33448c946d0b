<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class QuestionActivityResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'question_id' => $this->question_id,
            'question_status_id' => $this->question_status_id,
            'question_status' => $this->questionStatus['title'],
            'question_send_id' => $this->question_send_id,
            'sentFromUser' => new UserResource($this->questionSends->sentFromUser),
            'sentToUser' => new UserResource($this->questionSends->sentToUser),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
