<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class QuestionSendResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'question_id' => $this->question_id,
            'question' => new QuestionResource($this->question),
            'relationship' => new RelationshipResource($this->relationship),
            'video_id' => $this->video_id,
            'video' => new VideoResource($this->video),
            'followup_questions' => $this->when($this->needToInclude($request, 'video.followup_questions') && $this->video, function () {
                return $this->video->followupQuestions->map(function ($followup) {
                    return new QuestionResource($followup->followupQuestion);
                });
            }),
            'sent_from_user_id' => $this->sent_from_user_id,
            'sent_from_user' => new UserResource($this->sentFromUser),
            'sent_to_user_id' => $this->sent_to_user_id,
            'sent_to_user' => new UserResource($this->sentToUser),
            'sent_at' => $this->sent_at,
            'followupQuestionSend' => $this->when($this->needToInclude($request, 'question.followupQuestionSend'), function () {
                return new FollowupQuestionSendResourceCollection($this->followupQuestionSend);
            }),
            'trashed_at' =>$this->trashed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'questionActivities' => $this->when($this->needToInclude($request, 'question.questionActivity'), function () {
                return new QuestionActivityResourceCollection($this->questionActivity);
            }),
        ];
    }
}
