<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class VideoViewResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'viewer_user' => new UserResource($this->viewer),
            'video_id' => $this->video_id,
            'viewed_at' => $this->viewed_at,
            'liked_at' => $this->liked_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
