<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class PaywallActivityResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'activity_type_id' => $this->activity_type_id,
            // Add other fields as needed
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
} 