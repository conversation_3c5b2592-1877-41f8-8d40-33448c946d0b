<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionSendInboxOrOutboxResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'question_send_id' => $this->resource['question_send']['id'],
            'sent_from_user_id' => $this->resource['question_send']['sent_from_user_id'],
            'sent_to_user_id' => $this->resource['question_send']['sent_to_user_id'],
            'question_id' => $this->resource['question_send']['question_id'],
            'question' => new QuestionResource($this->resource['question_send']->question),
            'relationship_id' => $this->resource['question_send']['relationship_id'],
            'video_id' => $this->resource['question_send']['video_id'],
            'sent_at' => $this->resource['question_send']['sent_at'],
            'relationship' => $this->resource['question_send']['relationship_id']
            ? new RelationshipResource($this->resource['question_send']->relationship)
            : null,
            'created_at' => $this->resource['question_send']['created_at'],
            'updated_at' => $this->resource['question_send']['updated_at'],
            'question_activities' => QuestionActivityResource::collection(collect($this->resource['question_activities'])),
        ];
    }
}
