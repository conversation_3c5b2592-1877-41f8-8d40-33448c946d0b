<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class MembershipGiftResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'to_user_id' => $this->to_user_id,
            'invitation_id' => $this->invitation_id,
            'from_user_id' => $this->from_user_id,
            'sent_at' => $this->sent_at,
            'accepted_at' => $this->accepted_at,
            'rejected_at' => $this->rejected_at,
            'expires_at' => $this->expires_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
} 