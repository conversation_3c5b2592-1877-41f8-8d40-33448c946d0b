<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class NotificationResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'courier_id' => $this->courier_id,
            'from_user_id' => $this->from_user_id,
            'to_user_id' => $this->to_user_id,
            'cta_type' => $this->cta_type,
            'question_id' => $this->question_id,
            'video_id' => $this->video_id,
            'seen_at' => $this->seen_at,
            'created_at' => $this->created_at,
            // 'fromUser' => new UserResource($this->whenLoaded('fromUser')),
            // 'toUser' => new UserResource($this->whenLoaded('toUser')),
            // 'question' => new QuestionResource($this->whenLoaded('question')),
            // 'video' => new VideoResource($this->whenLoaded('video')),
        ];
    }
}
