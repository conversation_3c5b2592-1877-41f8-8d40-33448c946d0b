<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfferCodeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'is_auto_renew' => $this->is_auto_renew,
            'title' => $this->title,
            'regular_rate' => $this->regular_rate,
            'revenue_cat_product_id' => $this->revenue_cat_product_id,
            'duration_num' => $this->duration_num,
            'duration_units' => $this->duration_units,
            'expires_at' => $this->expires_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
