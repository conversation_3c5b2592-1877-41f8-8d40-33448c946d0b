<?php

namespace App\Http\Resources;

use Illuminate\Support\Facades\Storage;

class UserResource extends Resource
{
    public function toArray($request)
    {
        $fromUserCount = $this->fromUserRelationships->where('connectionRequest.status.title', '!=', 'discarded')->count();
        $toUserCount = $this->toUserRelationships->where('connectionRequest.status.title', '!=', 'discarded')->count();

        $totalFamilyCount = $fromUserCount + $toUserCount;
        $storiesCount = $this->videos->count();
        return [
            'id' => $this->id,
            'sub' => $this->sub,
            'phone_number' => $this->phone_number,
            'first_name' => $this->first_name,
            'middle_name' => $this->middle_name,
            'last_name' => $this->last_name,
            'birthday' => $this->birthday,
            'total_family_count' => $totalFamilyCount,
            'stories' => $storiesCount,
            'invitations' => $this->when($this->needToInclude($request, 'user.invitations'), function () {
                return new InvitationResourceCollection($this->invitations);
            }),
            'profile_pic' => $this->profile_pic ? Storage::temporaryUrl($this->profile_pic, now()->addDays(7)) : null,
            'is_admin' => $this->is_admin,
            'membership' => $this->when($this->needToInclude($request, 'user.membership'), function () {
                return new MembershipResource($this->membership);
            }),
            'sent_gifts' => $this->when($this->needToInclude($request, 'user.sent_gifts'), function () {
                return new MembershipGiftResourceCollection($this->sentGifts);
            }),
            'received_gifts' => $this->when($this->needToInclude($request, 'user.received_gifts'), function () {
                return new MembershipGiftResourceCollection($this->receivedGifts);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
