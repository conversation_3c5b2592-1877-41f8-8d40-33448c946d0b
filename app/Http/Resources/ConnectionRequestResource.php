<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class ConnectionRequestResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'status_id' => $this->status_id,
            'relationship_id' => $this->relationship_id,
            'relationship' => $this->when($this->needToInclude($request, 'connectionRequest.relationship'), function () {
                return new RelationshipResource($this->relationship);
            }),
            'updated_at' => $this->updated_at,
            'created_at' => $this->created_at,

        ];
    }
}
