<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class FamilyMembersResourceCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            'data' => $this->collection->transform(function ($familyMember) {
                return [
                    'id' => $familyMember['id'],
                    'username' => $familyMember['username'],
                    'profile_pic' => $familyMember['profile_pic'],
                    'family_member_count' => $familyMember['family_member_count'],
                    'relationshipName' => $familyMember['relationshipName'],

                ];
            }),
        ];
    }
}
