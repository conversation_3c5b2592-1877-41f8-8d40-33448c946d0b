<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class MembershipResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'membership_type_id' => $this->membership_type_id,
            'started_at' => $this->started_at,
            'end_at' => $this->end_at,
            'is_autopay_enabled' => $this->is_autopay_enabled,
            'free_trials_used_count' => $this->free_trials_used_count,
            'available_gifts_count' => $this->available_gifts_count,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
} 