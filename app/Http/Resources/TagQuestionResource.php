<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class TagQuestionResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'tag_id' => $this->tag_id,
            'question_id' => $this->question_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // 'tag' => new TagResource($this->whenLoaded('tag')),
            // 'question' => new QuestionResource($this->whenLoaded('question')),
        ];
    }
}
