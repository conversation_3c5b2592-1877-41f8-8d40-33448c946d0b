<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class VideoResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'owner_user_id' => $this->owner_user_id,
            'mux_id' => $this->mux_id,
            'status' => $this->status,
            'video_file' => $this->when($this->needToInclude($request, 'video.video_file'), function () {
                return $this->video_file ? Storage::temporaryUrl($this->video_file, now()->addDays(7)) : null;
            }),

            'transcript_text' => $this->when($this->needToInclude($request, 'video.transcript_file'), function () {
                return $this->transcript_file ? json_decode(Storage::get($this->transcript_file), true)['transcription'] ?? null : null;
            }),

            'duration' => $this->duration,
            'liked_by_authenticated_user' => $this->getLikedAtByAuthenticatedUser(),
            'like_count' => $this->getLikeCount(),
            'video_views_count' => $this->videoViews->count(),
            'video_commentv_count' => $this->videoComments->count(),
            'owner' => $this->when($this->needToInclude($request, 'video.owner'), function () {
                return new UserResource($this->owner);
            }),
            'question' => $this->when($this->needToInclude($request, 'video.question'), function () {
                return new QuestionResource($this->question);
            }),
            'followup_questions' => $this->when($this->needToInclude($request, 'video.followup_questions'), function () {
                return $this->followupQuestions->map(function ($followup) {
                    return new QuestionResource($followup->followupQuestion);
                });
            }),
            'trashed_at' => $this->trashed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
