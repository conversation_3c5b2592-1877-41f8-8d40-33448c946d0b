<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class RelationshipResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'from_user_id' => $this->from_user_id,
            'from_user' => $this->fromUser,
            'to_user_id' => $this->to_user_id,
            'to_user' => $this->toUser,
            'reciprocal_relationship_id' => $this->reciprocal_relationship_id,
            'to_invitation_id' => $this->to_invitation_id,
            'is_hidden' => $this->is_hidden,
            'name_as_from' => $this->name_as_from,
            'name_as_outsider' => $this->name_as_outsider,
            'created_at' => $this->created_at,
            // 'reciprocalRelationship' => new RelationshipResource($this->whenLoaded('reciprocalRelationship')),
            // 'questionSends' => QuestionSendResource::collection($this->whenLoaded('questionSends')),
        ];
    }
}
