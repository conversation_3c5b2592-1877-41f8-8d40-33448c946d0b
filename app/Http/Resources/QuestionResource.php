<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class QuestionResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'question_text' => $this->question_text,
            'source_question_id' => $this->source_question_id,
            'creator__user_id' => $this->creator__user_id,
            'question_type' => $this->question_type,
            'to_user_id' => $this->to_user_id,
            'createdBy' => $this->when($this->needToInclude($request, 'question.createdBy'), function () {
                return new UserResource($this->createdBy);
            }),
            'questionActivities' => $this->when($this->needToInclude($request, 'question.questionActivities'), function () {
                return new QuestionActivityResourceCollection($this->questionActivities);
            }),
            'tags' => $this->when($this->needToInclude($request, 'question.tags'), function () {
                return new TagResourceCollection($this->tags);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
