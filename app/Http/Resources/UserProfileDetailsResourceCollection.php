<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class UserProfileDetailsResourceCollection extends JsonResource
{
    public function toArray($request)
    {
        $data = is_array($this->resource) ? $this->resource : $this->resource->toArray();

        return [
            'id' => $data['id'],
            'phone_number' => $data['phone_number'],
            'first_name' => $data['first_name'],
            'middle_name' => $data['middle_name'] ?? null,
            'last_name' => $data['last_name'],
            'profile_pic' => $data['profile_pic'] ? Storage::temporaryUrl($data['profile_pic'], now()->addDays(7)) : null,
            'total_family_count' => $data['total_family_count'] ?? null,
            'stories' => $data['stories'] ?? null,
            'question_sends' => $data['question_sends'] ?? null,
            'relationship' => $data['relationship'] ?? null,
            'fromUserRelationshipsDetails' => $data['fromUserRelationshipsDetails'] ?? null,
            'toUserRelationshipsDetails' => $data['toUserRelationshipsDetails'] ?? null,
        ];
    }

}
