<?php

namespace App\Http\Resources;

use Illuminate\Support\Facades\Storage;

class UserProfileDetailsResourceCollection extends Resource
{
    public function toArray($request)
    {
        $data = is_array($this->resource) ? $this->resource : $this->resource->toArray();

        return [
            'id' => $data['id'],
            'phone_number' => $data['phone_number'],
            'first_name' => $data['first_name'],
            'middle_name' => $data['middle_name'] ?? null,
            'last_name' => $data['last_name'],
            'profile_pic' => $data['profile_pic'] ? Storage::temporaryUrl($data['profile_pic'], now()->addDays(7)) : null,
            'total_family_count' => $data['total_family_count'] ?? null,
            'stories' => $data['stories'] ?? null,
            'question_sends' => $data['question_sends'] ?? null,
            'relationship' => $data['relationship'] ?? null,
            'fromUserRelationshipsDetails' => $data['fromUserRelationshipsDetails'] ?? null,
            'toUserRelationshipsDetails' => $data['toUserRelationshipsDetails'] ?? null,
            'membership' => $this->when(isset($data['membership']) && $this->needToInclude($request, 'user.membership'), 
                function () use ($data) {
                    return new MembershipResource($data['membership']);
                }),
                'membership_type' => $data['membership_type'] ?? null,
            'sent_gifts' => $this->when(isset($data['sent_gifts']) && $this->needToInclude($request, 'user.sent_gifts'), 
                function () use ($data) {
                    return new MembershipGiftResourceCollection($data['sent_gifts']);
                }),
            'received_gifts' => $this->when(isset($data['received_gifts']) && $this->needToInclude($request, 'user.received_gifts'), 
                function () use ($data) {
                    return new MembershipGiftResourceCollection($data['received_gifts']);
                }),
        ];
    }

}
