<?php

namespace App\Http\Resources\OfferCode;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OfferCodeCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->transform(function ($offerCode) use ($request) {
                return new OfferCodeResource($offerCode);
            }),
            'summary' => $this->when($request->has('include_summary'), [
                'total_codes' => $this->collection->count(),
                'active_codes' => $this->collection->where('status', 'active')->count(),
                'expired_codes' => $this->collection->filter(function ($code) {
                    return $code->expires_at && $code->expires_at->isPast();
                })->count(),
                'total_usage' => $this->collection->sum('used_count'),
                'types_breakdown' => $this->collection->groupBy('type')->map(function ($group) {
                    return $group->count();
                }),
            ]),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'generated_at' => now()->toDateTimeString(),
                'total_items' => $this->collection->count(),
            ],
        ];
    }
}
