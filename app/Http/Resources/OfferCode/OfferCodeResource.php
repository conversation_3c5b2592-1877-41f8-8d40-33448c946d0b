<?php

namespace App\Http\Resources\OfferCode;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfferCodeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'description' => $this->description,
            'type' => $this->type,
            'type_label' => $this->getTypeLabel(),
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'discount_percentage' => $this->discount_percentage,
            'discount_amount' => $this->discount_amount,
            'duration_num' => $this->duration_num,
            'duration_units' => $this->duration_units,
            'duration_label' => $this->getDurationLabel(),
            'max_uses' => $this->max_uses,
            'used_count' => $this->used_count,
            'usage_percentage' => $this->getUsagePercentage(),
            'expires_at' => $this->expires_at?->toDateTimeString(),
            'expires_at_human' => $this->expires_at?->diffForHumans(),
            'is_expired' => $this->isExpired(),
            'is_active' => $this->isActive(),
            'is_usage_limit_reached' => $this->isUsageLimitReached(),
            'remaining_uses' => $this->getRemainingUses(),
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
            
            // Include usage data if loaded
            'usages' => $this->whenLoaded('usages', function () {
                return $this->usages->map(function ($usage) {
                    return [
                        'user_id' => $usage->user_id,
                        'applied_at' => $usage->applied_at,
                        'user' => $usage->user ? [
                            'id' => $usage->user->id,
                            'full_name' => $usage->user->full_name,
                            'phone_number' => $usage->user->phone_number,
                        ] : null,
                    ];
                });
            }),

            // Include statistics if requested
            'statistics' => $this->when($request->has('include_stats'), function () {
                return [
                    'total_uses' => $this->used_count,
                    'unique_users' => $this->usages()->distinct('user_id')->count(),
                    'usage_rate' => $this->getUsagePercentage(),
                    'days_until_expiry' => $this->expires_at ? 
                        now()->diffInDays($this->expires_at, false) : null,
                    'is_popular' => $this->used_count > 10, // Arbitrary threshold
                ];
            }),
        ];
    }

    /**
     * Get type label
     */
    private function getTypeLabel(): string
    {
        return match($this->type) {
            'membership_extension' => 'Membership Extension',
            'discount' => 'Discount',
            'free_trial' => 'Free Trial',
            default => ucfirst(str_replace('_', ' ', $this->type))
        };
    }

    /**
     * Get status label
     */
    private function getStatusLabel(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'expired' => 'Expired',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get duration label
     */
    private function getDurationLabel(): ?string
    {
        if (!$this->duration_num || !$this->duration_units) {
            return null;
        }

        $unit = $this->duration_num > 1 ? 
            str_plural($this->duration_units) : 
            $this->duration_units;

        return "{$this->duration_num} {$unit}";
    }

    /**
     * Get usage percentage
     */
    private function getUsagePercentage(): ?float
    {
        if (!$this->max_uses) {
            return null;
        }

        return round(($this->used_count / $this->max_uses) * 100, 2);
    }

    /**
     * Check if offer code is expired
     */
    private function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if offer code is active
     */
    private function isActive(): bool
    {
        return $this->status === 'active' && !$this->isExpired() && !$this->isUsageLimitReached();
    }

    /**
     * Check if usage limit is reached
     */
    private function isUsageLimitReached(): bool
    {
        return $this->max_uses && $this->used_count >= $this->max_uses;
    }

    /**
     * Get remaining uses
     */
    private function getRemainingUses(): ?int
    {
        if (!$this->max_uses) {
            return null;
        }

        return max(0, $this->max_uses - $this->used_count);
    }
}
