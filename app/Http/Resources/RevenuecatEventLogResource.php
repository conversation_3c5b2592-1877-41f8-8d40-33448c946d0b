<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class RevenuecatEventLogResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'external_id' => $this->external_id,
            'status' => $this->status,
            'app_user_id' => $this->app_user_id,
            'product_id' => $this->product_id,
            'store' => $this->store,
            'price' => $this->price,
            'period_type' => $this->period_type,
            'data' => $this->data,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
} 