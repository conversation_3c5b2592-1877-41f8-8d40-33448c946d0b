<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;

class InvitationResource extends Resource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'from_user_id' => $this->from_user_id,
            'to_phone_number' => $this->to_phone_number,
            'to_user_id' => $this->to_user_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'fromUser' => new UserResource($this->fromUser),
            // 'toUser' => new UserResource($this->whenLoaded('toUser')),
        ];
    }
}
