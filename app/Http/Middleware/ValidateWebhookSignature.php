<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ValidateWebhookSignature
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip validation in local environment for testing
        if (app()->environment('local')) {
            return $next($request);
        }

        $signature = $request->header('Mux-Signature');
        $timestamp = $request->header('Mux-Timestamp');
        $payload = $request->getContent();

        if (!$signature || !$timestamp) {
            Log::warning('Webhook request missing required headers', [
                'has_signature' => !empty($signature),
                'has_timestamp' => !empty($timestamp),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            
            return response()->json(['error' => 'Invalid webhook signature'], 401);
        }

        // Verify timestamp is recent (within 5 minutes)
        $currentTime = time();
        if (abs($currentTime - $timestamp) > 300) {
            Log::warning('Webhook timestamp too old', [
                'timestamp' => $timestamp,
                'current_time' => $currentTime,
                'difference' => abs($currentTime - $timestamp)
            ]);
            
            return response()->json(['error' => 'Request timestamp too old'], 401);
        }

        // Verify signature
        $webhookSecret = config('services.mux.webhook_secret');
        if (!$webhookSecret) {
            Log::error('Mux webhook secret not configured');
            return response()->json(['error' => 'Webhook validation not configured'], 500);
        }

        $expectedSignature = hash_hmac('sha256', $timestamp . '.' . $payload, $webhookSecret);
        
        if (!hash_equals($expectedSignature, $signature)) {
            Log::warning('Invalid webhook signature', [
                'expected' => $expectedSignature,
                'received' => $signature,
                'ip' => $request->ip()
            ]);
            
            return response()->json(['error' => 'Invalid webhook signature'], 401);
        }

        return $next($request);
    }
}
