<?php

namespace App\Http\Middleware;

use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Closure;
use Illuminate\Support\Facades\Auth;

class AttachBugsnagUserInfo
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        Bugsnag::registerCallback(function ($report) {
            if (Auth::check()) {
                $user = Auth::user();
                $report->setUser([
                    'id' => $user->id,
                    'full_name' => $user->full_name,
                    'phone_number' => $user->phone_number,
                ]);
            } else {
                $report->setUser([
                    'id' => 'guest',
                    'full_name' => 'Guest User',
                    'email' => null,
                ]);
            }

        });

        return $next($request);
    }
}
