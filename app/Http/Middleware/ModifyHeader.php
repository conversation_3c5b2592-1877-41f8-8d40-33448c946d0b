<?php
namespace App\Http\Middleware;
use Closure;
class ModifyHeader
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (empty($request->headers->get('Content-Type'))) {
            $request->headers->set('Content-Type', 'application/json');
        }
        if ($request->headers->get('Accept') == '*/*') {
            $request->headers->set('Accept', 'application/json');
        }
        $response = $next($request);
        $response->headers->set("Access-Control-Allow-Origin", $this->getAllowedOrigins($request));
        $response->headers->set("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With, X-localization");
        $response->headers->set("Access-Control-Allow-Methods", "OPTIONS, HEAD, GET, POST, PUT, DELETE");
        $response->headers->set("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        return $response;
    }

    private function getAllowedOrigins($request)
    {
        $requestDomain = $request->headers->get('origin');
        $allowedDomains = explode(',', env('CORS_ALLOWED_ORIGINS', '*'));

        $domainFound = $allowedDomains[0];
        $domainIndex = array_search($requestDomain, $allowedDomains);
        if ($domainIndex !== false) {
            $domainFound = $allowedDomains[$domainIndex];
        }
        return $domainFound;
    }
}
