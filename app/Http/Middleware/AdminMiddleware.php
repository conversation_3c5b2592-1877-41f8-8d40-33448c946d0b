<?php

namespace App\Http\Middleware;

use App\Cognito\CognitoClient;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    private $cognitoClient;

    public function __construct(CognitoClient $cognitoClient)
    {
        $this->cognitoClient = $cognitoClient;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Check if user is an admin
        if (!auth()->user()->isAdmin()) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        // Check if user is in the Admins group
        if (!$this->cognitoClient->isInAdminsGroup(auth()->user()->phone_number)) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        return $next($request);
    }
}
