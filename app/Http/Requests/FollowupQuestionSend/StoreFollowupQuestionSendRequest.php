<?php

namespace App\Http\Requests\FollowupQuestionSend;

use App\Http\Requests\Request;

class StoreFollowupQuestionSendRequest extends Request
{
    public function rules()
    {
        return [
            'question_id' => 'nullable|exists:questions,id',
            'question_send_id' => 'required|exists:question_sends,id',
            'question_text' => 'nullable|string',
            'question_type' => 'string',
            'to_user_id' => 'required',
            'tag_id' => 'required',
        ];
    }
}
