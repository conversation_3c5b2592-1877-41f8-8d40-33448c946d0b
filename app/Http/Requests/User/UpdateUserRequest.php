<?php

namespace App\Http\Requests\User;

use App\Http\Requests\Request;

class UpdateUserRequest extends Request
{

    public function rules()
    {
        return [
            'phone_number' => 'nullable | string',
            'password' => 'nullable | string',
            'full_name' => 'nullable|string',
            'first_name' => 'string',
            'middle_name' => 'nullable|string',
            'last_name' => 'string',
            'birthday' => 'nullable|date',
            'profile_pic' => 'nullable|image',
            'is_admin' => 'nullable',
            'membership_id' => 'nullable|exists:memberships,id',
        ];
    }
}
