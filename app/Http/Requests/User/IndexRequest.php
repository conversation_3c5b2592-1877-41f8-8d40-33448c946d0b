<?php

namespace App\Http\Requests\User;

use App\Http\Requests\Request;

class IndexRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone_number' => 'string',
            'first_name' => 'nullable|string',
            'query' => 'string',
            'is_admin' => 'boolean',
        ];
    }
}
