<?php

namespace App\Http\Requests\Video;

use App\Http\Requests\Request;

class HomeFeedRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userId' => 'nullable|integer|exists:users,id',
        ];
    }
}
