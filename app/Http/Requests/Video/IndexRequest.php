<?php

namespace App\Http\Requests\Video;

use App\Http\Requests\Request;

class IndexRequest extends Request
{


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'owner_user_id'=> 'numeric'
        ];
    }
}
