<?php

namespace App\Http\Requests\Video;

use App\Http\Requests\Request;

class UpdateVideoRequest extends Request
{
    public function rules()
    {
        return [
            'owner_user_id' => 'exists:users,id',
            'mux_id' => 'string',
            'question_id' => 'integer',
            'status' => 'string',
            'duration' => 'integer',
            'trashed_at' => 'date',
        ];
    }
}
