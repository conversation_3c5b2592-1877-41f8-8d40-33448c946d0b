<?php

namespace App\Http\Requests\Membership;

use App\Http\Requests\Request;

class UpdateMembershipRequest extends Request
{
    public function rules()
    {
        return [
            'membership_type_id' => 'exists:membership_types,id',
            'started_at' => 'date',
            'end_at' => 'date|after:started_at',
            'is_autopay_enabled' => 'boolean',
            'free_trials_used_count' => 'integer|min:0',
            'available_gifts_count' => 'integer|min:0'
        ];
    }
} 