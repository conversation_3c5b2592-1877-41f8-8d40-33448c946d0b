<?php

namespace App\Http\Requests\Membership;

use App\Http\Requests\Request;

class StoreMembershipRequest extends Request
{
    public function rules()
    {
        return [
            'membership_type_id' => 'required|exists:membership_types,id',
            'started_at' => 'nullable|date',
            'end_at' => 'nullable|date|after:started_at',
            'is_autopay_enabled' => 'nullable|boolean',
            'free_trials_used_count' => 'nullable|integer|min:0',
            'available_gifts_count' => 'nullable|integer|min:0'
        ];
    }
} 
