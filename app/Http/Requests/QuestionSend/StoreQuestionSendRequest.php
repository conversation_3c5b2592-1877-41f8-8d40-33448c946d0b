<?php

namespace App\Http\Requests\QuestionSend;

use App\Http\Requests\Request;

class StoreQuestionSendRequest extends Request
{
    public function rules()
    {
        return [
            'question_id' => 'required|exists:questions,id',
            'sent_to_user_id' => 'nullable|exists:users,id',
            'question_status' => 'nullable | string',
            'relationship_id' => 'nullable|exists:relationships,id',
            'video_id' => 'nullable|exists:videos,id',
            'invitation_id' => 'nullable|exists:invitations,id',
            'sent_at' => 'required|date',
        ];
    }
}
