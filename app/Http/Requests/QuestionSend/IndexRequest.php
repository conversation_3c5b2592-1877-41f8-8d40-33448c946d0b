<?php

namespace App\Http\Requests\QuestionSend;

use App\Http\Requests\Request;

class IndexRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sent_from_user_id' => 'nullable | numeric',
            'sent_to_user_id' => 'nullable | numeric',
            'video_id' => 'nullable',
            'invitation_id' => 'nullable',
            'trashed_at' => 'nullable',


        ];
    }
}
