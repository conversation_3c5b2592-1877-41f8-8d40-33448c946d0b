<?php

namespace App\Http\Requests\Question;

use App\Http\Requests\Request;
use App\Models\Question;
use Illuminate\Validation\Rule;

class UpdateQuestionRequest extends Request
{
    public function rules()
    {
        return [
            'question_text' => 'required|string',
            'source_question_id' => 'nullable|integer',
            'creator__user_id' => 'nullable|exists:users,id',
            'question_type' => ['nullable', 'string', Rule::in(Question::ALL_QUESTION_TYPES)],
        ];
    }
}
