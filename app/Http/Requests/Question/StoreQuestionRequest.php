<?php

namespace App\Http\Requests\Question;

use App\Http\Requests\Request;

class StoreQuestionRequest extends Request
{
    public function rules()
    {
        return [
            'question_text' => 'required|string',
            'question_type' => 'string',
            'source_question_id' => 'nullable|integer',
            'creator__user_id' => 'nullable|exists:users,id',
            'to_user_id' => 'nullable|exists:users,id',
            'tag_id' => 'nullable  | exists:tags,id',
        ];
    }
}
