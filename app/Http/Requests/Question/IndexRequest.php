<?php

namespace App\Http\Requests\Question;

use App\Http\Requests\Request;
use App\Models\Question;
use Illuminate\Validation\Rule;

class IndexRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'creator__user_id' => 'exists:users,id',
            'to_user_id' => 'exists:users,id',
            'user_id' => 'exists:users,id',
            'question_type' => ['nullable', 'string', Rule::in(Question::ALL_QUESTION_TYPES)],

        ];
    }
}
