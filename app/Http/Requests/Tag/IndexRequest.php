<?php

namespace App\Http\Requests\Tag;

use App\Http\Requests\Request;

class IndexRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'string',
            'image' => 'nullable|image',
            'creator_user_id' => 'exists:users,id',
        ];
    }
}
