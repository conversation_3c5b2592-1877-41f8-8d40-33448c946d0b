<?php

namespace App\Http\Requests\PaywallActivity;

use App\Http\Requests\Request;

class IndexRequest extends Request
{
    public function rules(): array
    {
        return [
            'user_id' => 'sometimes|exists:users,id',
            'activity_type_id' => 'sometimes|exists:paywall_activity_types,id',
            'membership_gift_id' => 'sometimes|exists:membership_gifts,id',
            'price' => 'sometimes|numeric|min:0',
            'gift_count_purchased' => 'sometimes|integer|min:0',
        ];
    }
} 