<?php

namespace App\Http\Requests\PaywallActivity;

use App\Http\Requests\Request;

class StorePaywallActivityRequest extends Request
{
    public function rules()
    {
        return [
            'activity_type_id' => 'required|exists:paywall_activity_types,id',
            'membership_gift_id' => 'nullable|exists:membership_gifts,id',
            'price' => 'required|numeric|min:0',
            'gift_count_purchased' => 'nullable|integer|min:0',
        ];
    }
}
