<?php

namespace App\Http\Requests\PaywallActivity;

use App\Http\Requests\Request;

class UpdatePaywallActivityRequest extends Request
{
    public function rules()
    {
        return [
            'user_id' => 'sometimes|exists:users,id',
            'activity_type_id' => 'sometimes|exists:paywall_activity_types,id',
            'membership_gift_id' => 'nullable|exists:membership_gifts,id',
            'price' => 'sometimes|numeric|min:0',
            'gift_count_purchased' => 'sometimes|integer|min:0',
        ];
    }
} 