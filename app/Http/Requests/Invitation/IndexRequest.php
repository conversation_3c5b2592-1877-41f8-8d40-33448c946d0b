<?php

namespace App\Http\Requests\Invitation;

use App\Http\Requests\Request;

class IndexRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'from_user_id' => 'exists:users,id',
            'to_phone_number' => 'string',
            'to_user_id' => 'exists:users,id',

        ];
    }
}
