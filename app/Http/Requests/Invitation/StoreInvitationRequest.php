<?php

// app/Http/Requests/Invitation/StoreInvitationRequest.php

namespace App\Http\Requests\Invitation;

use App\Http\Requests\Request;

class StoreInvitationRequest extends Request
{
    public function rules()
    {
        return [
            'from_user_id' => 'required|exists:users,id',
            'to_phone_number' => 'required|string',
            'to_user_id' => 'nullable|exists:users,id',
        ];
    }
}
