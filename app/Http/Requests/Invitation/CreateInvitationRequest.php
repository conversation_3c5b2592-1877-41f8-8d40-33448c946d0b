<?php

namespace App\Http\Requests\Invitation;

use App\Http\Requests\Request;

class CreateInvitationRequest extends Request
{
    public function rules()
    {
        return [
            'to_phone_number' => 'required|string',
            'to_user_id' => 'nullable|exists:users,id',
            'name' => 'required|string',
            'name_as_from' => 'required|string',
            'name_as_outsider' => 'nullable|string',
        ];
    }
}
