<?php

namespace App\Http\Requests\MembershipType;

use App\Http\Requests\Request;
use App\Models\MembershipType;
use Illuminate\Validation\Rule;

class StoreMembershipTypeRequest extends Request
{
    public function rules()
    {
        return [
            'name' => ['required', 'string', Rule::in(MembershipType::ALL_TYPES)],
            'days_to_expire' => 'required|integer|min:1'
        ];
    }
} 
