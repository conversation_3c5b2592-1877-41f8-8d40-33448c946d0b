<?php

namespace App\Http\Requests\Relationship;

use App\Http\Requests\Request;

class StoreRelationshipRequest extends Request
{
    public function rules()
    {
        return [
            'name' => 'required|string',
            'from_user_id' => 'required|exists:users,id',
            'to_user_id' => 'required|exists:users,id',
            'reciprocal_relationship_id' => 'nullable|exists:relationships,id',
            'to_invitation_id' => 'nullable|exists:invitations,id',
            'is_hidden' => 'boolean',
            'name_as_from' => 'required|string',
            'name_as_outsider' => 'nullable|string',
        ];
    }
}
