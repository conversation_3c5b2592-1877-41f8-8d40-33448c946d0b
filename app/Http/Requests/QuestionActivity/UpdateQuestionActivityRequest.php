<?php

namespace App\Http\Requests\QuestionActivity;

use App\Http\Requests\Request;

class UpdateQuestionActivityRequest extends Request
{
    public function rules()
    {
        return [
            'question_id' => 'required|exists:questions,id',
            'question_status_id' => 'required|exists:question_statuses,id',
            'question_send_id' => 'required|exists:question_sends,id',
        ];
    }
}
