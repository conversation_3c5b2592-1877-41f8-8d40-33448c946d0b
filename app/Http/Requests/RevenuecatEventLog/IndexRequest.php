<?php

namespace App\Http\Requests\RevenuecatEventLog;

use App\Http\Requests\Request;

class IndexRequest extends Request
{
    public function rules()
    {
        return [
            'type' => 'sometimes|string',
            'status' => 'sometimes|in:PENDING,PROCESSED',
            'app_user_id' => 'sometimes|integer|exists:users,id',
            'external_id' => 'sometimes|string',
            'product_id' => 'sometimes|string',
            'store' => 'sometimes|string',
            'period_type' => 'sometimes|string',
        ];
    }
} 