<?php

namespace App\Http\Requests\RevenuecatEventLog;

use App\Http\Requests\Request;
use App\Models\RevenuecatEventLog;

class UpdateRevenuecatEventLogRequest extends Request
{
    public function rules()
    {
        $id = $this->route('revenuecat_event_log')?->id;
        return [
            'type' => 'nullable|string',
            'external_id' => 'nullable|string|unique:revenuecat_event_logs,external_id,' . $id,
            'status' => 'required|string|in:' . implode(',', RevenuecatEventLog::ALL_STATUSES),
            'app_user_id' => 'nullable|integer|exists:users,id',
            'product_id' => 'nullable|string',
            'store' => 'nullable|string',
            'price' => 'nullable|numeric',
            'period_type' => 'nullable|string',
            'data' => 'required|array',
        ];
    }
} 