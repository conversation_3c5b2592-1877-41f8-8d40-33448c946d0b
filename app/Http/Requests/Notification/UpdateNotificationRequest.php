<?php

namespace App\Http\Requests\Notification;

use App\Http\Requests\Request;

class UpdateNotificationRequest extends Request
{
    public function rules()
    {
        return [
            'courier_id' => 'required|string',
            'from_user_id' => 'required|exists:users,id',
            'to_user_id' => 'required|exists:users,id',
            'cta_type' => 'required|string',
            'question_id' => 'nullable|exists:questions,id',
            'video_id' => 'nullable|exists:videos,id',
            'seen_at' => 'nullable|date',
        ];
    }
}
