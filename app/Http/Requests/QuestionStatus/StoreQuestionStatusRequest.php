<?php

namespace App\Http\Requests\QuestionStatus;

use App\Models\QuestionStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreQuestionStatusRequest extends FormRequest
{
    public function rules()
    {
        return [
            'title' => ['required', 'string', Rule::in(QuestionStatus::ALL_STATUSES)],
            'description' => 'nullable|string',
        ];
    }
}
