<?php

namespace App\Http\Requests\ConnectionRequestStatus;

use App\Http\Requests\Request;
use App\Models\ConnectionRequestStatus;
use Illuminate\Validation\Rule;

class StoreConnectionRequestStatusRequest extends Request
{
  public function rules()
    {
        return [
            'title' => ['required', 'string', Rule::in(ConnectionRequestStatus::ALL_STATUSES)],
            'description' => 'nullable|string',
        ];
    }
}
