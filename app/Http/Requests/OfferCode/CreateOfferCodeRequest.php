<?php

namespace App\Http\Requests\OfferCode;

use App\Models\OfferCode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateOfferCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add your authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'code' => [
                'sometimes',
                'string',
                'max:50',
                'unique:offer_codes,code',
                'regex:/^[A-Z0-9]+$/' // Only uppercase letters and numbers
            ],
            'description' => 'nullable|string|max:500',
            'type' => [
                'required',
                'string',
                Rule::in(OfferCode::TYPES)
            ],
            'status' => [
                'sometimes',
                'string',
                Rule::in(OfferCode::STATUSES)
            ],
            'discount_percentage' => [
                'nullable',
                'numeric',
                'min:0',
                'max:100',
                'required_if:type,' . OfferCode::TYPE_DISCOUNT
            ],
            'discount_amount' => [
                'nullable',
                'numeric',
                'min:0',
                'required_if:type,' . OfferCode::TYPE_DISCOUNT
            ],
            'duration_num' => [
                'nullable',
                'integer',
                'min:1',
                'required_if:type,' . OfferCode::TYPE_MEMBERSHIP_EXTENSION . ',' . OfferCode::TYPE_FREE_TRIAL
            ],
            'duration_units' => [
                'nullable',
                'string',
                Rule::in(OfferCode::DURATION_UNITS),
                'required_if:type,' . OfferCode::TYPE_MEMBERSHIP_EXTENSION . ',' . OfferCode::TYPE_FREE_TRIAL
            ],
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.unique' => 'This offer code already exists.',
            'code.regex' => 'Offer code must contain only uppercase letters and numbers.',
            'type.required' => 'Offer code type is required.',
            'type.in' => 'Invalid offer code type.',
            'discount_percentage.required_if' => 'Discount percentage is required for discount type offers.',
            'discount_amount.required_if' => 'Discount amount is required for discount type offers.',
            'duration_num.required_if' => 'Duration number is required for membership extension and free trial offers.',
            'duration_units.required_if' => 'Duration units is required for membership extension and free trial offers.',
            'expires_at.after' => 'Expiration date must be in the future.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate discount fields based on type
            if ($this->input('type') === OfferCode::TYPE_DISCOUNT) {
                $discountPercentage = $this->input('discount_percentage');
                $discountAmount = $this->input('discount_amount');
                
                if (!$discountPercentage && !$discountAmount) {
                    $validator->errors()->add('discount', 'Either discount percentage or discount amount is required for discount offers.');
                }
                
                if ($discountPercentage && $discountAmount) {
                    $validator->errors()->add('discount', 'Cannot specify both discount percentage and discount amount.');
                }
            }

            // Validate duration fields for extension/trial types
            $extensionTypes = [OfferCode::TYPE_MEMBERSHIP_EXTENSION, OfferCode::TYPE_FREE_TRIAL];
            if (in_array($this->input('type'), $extensionTypes)) {
                if (!$this->input('duration_num') || !$this->input('duration_units')) {
                    $validator->errors()->add('duration', 'Duration number and units are required for this offer type.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-generate code if not provided
        if (!$this->has('code')) {
            $this->merge([
                'code' => $this->generateUniqueCode()
            ]);
        } else {
            // Ensure code is uppercase
            $this->merge([
                'code' => strtoupper($this->input('code'))
            ]);
        }

        // Set default status
        if (!$this->has('status')) {
            $this->merge([
                'status' => OfferCode::STATUS_ACTIVE
            ]);
        }
    }

    /**
     * Generate unique offer code
     */
    private function generateUniqueCode(): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        } while (OfferCode::where('code', $code)->exists());

        return $code;
    }
}
