<?php

namespace App\Http\Requests\OfferCode;

use App\Http\Requests\Request;

class StoreOfferCodeRequest extends Request
{
    public function rules(): array
    {
        return [
            'code' => 'required|string|max:50|unique:offer_codes,code',
            'description' => 'nullable|string|max:500',
            'type' => 'required|string|in:membership_extension,discount,free_trial',
            'status' => 'sometimes|string|in:active,inactive,expired',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'duration_num' => 'nullable|integer|min:1',
            'duration_units' => 'nullable|string|in:day,week,month,year',
            'max_uses' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
        ];
    }
}
