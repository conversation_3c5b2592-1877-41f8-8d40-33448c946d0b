<?php

namespace App\Http\Requests\OfferCode;

use App\Http\Requests\Request;
use App\Models\OfferCode;
use Illuminate\Validation\Rule;

class StoreOfferCodeRequest extends Request
{
    public function rules(): array
    {
        return [
            'code' => 'required|string|max:50|unique:offer_codes,code',
            'is_auto_renew' => 'sometimes|boolean',
            'title' => 'nullable|string|max:255',
            'regular_rate' => 'nullable|numeric|min:0',
            'revenue_cat_product_id' => 'nullable|string|max:255',
            'duration_num' => 'nullable|integer|min:1',
            'duration_units' => ['nullable', 'string', Rule::in(OfferCode::DURATION_UNITS)],
            'expires_at' => 'nullable|date|after:now',
        ];
    }
}
