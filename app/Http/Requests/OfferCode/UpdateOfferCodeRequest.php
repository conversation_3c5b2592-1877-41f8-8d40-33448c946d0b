<?php

namespace App\Http\Requests\OfferCode;

use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class UpdateOfferCodeRequest extends Request
{
    public function rules(): array
    {
        $offerCodeId = $this->route('offercode')->id ?? $this->route('offer_code')->id;

        return [
            'code' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('offer_codes', 'code')->ignore($offerCodeId)
            ],
            'is_auto_renew' => 'sometimes|boolean',
            'title' => 'sometimes|nullable|string|max:255',
            'regular_rate' => 'sometimes|nullable|numeric|min:0',
            'revenue_cat_product_id' => 'sometimes|nullable|string|max:255',
            'duration_num' => 'sometimes|nullable|integer|min:1',
            'duration_units' => ['sometimes', 'nullable', 'string', Rule::in(\App\Models\OfferCode::DURATION_UNITS)],
            'expires_at' => 'sometimes|nullable|date|after:now',
        ];
    }
}
