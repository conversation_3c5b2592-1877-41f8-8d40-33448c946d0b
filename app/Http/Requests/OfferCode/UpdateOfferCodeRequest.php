<?php

namespace App\Http\Requests\OfferCode;

use App\Models\OfferCode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOfferCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add your authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $offerCodeId = $this->route('offer_code')->id ?? $this->route('offerCode')->id;

        return [
            'code' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('offer_codes', 'code')->ignore($offerCodeId),
                'regex:/^[A-Z0-9]+$/' // Only uppercase letters and numbers
            ],
            'description' => 'sometimes|nullable|string|max:500',
            'type' => [
                'sometimes',
                'string',
                Rule::in(OfferCode::TYPES)
            ],
            'status' => [
                'sometimes',
                'string',
                Rule::in(OfferCode::STATUSES)
            ],
            'discount_percentage' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:100'
            ],
            'discount_amount' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:0'
            ],
            'duration_num' => [
                'sometimes',
                'nullable',
                'integer',
                'min:1'
            ],
            'duration_units' => [
                'sometimes',
                'nullable',
                'string',
                Rule::in(OfferCode::DURATION_UNITS)
            ],
            'max_uses' => 'sometimes|nullable|integer|min:1',
            'expires_at' => 'sometimes|nullable|date|after:now',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.unique' => 'This offer code already exists.',
            'code.regex' => 'Offer code must contain only uppercase letters and numbers.',
            'type.in' => 'Invalid offer code type.',
            'status.in' => 'Invalid offer code status.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'discount_amount.min' => 'Discount amount must be positive.',
            'duration_num.min' => 'Duration must be at least 1.',
            'max_uses.min' => 'Maximum uses must be at least 1.',
            'expires_at.after' => 'Expiration date must be in the future.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $offerCode = $this->route('offer_code') ?? $this->route('offerCode');
            
            // Get current type or use existing one
            $type = $this->input('type', $offerCode->type);

            // Validate discount fields based on type
            if ($type === OfferCode::TYPE_DISCOUNT) {
                $discountPercentage = $this->input('discount_percentage', $offerCode->discount_percentage);
                $discountAmount = $this->input('discount_amount', $offerCode->discount_amount);
                
                if (!$discountPercentage && !$discountAmount) {
                    $validator->errors()->add('discount', 'Either discount percentage or discount amount is required for discount offers.');
                }
                
                if ($discountPercentage && $discountAmount) {
                    $validator->errors()->add('discount', 'Cannot specify both discount percentage and discount amount.');
                }
            }

            // Validate duration fields for extension/trial types
            $extensionTypes = [OfferCode::TYPE_MEMBERSHIP_EXTENSION, OfferCode::TYPE_FREE_TRIAL];
            if (in_array($type, $extensionTypes)) {
                $durationNum = $this->input('duration_num', $offerCode->duration_num);
                $durationUnits = $this->input('duration_units', $offerCode->duration_units);
                
                if (!$durationNum || !$durationUnits) {
                    $validator->errors()->add('duration', 'Duration number and units are required for this offer type.');
                }
            }

            // Prevent reducing max_uses below current usage
            if ($this->has('max_uses') && $this->input('max_uses')) {
                $currentUsage = $offerCode->used_count ?? 0;
                if ($this->input('max_uses') < $currentUsage) {
                    $validator->errors()->add('max_uses', "Maximum uses cannot be less than current usage ({$currentUsage}).");
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure code is uppercase if provided
        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper($this->input('code'))
            ]);
        }
    }
}
