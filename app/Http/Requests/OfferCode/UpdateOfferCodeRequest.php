<?php

namespace App\Http\Requests\OfferCode;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOfferCodeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $offerCodeId = $this->route('offercode')->id ?? $this->route('offer_code')->id;

        return [
            'code' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('offer_codes', 'code')->ignore($offerCodeId)
            ],
            'description' => 'sometimes|nullable|string|max:500',
            'type' => 'sometimes|string|in:membership_extension,discount,free_trial',
            'status' => 'sometimes|string|in:active,inactive,expired',
            'discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'discount_amount' => 'sometimes|nullable|numeric|min:0',
            'duration_num' => 'sometimes|nullable|integer|min:1',
            'duration_units' => 'sometimes|nullable|string|in:day,week,month,year',
            'max_uses' => 'sometimes|nullable|integer|min:1',
            'expires_at' => 'sometimes|nullable|date|after:now',
        ];
    }
}
