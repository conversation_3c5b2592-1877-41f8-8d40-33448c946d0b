<?php

namespace App\Http\Requests\OfferCode;

use Illuminate\Foundation\Http\FormRequest;

class ApplyOfferCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check(); // User must be authenticated
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                'max:50',
                'regex:/^[A-Z0-9]+$/' // Only uppercase letters and numbers
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.required' => 'Offer code is required.',
            'code.string' => 'Offer code must be a string.',
            'code.max' => 'Offer code cannot exceed 50 characters.',
            'code.regex' => 'Offer code must contain only uppercase letters and numbers.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure code is uppercase and trimmed
        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper(trim($this->input('code')))
            ]);
        }
    }
}
