<?php

namespace App\Http\Requests\ConnectionRequest;

use App\Http\Requests\Request;

class CreateOrUpdateConnectionRequest extends Request
{

    public function rules()
    {
        return [
            'connection_request_status' => 'required|string',
            'from_user_id' => 'required|exists:users,id',
            'to_user_id' => 'nullable | exists:users,id',
            'name_as_from' => 'nullable|string',
            'name_as_outsider' => 'nullable|string',
        ];
    }
}
