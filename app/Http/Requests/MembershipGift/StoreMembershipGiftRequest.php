<?php

namespace App\Http\Requests\MembershipGift;

use App\Http\Requests\Request;

class StoreMembershipGiftRequest extends Request
{
    public function rules()
    {
        return [
            'to_user_id' => 'nullable|exists:users,id',
            'invitation_id' => 'nullable|exists:invitations,id',
            'from_user_id' => 'nullable|exists:users,id',
            'sent_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:sent_at',
            'accepted_at' => 'nullable|date|after:sent_at',
            'rejected_at' => 'nullable|date|after:sent_at'
        ];
    }
}
