<?php

namespace App\Http\Requests\MembershipGift;

use App\Http\Requests\Request;

class IndexRequest extends Request
{
    public function rules(): array
    {
        return [
            'to_user_id' => 'exists:users,id',
            'from_user_id' => 'exists:users,id',
            'sent_at' => 'date',
            'accepted_at' => 'date',
            'rejected_at' => 'date',
            'expires_at' => 'date',
            'invitation_id' => 'exists:invitations,id',
        ];
    }
}
