<?php

namespace App\Http\Requests\MembershipGift;

use App\Http\Requests\Request;

class UpdateMembershipGiftRequest extends Request
{
    public function rules()
    {
        return [
            'accepted_at' => 'nullable|date|after:sent_at',
            'rejected_at' => 'nullable|date|after:sent_at',
            'sent_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:sent_at',
            'invitation_id' => 'nullable|exists:invitations,id',
        ];
    }
} 