<?php

namespace App\Http\Controllers;

use App\Http\Requests\VideoFollowupQuestion\IndexRequest;
use App\Http\Requests\VideoFollowupQuestion\StoreVideoFollowupQuestionRequest;
use App\Http\Requests\VideoFollowupQuestion\UpdateVideoFollowupQuestionRequest;
use App\Http\Resources\VideoFollowupQuestionResource;
use App\Http\Resources\VideoFollowupQuestionResourceCollection;
use App\Models\VideoFollowupQuestion;
use App\Repositories\Contracts\VideoFollowupQuestionRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class VideoFollowupQuestionController extends Controller
{
    protected $videoFollowupQuestionRepository;

    public function __construct(VideoFollowupQuestionRepository $videoFollowupQuestionRepository)
    {
        $this->videoFollowupQuestionRepository = $videoFollowupQuestionRepository;
    }

    public function index(IndexRequest $request)
    {
        $followupQuestions = $this->videoFollowupQuestionRepository->findBy($request?->all());
        return new VideoFollowupQuestionResourceCollection($followupQuestions);
    }

    public function store(StoreVideoFollowupQuestionRequest $request)
    {
        $followupQuestion = $this->videoFollowupQuestionRepository->save($request->all());
        return new VideoFollowupQuestionResource($followupQuestion);
    }

    public function update(UpdateVideoFollowupQuestionRequest $request, VideoFollowupQuestion $videoFollowupQuestion)
    {
        $followupQuestion = $this->videoFollowupQuestionRepository->update($videoFollowupQuestion, $request->all());
        return new VideoFollowupQuestionResource($followupQuestion);
    }

    public function show($id)
    {
        $followupQuestion = $this->videoFollowupQuestionRepository->findOne($id);
        if ($followupQuestion === null) {
            throw new ModelNotFoundException();
        }
        return new VideoFollowupQuestionResource($followupQuestion);
    }

    public function destroy(VideoFollowupQuestion $videoFollowupQuestion)
    {
        $this->videoFollowupQuestionRepository->delete($videoFollowupQuestion);
        return response()->json(null, 204);
    }
} 