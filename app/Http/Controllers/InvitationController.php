<?php

namespace App\Http\Controllers;

use App\Http\Requests\Invitation\CreateInvitationRequest;
use App\Http\Requests\Invitation\DeleteInvitationRequest;
use App\Http\Requests\Invitation\IndexRequest;
use App\Http\Requests\Invitation\StoreInvitationRequest;
use App\Http\Requests\Invitation\UpdateInvitationRequest;
use App\Http\Resources\InvitationResource;
use App\Http\Resources\InvitationResourceCollection;
use App\Models\Invitation;
use App\Repositories\Contracts\InvitationRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class InvitationController extends Controller
{
    protected $invitationRepository;

    public function __construct(InvitationRepository $invitationRepository)
    {
        $this->invitationRepository = $invitationRepository;

    }

    public function index(IndexRequest $request)
    {
        $requestData = $request->all();
        // If phone number is provided, prepend '+' sign
        $phoneNumber = $requestData['to_phone_number'] ?? null;
        if ($phoneNumber !== null) {
            $phoneNumber = '+' . $phoneNumber;
        }
        // Find user based on phone number if provided, otherwise get all users
        $invitations = $phoneNumber ? $this->invitationRepository->findBy(['to_phone_number' => $phoneNumber]) : $this->invitationRepository->findBy($requestData);

        return new InvitationResourceCollection($invitations);
    }

    public function store(StoreInvitationRequest $request)
    {
        $invitation = $this->invitationRepository->save($request->all());

        return new InvitationResource($invitation);
    }

    public function update(UpdateInvitationRequest $request, Invitation $invitation)
    {

        $invitation = $this->invitationRepository->update($invitation, $request->all());
        return new InvitationResource($invitation);
    }

    public function show($id)
    {
        $invitation = $this->invitationRepository->findOne($id);
        if ($invitation === null) {
            throw new ModelNotFoundException();
        }

        return new InvitationResource($invitation);
    }

    public function destroy(Invitation $invitation)
    {
        $this->invitationRepository->delete($invitation);

        return response()->json(null, 204);
    }

    public function create(CreateInvitationRequest $request)
    {
        // Validate the request
        $requestData = $request->validated();

        // Create Invitation and Relationship
        $invitation = $this->invitationRepository->createInvitationAndRelationship($requestData);

        return new InvitationResource($invitation);
    }
    public function delete(DeleteInvitationRequest $request)
    {
        $requestData = $request->validated();

        $this->invitationRepository->deleteInvitation($requestData);

        return response()->json(null, 204);
    }
}
