<?php

namespace App\Http\Controllers;

use App\Http\Requests\PaywallActivityType\StorePaywallActivityTypeRequest;
use App\Http\Requests\PaywallActivityType\UpdatePaywallActivityTypeRequest;
use App\Http\Requests\PaywallActivityType\IndexRequest;
use App\Http\Resources\PaywallActivityTypeResource;
use App\Http\Resources\PaywallActivityTypeResourceCollection;
use App\Models\PaywallActivityType;
use App\Repositories\Contracts\PaywallActivityTypeRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PaywallActivityTypeController extends Controller
{
    protected $paywallActivityTypeRepository;

    public function __construct(PaywallActivityTypeRepository $paywallActivityTypeRepository)
    {
        $this->paywallActivityTypeRepository = $paywallActivityTypeRepository;
    }

    public function index(IndexRequest $request)
    {
        $types = $this->paywallActivityTypeRepository->findBy($request->all());
        return new PaywallActivityTypeResourceCollection($types);
    }

    public function store(StorePaywallActivityTypeRequest $request)
    {
        $type = $this->paywallActivityTypeRepository->save($request->all());
        return new PaywallActivityTypeResource($type);
    }

    public function show($id)
    {
        $type = $this->paywallActivityTypeRepository->findOne($id);
        if ($type === null) {
            throw new ModelNotFoundException();
        }
        return new PaywallActivityTypeResource($type);
    }

    public function update(UpdatePaywallActivityTypeRequest $request, PaywallActivityType $paywallActivityType)
    {
        $type = $this->paywallActivityTypeRepository->update($paywallActivityType, $request->all());
        return new PaywallActivityTypeResource($type);
    }

    public function destroy(PaywallActivityType $paywallActivityType)
    {
        $this->paywallActivityTypeRepository->delete($paywallActivityType);
        return response()->json(null, 204);
    }
} 