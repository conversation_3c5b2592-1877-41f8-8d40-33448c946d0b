<?php

namespace App\Http\Controllers;

use App\Events\QuestionAnswered;
use App\Events\QuestionSent;
use App\Http\Requests\QuestionSend\IndexRequest;
use App\Http\Requests\QuestionSend\StoreQuestionSendRequest;
use App\Http\Requests\QuestionSend\UpdateQuestionSendRequest;
use App\Http\Requests\Video\HomeFeedRequest;
use App\Http\Resources\QuestionSendInboxOrOutboxResource;
use App\Http\Resources\QuestionSendResource;
use App\Http\Resources\QuestionSendResourceCollection;
use App\Models\QuestionSend;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionActivityRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class QuestionSendController extends Controller
{
    protected $questionSendRepository;
    protected $notificationRepository;
    protected $questionActivityRepository;

    public function __construct(
        QuestionSendRepository $questionSendRepository,
        NotificationRepository $notificationRepository,
        QuestionActivityRepository $questionActivityRepository
    ) {
        $this->questionSendRepository = $questionSendRepository;
        $this->notificationRepository = $notificationRepository;
        $this->questionActivityRepository = $questionActivityRepository;
    }

    public function index(IndexRequest $request)
    {
        $questionSends = $this->questionSendRepository->findBy($request?->all());

        return new QuestionSendResourceCollection($questionSends);
    }

    public function store(StoreQuestionSendRequest $request)
    {
        $questionSend = $this->questionSendRepository->create($request->all());

        if (!is_null($questionSend->sent_from_user_id)) {
            event(new QuestionSent($questionSend));
        }

        return new QuestionSendResource($questionSend);
    }

    public function update(UpdateQuestionSendRequest $request, QuestionSend $questionSend)
    {
        $data = $request->all();
        $questionStatus = $data['question_status'] ?? null;

        if (is_null($questionStatus)) {
            $updatedQuestionSend = $this->questionSendRepository->update($questionSend, $data);
        } else {
            $updatedQuestionSend = $this->questionSendRepository->updateQuestionSendAndActivity($questionSend, $data);

            // Only trigger QuestionAnswered for answer status
            if ($questionStatus === 'answered') {
                event(new QuestionAnswered($updatedQuestionSend));
            }
        }

        return new QuestionSendResource($updatedQuestionSend);
    }

    public function show($id)
    {
        $questionSend = $this->questionSendRepository->findOne($id);
        if ($questionSend === null) {
            throw new ModelNotFoundException();
        }

        return new QuestionSendResource($questionSend);
    }

    public function destroy(QuestionSend $questionSend)
    {
        $this->questionSendRepository->delete($questionSend);

        return response()->json(null, 204);
    }

    public function getInboxOutbox(IndexRequest $request)
    {
        $questionSends = $this->questionSendRepository->findByConnectedPeopleQuestionActivity($request->all());

        $resourceCollection = $questionSends->map(function ($send) {

            return new QuestionSendInboxOrOutboxResource($send);
        });

        return QuestionSendInboxOrOutboxResource::collection($resourceCollection);
    }

    public function getCounts(HomeFeedRequest $request)
    {
        $userId = $request->query('userId', Auth::user()->id);

        $inboxCount = $this->questionSendRepository->findBy(['sent_to_user_id' => $userId, 'video_id' => null])->count();
        $outboxCount = $this->questionSendRepository->findBy(['sent_from_user_id' => $userId])->count();

        return response()->json([
            'inbox_count' => $inboxCount,
            'outbox_count' => $outboxCount,
        ]);
    }

    public function discard(QuestionSend $questionSend)
    {
        $currentTime = now();

        $updatedQuestionSend = $this->questionSendRepository->update($questionSend, [
            'trashed_at' => $currentTime,
        ]);

        return new QuestionSendResource($updatedQuestionSend);
    }
}
