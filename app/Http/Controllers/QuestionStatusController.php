<?php

namespace App\Http\Controllers;

use App\Http\Requests\QuestionStatus\StoreQuestionStatusRequest;
use App\Http\Requests\QuestionStatus\UpdateQuestionStatusRequest;
use App\Http\Resources\QuestionStatusResource;
use App\Http\Resources\QuestionStatusResourceCollection;
use App\Models\QuestionStatus;
use App\Repositories\Contracts\QuestionStatusRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class QuestionStatusController extends Controller
{
    protected $questionStatusRepository;

    public function __construct(QuestionStatusRepository $questionStatusRepository)
    {
        $this->questionStatusRepository = $questionStatusRepository;
    }

    public function index()
    {
        $questionStatus = $this->questionStatusRepository->findBy();

        return new QuestionStatusResourceCollection($questionStatus);
    }

    public function store(StoreQuestionStatusRequest $request)
    {
        $questionStatus = $this->questionStatusRepository->save($request->all());

        return new QuestionStatusResource($questionStatus);
    }

    public function update(UpdateQuestionStatusRequest $request, QuestionStatus $questionStatus)
    {

        $questionStatus = $this->questionStatusRepository->update($questionStatus, $request->all());
        return new QuestionStatusResource($questionStatus);
    }

    public function show($id)
    {
        $questionStatus = $this->questionStatusRepository->findOne($id);
        if ($questionStatus === null) {
            throw new ModelNotFoundException();
        }

        return new QuestionStatusResource($questionStatus);
    }

    public function destroy(QuestionStatus $questionStatus)
    {
        $this->questionStatusRepository->delete($questionStatus);

        return response()->json(null, 204);
    }
}
