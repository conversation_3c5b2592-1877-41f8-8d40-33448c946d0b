<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Membership;
use App\Events\MembershipNotification;
use App\Models\MembershipType;
use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\MembershipTypeRepository;
use App\Repositories\Contracts\UserRepository;
use App\Repositories\Contracts\RevenuecatEventLogRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\RevenuecatEventLog;

class RevenueCatWebhookController extends Controller
{
    protected $userRepository;
    protected $membershipRepository;
    protected $membershipTypeRepository;
    protected $revenuecatEventLogRepository;

    /**
     * @param UserRepository $userRepository
     * @param MembershipRepository $membershipRepository
     * @param MembershipTypeRepository $membershipTypeRepository
     */
    public function __construct(UserRepository $userRepository, MembershipRepository $membershipRepository, MembershipTypeRepository $membershipTypeRepository, RevenuecatEventLogRepository $revenuecatEventLogRepository)
    {
        $this->userRepository = $userRepository;
        $this->membershipRepository = $membershipRepository;
        $this->membershipTypeRepository = $membershipTypeRepository;
        $this->revenuecatEventLogRepository = $revenuecatEventLogRepository;
    }

    /**
     * Handles RevenueCat webhooks.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Request $request)
    {
        try {
            // if (env('APP_ENV') !== 'production') {
            //     // Debugging to Slack
            //     Http::post(config('services.slack.rev_event_webhook_url'), [
            //         'text' => "🔔 RevenueCat Webhook Received:\n```" . json_encode($request->all(), JSON_PRETTY_PRINT) . "```"
            //     ]);
            // }

            $payload = $request->all();
            $event = $payload['event'];
            $eventType = $event['type'];

            // Always create a log with status PENDING
            $log = $this->revenuecatEventLogRepository->save([
                'type' => $eventType ?? null,
                'external_id' => $event['id'] ?? null,
                'status' => RevenuecatEventLog::STATUS_PENDING,
                'app_user_id' => $event['app_user_id'] ?? null,
                'product_id' => $event['product_id'] ?? null,
                'store' => $event['store'] ?? null,
                'price' => $event['price'] ?? null,
                'period_type' => $event['period_type'] ?? null,
                'data' => $payload,
            ]);

            // User Identify
            $userId = $event['app_user_id'];
            $user = $this->userRepository->findOne($userId);

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            $processed = false;
            switch ($eventType) {
                case 'INITIAL_PURCHASE':
                    $this->handleInitialPurchase($user, $event);
                    $processed = true;
                    break;
                case 'RENEWAL':
                    $this->handleRenewal($user, $event);
                    $processed = true;
                    break;
                case 'CANCELLATION':
                    $this->handleSubscriptionCancelled($user, $event);
                    $processed = true;
                    break;
                case 'UNCANCELLATION':
                    $this->handleSubscriptionUncancelled($user, $event);
                    $processed = true;
                    break;
                case 'EXPIRATION':
                    $this->handleSubscriptionExpired($user, $event);
                    $processed = true;
                    break;
                case 'BILLING_ISSUE':
                    $this->handleBillingIssue($user, $event);
                    $processed = true;
                    break;
                case 'SUBSCRIPTION_PAUSED':
                    $this->handleSubscriptionPaused($user, $event);
                    $processed = true;
                    break;
                default:
                    Log::info("Unhandled RevenueCat event type: {$eventType}");
            }

            // If processed, update log status to PROCESSED
            if ($processed) {
                $this->revenuecatEventLogRepository->update($log, ['status' => RevenuecatEventLog::STATUS_PROCESSED]);
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle the INITIAL_PURCHASE event from RevenueCat.
     *
     * This function creates a new membership for the user with the given event data.
     * If the user already has a membership, it updates the existing one.
     *
     * @param User $user The user who triggered the event
     * @param array $event The event data from RevenueCat
     * @return Membership The newly created or updated membership
     */
    protected function handleInitialPurchase($user, $event)
    {

        if (env('APP_ENV') !== 'production') {
            Http::post(config('services.slack.rev_event_webhook_url'), [
                'text' => "🔔 RevenueCat Initial Purchase Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
            ]);
        }

        $membershipType = match ($event['period_type']) {
            'TRIAL' => $this->membershipTypeRepository->findOneBy(['name' => MembershipType::TRIAL_MEMBERSHIP]),
            'NORMAL' => $this->membershipTypeRepository->findOneBy(['name' => MembershipType::FULL_MEMBERSHIP]),
            default => $this->membershipTypeRepository->findOneBy(['name' => MembershipType::TRIAL_MEMBERSHIP])
        };

        if ($user->membership) {
            $membership = $this->membershipRepository->update($user->membership, [
                'membership_type_id' => $membershipType->id,
                'started_at' => Carbon::createFromTimestampMs($event['purchased_at_ms']),
                'end_at' => Carbon::createFromTimestampMs($event['expiration_at_ms']),
                'is_autopay_enabled' => true,
                'available_gifts_count' => 3
            ]);
        } else {
            $membership = $this->membershipRepository->save([
                'membership_type_id' => $membershipType->id,
                'started_at' => Carbon::createFromTimestampMs($event['purchased_at_ms']),
                'end_at' => Carbon::createFromTimestampMs($event['expiration_at_ms']),
                'is_autopay_enabled' => true,
                'available_gifts_count' => 3
            ]);

            $this->userRepository->update($user, [
                'membership_id' => $membership->id
            ]);
        }

        return $membership;
    }

    /**
     * Handles the Renewal event from RevenueCat.
     *
     * @param User $user
     * @param array $event
     * @return void
     */
    protected function handleRenewal($user, $event)
    {
        if (env('APP_ENV') !== 'production') {
        Http::post(config('services.slack.rev_event_webhook_url'), [
                'text' => "🔔 RevenueCat Renewal Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
            ]);
        }

        if ($membership) {

            $membershipType = $this->membershipTypeRepository->findOneBy(['name' => MembershipType::FULL_MEMBERSHIP]);

            $this->membershipRepository->update($membership, [
                'membership_type_id' => $membershipType->id,
                'started_at' => Carbon::createFromTimestampMs($event['purchased_at_ms']),
                'end_at' => Carbon::createFromTimestampMs($event['expiration_at_ms']),
                'is_autopay_enabled' => true
            ]);
        }
    }


    /**
     * Handles the Cancellation event from RevenueCat.
     *
     * @param User $user
     * @param array $event
     * @return void
     */

    protected function handleSubscriptionCancelled($user, $event)
    {
        if (env('APP_ENV') !== 'production') {
            Http::post(config('services.slack.rev_event_webhook_url'), [
                    'text' => "🔔 RevenueCat CANCELLATION  Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
                ]);
        }

        $membership = $user->membership;

        if ($membership) {
            $updateData = [
                'is_autopay_enabled' => false,
            ];

            // If it's a trial membership, delete user gifts and set available_gifts_count to 0
            if ($membership->membershipType->name === MembershipType::TRIAL_MEMBERSHIP) {
                $user->sentGifts()->delete();
                $updateData['available_gifts_count'] = 0;
            }

            $this->membershipRepository->update($membership, $updateData);

            // Send downgrade notification
            event(new MembershipNotification(
                'membership_downgraded',
                $membership,
                null,
                $this->getSubscriptionCost($event)
            ));
        }
    }

    /**
     * Handles the Subscription Expired event from RevenueCat.
     *
     * This function updates the user's membership to disable autopay when a subscription expires.
     *
     * @param User $user The user whose subscription has expired
     * @param array $event The event data from RevenueCat
     * @return void
     */

    protected function handleSubscriptionExpired($user, $event)
    {
        $membership = $user->membership;
        if ($membership) {
            $this->membershipRepository->update($membership, [
                'is_autopay_enabled' => false
            ]);
        }
    }

    /**
     * Handles the Uncancellation event from RevenueCat.
     *
     * This function updates the user's membership to re-enable autopay when a subscription is uncancelled.
     * It also sends a notification to a Slack channel if the application is not in production mode.
     *
     * @param User $user The user whose subscription has been uncancelled
     * @param array $event The event data from RevenueCat
     * @return void
     */

    protected function handleSubscriptionUncancelled($user, $event)
    {
        if (env('APP_ENV') !== 'production') {
            Http::post(config('services.slack.rev_event_webhook_url'), [
                'text' => "🔔 RevenueCat Uncancellation Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
            ]);
        }

        $membership = $user->membership;
        if ($membership) {
            $this->membershipRepository->update($membership, [
                'is_autopay_enabled' => true,
            ]);
        }
    }

    protected function handleBillingIssue($user, $event)
    {
        // TODO: Handle billing issue by updating membership status and sending payment failure notifications


        if (env('APP_ENV') !== 'production') {
            Http::post(config('services.slack.rev_event_webhook_url'), [
                    'text' => "🔔 RevenueCat BILLING_ISSUE  Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
                ]);
            }
    }

    protected function handleSubscriptionPaused($user, $event)
    {
        //TODO: Handle subscription pause by updating membership status

        if (env('APP_ENV') !== 'production') {
            Http::post(config('services.slack.rev_event_webhook_url'), [
                    'text' => "🔔 RevenueCat SUBSCRIPTION_PAUSED  Webhook Received:\n```" . json_encode($event, JSON_PRETTY_PRINT) . "```"
                ]);
            }
    }

    protected function getSubscriptionCost($event)
    {
        return $event['price'] ?? null;
    }
}
