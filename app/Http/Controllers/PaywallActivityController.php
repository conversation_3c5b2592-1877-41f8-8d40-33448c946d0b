<?php

namespace App\Http\Controllers;

use App\Http\Requests\PaywallActivity\StorePaywallActivityRequest;
use App\Http\Requests\PaywallActivity\UpdatePaywallActivityRequest;
use App\Http\Requests\PaywallActivity\IndexRequest;
use App\Http\Resources\PaywallActivityResource;
use App\Http\Resources\PaywallActivityResourceCollection;
use App\Models\PaywallActivity;
use App\Repositories\Contracts\PaywallActivityRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class PaywallActivityController extends Controller
{
    protected $paywallActivityRepository;

    public function __construct(PaywallActivityRepository $paywallActivityRepository)
    {
        $this->paywallActivityRepository = $paywallActivityRepository;
    }

    public function index(IndexRequest $request)
    {
        $activities = $this->paywallActivityRepository->findBy($request->all());
        return new PaywallActivityResourceCollection($activities);
    }

    public function store(StorePaywallActivityRequest $request)
    {
        $data = $request->all();
        $data['user_id'] = Auth::user()->id; // Always use the authenticated user's ID
        $activity = $this->paywallActivityRepository->save($data);

        // If this is a paid activity and gift_count_purchased > 0, increment available_gifts_count
        if (!is_null($activity->price) && $activity->gift_count_purchased > 0) {
            $authUser = Auth::user();
            if ($authUser && $authUser->membership) {
                $authUser->membership->increment('available_gifts_count', $activity->gift_count_purchased);
            }
        }

        return new PaywallActivityResource($activity);
    }

    public function show($id)
    {
        $activity = $this->paywallActivityRepository->findOne($id);
        if ($activity === null) {
            throw new ModelNotFoundException();
        }
        return new PaywallActivityResource($activity);
    }

    public function update(UpdatePaywallActivityRequest $request, PaywallActivity $paywallActivity)
    {
        $activity = $this->paywallActivityRepository->update($paywallActivity, $request->all());
        return new PaywallActivityResource($activity);
    }

    public function destroy(PaywallActivity $paywallActivity)
    {
        $this->paywallActivityRepository->delete($paywallActivity);
        return response()->json(null, 204);
    }
}
