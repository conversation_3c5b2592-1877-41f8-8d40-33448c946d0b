<?php

namespace App\Http\Controllers;

use App\Http\Requests\QuestionActivity\StoreQuestionActivityRequest;
use App\Http\Requests\QuestionActivity\UpdateQuestionActivityRequest;
use App\Http\Resources\QuestionActivityResource;
use App\Http\Resources\QuestionActivityResourceCollection;
use App\Models\QuestionActivity;
use App\Repositories\Contracts\QuestionActivityRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class QuestionActivityController extends Controller
{
    protected $questionActivityRepository;

    public function __construct(QuestionActivityRepository $questionActivityRepository)
    {
        $this->questionActivityRepository = $questionActivityRepository;
    }

    public function index()
    {
        $questionActivities = $this->questionActivityRepository->findBy();

        return new QuestionActivityResourceCollection($questionActivities);
    }

    public function store(StoreQuestionActivityRequest $request)
    {
        $questionActivity = $this->questionActivityRepository->save($request->all());

        return new QuestionActivityResource($questionActivity);
    }

    public function update(UpdateQuestionActivityRequest $request, QuestionActivity $questionActivity)
    {
        $questionActivity = $this->questionActivityRepository->update($questionActivity, $request->all());

        return new QuestionActivityResource($questionActivity);
    }

    public function show($id)
    {
        $questionActivity = $this->questionActivityRepository->findOne($id);

        if ($questionActivity === null) {
            throw new ModelNotFoundException();
        }

        return new QuestionActivityResource($questionActivity);
    }

    public function destroy(QuestionActivity $questionActivity)
    {
        $this->questionActivityRepository->delete($questionActivity);

        return response()->json(null, 204);
    }
}
