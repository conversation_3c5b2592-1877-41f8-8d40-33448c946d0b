<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferCodeUser\IndexRequest;
use App\Http\Requests\OfferCodeUser\StoreOfferCodeUserRequest;
use App\Http\Requests\OfferCodeUser\UpdateOfferCodeUserRequest;
use App\Http\Resources\OfferCodeUserResource;
use App\Http\Resources\OfferCodeUserResourceCollection;
use App\Models\OfferCodeUser;
use App\Repositories\Contracts\OfferCodeUserRepository;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class OfferCodeUserController extends Controller
{
    protected $offerCodeUserRepository;
    protected $userRepository;
    protected $offerCodeRepository;

    public function __construct(
        OfferCodeUserRepository $offerCodeUserRepository,
        UserRepository $userRepository,
        OfferCodeRepository $offerCodeRepository
    ) {
        $this->offerCodeUserRepository = $offerCodeUserRepository;
        $this->userRepository = $userRepository;
        $this->offerCodeRepository = $offerCodeRepository;
    }

    public function index(IndexRequest $request)
    {
        $offerCodeUsers = $this->offerCodeUserRepository->findBy($request->all());
        return new OfferCodeUserResourceCollection($offerCodeUsers);
    }

    public function store(StoreOfferCodeUserRequest $request)
    {
        $user = $this->userRepository->findOne(Auth::user()->id);
        $offerCode = $this->offerCodeRepository->findOne($request['offer_code_id']);

        $data = $request->all();
        $data['user_id'] = $user->id;
        $data['redeemed_at'] = now();

        $offerCodeUser = $this->offerCodeUserRepository->save($data);

        // Calculate duration and update membership
        $this->updateUserMembership($user, $offerCode);

        return new OfferCodeUserResource($offerCodeUser);
    }

    private function updateUserMembership($user, $offerCode)
    {
        $startDate = now();
        $endDate = $this->calculateEndDate($startDate, $offerCode->duration_num, $offerCode->duration_units);

        $membershipData = [
            'started_at' => $startDate->toIso8601String(),
            'end_at' => $endDate->toIso8601String(),
            'is_autopay_enabled' => $offerCode->is_auto_renew,
        ];

        // Add regular rate if auto-renew is enabled
        if ($offerCode->is_auto_renew && $offerCode->regular_rate) {
            $membershipData['regular_rate'] = $offerCode->regular_rate;
        }

        if ($user->membership) {
            // Update existing membership
            $this->membershipRepository->update($user->membership, $membershipData);
        } else {
            // Create new membership - need to add membership_type_id
            $membershipData['membership_type_id'] = 1; // Default or get from offer code
            $membership = $this->membershipRepository->save($membershipData);

            $this->userRepository->update($user, [
                'membership_id' => $membership->id
            ]);
        }
    }

    private function calculateEndDate($startDate, $durationNum, $durationUnits)
    {
        switch ($durationUnits) {
            case 'day':
                return $startDate->copy()->addDays($durationNum);
            case 'week':
                return $startDate->copy()->addWeeks($durationNum);
            case 'month':
                return $startDate->copy()->addMonths($durationNum);
            case 'year':
                return $startDate->copy()->addYears($durationNum);
            default:
                return $startDate->copy()->addMonths($durationNum);
        }
    }

    public function show($id)
    {
        $offerCodeUser = $this->offerCodeUserRepository->findOne($id);
        if ($offerCodeUser === null) {
            throw new ModelNotFoundException();
        }
        return new OfferCodeUserResource($offerCodeUser);
    }

    public function update(UpdateOfferCodeUserRequest $request, OfferCodeUser $offerCodeUser)
    {
        $offerCodeUser = $this->offerCodeUserRepository->update($offerCodeUser, $request->all());
        return new OfferCodeUserResource($offerCodeUser);
    }

    public function destroy(OfferCodeUser $offerCodeUser)
    {
        $this->offerCodeUserRepository->delete($offerCodeUser);
        return response()->json(null, 204);
    }
}
