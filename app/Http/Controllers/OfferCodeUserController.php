<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferCodeUser\IndexRequest;
use App\Http\Requests\OfferCodeUser\StoreOfferCodeUserRequest;
use App\Http\Requests\OfferCodeUser\UpdateOfferCodeUserRequest;
use App\Http\Resources\OfferCodeUserResource;
use App\Http\Resources\OfferCodeUserResourceCollection;
use App\Models\OfferCodeUser;
use App\Repositories\Contracts\OfferCodeUserRepository;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class OfferCodeUserController extends Controller
{
    protected $offerCodeUserRepository;
    protected $userRepository;
    protected $offerCodeRepository;

    public function __construct(
        OfferCodeUserRepository $offerCodeUserRepository,
        UserRepository $userRepository,
        OfferCodeRepository $offerCodeRepository
    ) {
        $this->offerCodeUserRepository = $offerCodeUserRepository;
        $this->userRepository = $userRepository;
        $this->offerCodeRepository = $offerCodeRepository;
    }

    public function index(IndexRequest $request)
    {
        $offerCodeUsers = $this->offerCodeUserRepository->findBy($request->all());
        return new OfferCodeUserResourceCollection($offerCodeUsers);
    }

    public function store(StoreOfferCodeUserRequest $request)
    {
        $user = $this->userRepository->findOne(Auth::user()->id);
        $offerCode = $this->offerCodeRepository->findOne($request['offer_code_id']);

        $data = $request->all();
        $data['user_id'] = $user->id;
        $data['redeemed_at'] = now();

        $offerCodeUser = $this->offerCodeUserRepository->save($data);

        return new OfferCodeUserResource($offerCodeUser);
    }

    public function show($id)
    {
        $offerCodeUser = $this->offerCodeUserRepository->findOne($id);
        if ($offerCodeUser === null) {
            throw new ModelNotFoundException();
        }
        return new OfferCodeUserResource($offerCodeUser);
    }

    public function update(UpdateOfferCodeUserRequest $request, OfferCodeUser $offerCodeUser)
    {
        $offerCodeUser = $this->offerCodeUserRepository->update($offerCodeUser, $request->all());
        return new OfferCodeUserResource($offerCodeUser);
    }

    public function destroy(OfferCodeUser $offerCodeUser)
    {
        $this->offerCodeUserRepository->delete($offerCodeUser);
        return response()->json(null, 204);
    }
}
