<?php

namespace App\Http\Controllers;

use App\Events\MembershipNotification;
use App\Http\Requests\MembershipGift\IndexRequest;
use App\Http\Requests\MembershipGift\StoreMembershipGiftRequest;
use App\Http\Requests\MembershipGift\UpdateMembershipGiftRequest;
use App\Http\Resources\MembershipGiftResource;
use App\Http\Resources\MembershipGiftResourceCollection;
use App\Models\MembershipGift;
use App\Models\MembershipType;
use App\Repositories\Contracts\MembershipGiftRepository;
use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\MembershipTypeRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class MembershipGiftController extends Controller
{
    protected $membershipGiftRepository;
    protected $membershipRepository;
    protected $membershipTypeRepository;

    public function __construct(MembershipGiftRepository $membershipGiftRepository, MembershipRepository $membershipRepository, MembershipTypeRepository $membershipTypeRepository)
    {
        $this->membershipGiftRepository = $membershipGiftRepository;
        $this->membershipRepository = $membershipRepository;
        $this->membershipTypeRepository = $membershipTypeRepository;
    }

    public function index(IndexRequest $request)
    {
        $membershipGifts = $this->membershipGiftRepository->findBy($request->all());
        return new MembershipGiftResourceCollection($membershipGifts);
    }

    public function store(StoreMembershipGiftRequest $request)
    {
        $authUser = Auth::user();
        
        // Check if user has available gifts
        if ($authUser->membership->available_gifts_count <= 0) {
            return response()->json([
                'message' => 'You have no available gifts to send',
                'status' => 'error'
            ], 422);
        }
        
        $data = $request->all();
        
        $data['from_user_id'] = $authUser->id;
        
        $data['sent_at'] = now()->toIso8601String();
        
        // Set expires_at to 1 year from now if not provided
        if (!isset($data['expires_at'])) {
            $data['expires_at'] = now()->addDays(365)->toIso8601String();
        }
        
        $membershipGift = $this->membershipGiftRepository->save($data);

        // Reduce available gifts count by 1
        $authUser->membership->decrement('available_gifts_count');

        // Only send notification if gift is sent to a user (not to an invitation)
        if (isset($data['to_user_id'])) {
            event(new MembershipNotification(
                'gift_membership_sent',
                null,
                $membershipGift
            ));
        }
        
        return new MembershipGiftResource($membershipGift);
    }

    public function show($id)
    {
        $membershipGift = $this->membershipGiftRepository->findOne($id);
        if ($membershipGift === null) {
            throw new ModelNotFoundException();
        }
        return new MembershipGiftResource($membershipGift);
    }

     public function update(UpdateMembershipGiftRequest $request, MembershipGift $membershipGift)
    {
        $data = $request->all();
        $authUser = Auth::user();

        // Check if this is a resend operation (rejected_at was set but now being set to null)
        $isResending = isset($data['rejected_at']) && $data['rejected_at'] === null &&
                      $membershipGift->rejected_at !== null;

        // If resending a previously rejected gift, check if user has available gifts
        if ($isResending) {
            // Check if user has available gifts
            if ($authUser->membership->available_gifts_count <= 0) {
                return response()->json([
                    'message' => 'You have no available gifts to send',
                    'status' => 'error'
                ], 422);
            }

            // Update sent_at to current time
            $data['sent_at'] = now()->toIso8601String();

            // Reduce available gifts count by 1 since we're sending a new gift
            $authUser->membership->decrement('available_gifts_count');
        }

        $membershipGift = $this->membershipGiftRepository->update($membershipGift, $data);

        // Check if accepted_at or rejected_at was updated
        if (!empty($data['accepted_at'])) {
            $authUser = Auth::user();
            $giftMembershipType = $this->membershipTypeRepository->findOneBy(['name' => MembershipType::GIFT_MEMBERSHIP]);

            // Get or create membership for auth user
            if ($authUser->membership) {
                // Update existing membership
                $this->membershipRepository->update($authUser->membership, [
                    'membership_type_id' => $giftMembershipType->id,
                    'started_at' => now(),
                    'end_at' => now()->addDays(365),
                ]);
            } else {
                // Create new membership
                $membership = $this->membershipRepository->save([
                    'membership_type_id' => $giftMembershipType->id,
                    'started_at' => now(),
                    'end_at' => now()->addDays(365),
                    // 'is_autopay_enabled' => false,
                    // 'available_gifts_count' => 3
                ]);

                // Update user with new membership
                $authUser->update(['membership_id' => $membership->id]);
            }

            event(new MembershipNotification(
                'gift_membership_accepted',
                null,
                $membershipGift
            ));
        } elseif (!empty($data['rejected_at'])) {
            // Increment available_gifts_count for the sender
            if ($membershipGift->fromUser && $membershipGift->fromUser->membership) {
                $membershipGift->fromUser->membership->increment('available_gifts_count');
            }
            event(new MembershipNotification(
                'gift_membership_rejected',
                null,
                $membershipGift
            ));
        } elseif ($isResending) {
            // Send notification for resent gift if it's to a user (not to an invitation)
            if ($membershipGift->to_user_id) {
                event(new MembershipNotification(
                    'gift_membership_sent',
                    null,
                    $membershipGift
                ));
            }
        }

        return new MembershipGiftResource($membershipGift);
    }

    public function destroy(MembershipGift $membershipGift)
    {
        // Increment available_gifts_count for the sender
        if ($membershipGift->fromUser && $membershipGift->fromUser->membership) {
            $membershipGift->fromUser->membership->increment('available_gifts_count');
        }

        $this->membershipGiftRepository->delete($membershipGift);
        return response()->json(null, 204);
    }
}
