<?php

namespace App\Http\Controllers;

use App\Http\Requests\Question\IndexRequest;
use App\Http\Requests\Question\StoreQuestionRequest;
use App\Http\Requests\Question\UpdateQuestionRequest;
use App\Http\Resources\QuestionResource;
use App\Http\Resources\QuestionResourceCollection;
use App\Models\Question;
use App\Models\Tag;
use App\Repositories\Contracts\QuestionRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class QuestionController extends Controller
{
    protected $questionRepository;

    public function __construct(QuestionRepository $questionRepository)
    {
        $this->questionRepository = $questionRepository;
    }

    public function index(IndexRequest $request)
    {
        $questions = $this->questionRepository->findBy($request->all());

        return new QuestionResourceCollection($questions);
    }

    public function store(StoreQuestionRequest $request)
    {
        $question = $this->questionRepository->save($request->all());

        // Set question type if creator exists
        if ($question->creator__user_id && !$question->question_type) {
            $question->question_type = Question::CUSTOM_QUESTION_TYPE;
            $question->save();
        }

        // Attach user-selected tags if they exist
        if ($request->has('tag_id')) {
            $question->tags()->attach($request->tag_id);
        } else if ($question->creator__user_id) {
            // Attach custom tag if no user-selected tags
            $customTag = Tag::where('title', Question::CUSTOM_QUESTION_TYPE)->first();
            if ($customTag) {
                $question->tags()->attach($customTag->id);
            }
        }

        $question->refresh();
        return new QuestionResource($question);
    }

    public function update(UpdateQuestionRequest $request, Question $question)
    {

        $question = $this->questionRepository->update($question, $request->all());
        return new QuestionResource($question);
    }

    public function show($id)
    {
        $question = $this->questionRepository->findOne($id);
        if ($question === null) {
            throw new ModelNotFoundException();
        }

        return new QuestionResource($question);
    }

    public function destroy(Question $question)
    {
        $this->questionRepository->delete($question);

        return response()->json(null, 204);
    }
}
