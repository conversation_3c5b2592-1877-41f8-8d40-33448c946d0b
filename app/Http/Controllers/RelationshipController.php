<?php

namespace App\Http\Controllers;

use App\Http\Requests\Relationship\StoreRelationshipRequest;
use App\Http\Requests\Relationship\UpdateRelationshipRequest;
use App\Http\Resources\RelationshipResource;
use App\Http\Resources\RelationshipResourceCollection;
use App\Models\Relationship;
use App\Repositories\Contracts\RelationshipRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RelationshipController extends Controller
{
    protected $relationshipRepository;

    public function __construct(RelationshipRepository $relationshipRepository)
    {
        $this->relationshipRepository = $relationshipRepository;

    }

    public function index()
    {
        $relationships = $this->relationshipRepository->findBy();

        return new RelationshipResourceCollection($relationships);
    }

    public function store(StoreRelationshipRequest $request)
    {
        $relationship = $this->relationshipRepository->save($request->all());

        return new RelationshipResource($relationship);
    }

    public function update(UpdateRelationshipRequest $request, Relationship $relationship)
    {
        $relationship = $this->relationshipRepository->update($relationship, $request->all());
        return new RelationshipResource($relationship);
    }

    public function show($id)
    {
        $relationship = $this->relationshipRepository->findOne($id);
        if ($relationship === null) {
            throw new ModelNotFoundException();
        }

        return new RelationshipResource($relationship);
    }

    public function destroy(Relationship $relationship)
    {
        $this->relationshipRepository->delete($relationship);

        return response()->json(null, 204);
    }
}
