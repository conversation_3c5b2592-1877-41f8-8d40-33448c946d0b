<?php

namespace App\Http\Controllers;

use App\Http\Requests\Notification\StoreNotificationRequest;
use App\Http\Requests\Notification\UpdateNotificationRequest;
use App\Http\Resources\NotificationResource;
use App\Http\Resources\NotificationResourceCollection;
use App\Models\Notification;
use App\Repositories\Contracts\NotificationRepository;
use function GuzzleHttp\Promise\all;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class NotificationController extends Controller
{
    protected $notificationRepository;

    public function __construct(NotificationRepository $notificationRepository)
    {
        $this->notificationRepository = $notificationRepository;
    }

    public function index()
    {
        $notifications = $this->notificationRepository->findBy();

        return new NotificationResourceCollection($notifications);
    }

    public function store(StoreNotificationRequest $request)
    {
        $notification = $this->notificationRepository->save($request->all());

        return new NotificationResource($notification);
    }

    public function update(UpdateNotificationRequest $request, Notification $notification)
    {

        $notification = $this->notificationRepository->update($notification, $request->all());
        return new NotificationResource($notification);
    }

    public function show($id)
    {
        $notification = $this->notificationRepository->findOne($id);
        if ($notification === null) {
            throw new ModelNotFoundException();
        }

        return new NotificationResource($notification);
    }

    public function destroy(Notification $notification)
    {
        $this->notificationRepository->delete($notification);

        return response()->json(null, 204);
    }
}
