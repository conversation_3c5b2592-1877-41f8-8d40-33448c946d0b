<?php

namespace App\Http\Controllers;

use App\Events\VideoCommented;
use App\Http\Requests\VideoComment\IndexRequest;
use App\Http\Requests\VideoComment\StoreVideoCommentRequest;
use App\Http\Requests\VideoComment\UpdateVideoCommentRequest;
use App\Http\Resources\VideoCommentResource;
use App\Http\Resources\VideoCommentResourceCollection;
use App\Models\VideoComment;
use App\Repositories\Contracts\VideoCommentRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class VideoCommentController extends Controller
{
    protected $videoCommentRepository;

    public function __construct(VideoCommentRepository $videoCommentRepository)
    {
        $this->videoCommentRepository = $videoCommentRepository;
    }

    public function index(IndexRequest $request)
    {
        $videoComments = $this->videoCommentRepository->findBy($request?->all());

        return new VideoCommentResourceCollection($videoComments);
    }

    public function store(StoreVideoCommentRequest $request)
    {
        $videoComment = $this->videoCommentRepository->save($request->all());

        event(new VideoCommented($videoComment));

        return new VideoCommentResource($videoComment);
    }

    public function update(UpdateVideoCommentRequest $request, VideoComment $videoComment)
    {

        $videoComment = $this->videoCommentRepository->update($videoComment, $request->all());
        return new VideoCommentResource($videoComment);
    }

    public function show($id)
    {
        $videoComment = $this->videoCommentRepository->findOne($id);
        if ($videoComment === null) {
            throw new ModelNotFoundException();
        }

        return new VideoCommentResource($videoComment);
    }

    public function destroy(VideoComment $videoComment)
    {
        $this->videoCommentRepository->delete($videoComment);

        return response()->json(null, 204);
    }
}
