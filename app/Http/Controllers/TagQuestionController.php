<?php

namespace App\Http\Controllers;

use App\Http\Requests\TagQuestion\StoreTagQuestionRequest;
use App\Http\Requests\TagQuestion\UpdateTagQuestionRequest;
use App\Http\Resources\TagQuestionResource;
use App\Http\Resources\TagQuestionResourceCollection;
use App\Models\TagQuestion;
use App\Repositories\Contracts\TagQuestionRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class TagQuestionController extends Controller
{
    protected $tagQuestionRepository;

    public function __construct(TagQuestionRepository $tagQuestionRepository)
    {
        $this->tagQuestionRepository = $tagQuestionRepository;
    }

    public function index()
    {
        $tagQuestions = $this->tagQuestionRepository->findBy();

        return new TagQuestionResourceCollection($tagQuestions);
    }

    public function store(StoreTagQuestionRequest $request)
    {
        $tagQuestion = $this->tagQuestionRepository->save($request->all());

        return new TagQuestionResource($tagQuestion);
    }

    public function update(UpdateTagQuestionRequest $request, TagQuestion $tagQuestion)
    {

        $tagQuestion = $this->tagQuestionRepository->update($tagQuestion, $request->all());
        return new TagQuestionResource($tagQuestion);
    }

    public function show($id)
    {
        $tagQuestion = $this->tagQuestionRepository->findOne($id);
        if ($tagQuestion === null) {
            throw new ModelNotFoundException();
        }

        return new TagQuestionResource($tagQuestion);
    }

    public function destroy(TagQuestion $tagQuestion)
    {
        $this->tagQuestionRepository->delete($tagQuestion);

        return response()->json(null, 204);
    }
}
