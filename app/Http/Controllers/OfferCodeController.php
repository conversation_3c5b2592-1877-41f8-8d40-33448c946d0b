<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferCode\CreateOfferCodeRequest;
use App\Http\Requests\OfferCode\UpdateOfferCodeRequest;
use App\Http\Requests\OfferCode\ApplyOfferCodeRequest;
use App\Http\Resources\OfferCode\OfferCodeResource;
use App\Http\Resources\OfferCode\OfferCodeCollection;
use App\Models\OfferCode;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class OfferCodeController extends Controller
{
    protected OfferCodeRepository $offerCodeRepository;
    protected UserRepository $userRepository;

    public function __construct(
        OfferCodeRepository $offerCodeRepository,
        UserRepository $userRepository
    ) {
        $this->offerCodeRepository = $offerCodeRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Display a listing of offer codes
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $status = $request->get('status');

            $query = $this->offerCodeRepository->query();

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('code', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($status) {
                $query->where('status', $status);
            }

            $offerCodes = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => new OfferCodeCollection($offerCodes),
                'meta' => [
                    'current_page' => $offerCodes->currentPage(),
                    'last_page' => $offerCodes->lastPage(),
                    'per_page' => $offerCodes->perPage(),
                    'total' => $offerCodes->total(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching offer codes', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch offer codes'
            ], 500);
        }
    }

    /**
     * Store a newly created offer code
     */
    public function store(CreateOfferCodeRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Generate unique code if not provided
            if (!isset($data['code'])) {
                $data['code'] = $this->generateUniqueCode();
            }

            $offerCode = $this->offerCodeRepository->create($data);

            Log::info('Offer code created', [
                'offer_code_id' => $offerCode->id,
                'code' => $offerCode->code,
                'created_by' => Auth::id()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Offer code created successfully',
                'data' => new OfferCodeResource($offerCode)
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error creating offer code', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create offer code'
            ], 500);
        }
    }

    /**
     * Display the specified offer code
     */
    public function show(OfferCode $offerCode): JsonResponse
    {
        try {
            return response()->json([
                'status' => 'success',
                'data' => new OfferCodeResource($offerCode)
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching offer code', [
                'offer_code_id' => $offerCode->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch offer code'
            ], 500);
        }
    }

    /**
     * Update the specified offer code
     */
    public function update(UpdateOfferCodeRequest $request, OfferCode $offerCode): JsonResponse
    {
        try {
            $data = $request->validated();
            
            $updatedOfferCode = $this->offerCodeRepository->update($offerCode, $data);

            Log::info('Offer code updated', [
                'offer_code_id' => $offerCode->id,
                'code' => $offerCode->code,
                'updated_by' => Auth::id()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Offer code updated successfully',
                'data' => new OfferCodeResource($updatedOfferCode)
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating offer code', [
                'offer_code_id' => $offerCode->id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update offer code'
            ], 500);
        }
    }

    /**
     * Remove the specified offer code
     */
    public function destroy(OfferCode $offerCode): JsonResponse
    {
        try {
            $this->offerCodeRepository->delete($offerCode);

            Log::info('Offer code deleted', [
                'offer_code_id' => $offerCode->id,
                'code' => $offerCode->code,
                'deleted_by' => Auth::id()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Offer code deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting offer code', [
                'offer_code_id' => $offerCode->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete offer code'
            ], 500);
        }
    }

    /**
     * Apply offer code for authenticated user
     */
    public function apply(ApplyOfferCodeRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $code = $request->validated()['code'];

            $offerCode = $this->offerCodeRepository->findByCode($code);

            if (!$offerCode) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid offer code'
                ], 404);
            }

            // Check if offer code is active
            if ($offerCode->status !== OfferCode::STATUS_ACTIVE) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Offer code is not active'
                ], 400);
            }

            // Check if offer code has expired
            if ($offerCode->expires_at && $offerCode->expires_at->isPast()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Offer code has expired'
                ], 400);
            }

            // Check usage limits
            if ($offerCode->max_uses && $offerCode->used_count >= $offerCode->max_uses) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Offer code usage limit exceeded'
                ], 400);
            }

            // Check if user has already used this code
            $hasUsed = $this->offerCodeRepository->hasUserUsedCode($user->id, $offerCode->id);
            if ($hasUsed) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'You have already used this offer code'
                ], 400);
            }

            // Apply the offer code
            $result = $this->offerCodeRepository->applyToUser($user, $offerCode);

            Log::info('Offer code applied', [
                'user_id' => $user->id,
                'offer_code_id' => $offerCode->id,
                'code' => $offerCode->code
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Offer code applied successfully',
                'data' => [
                    'offer_code' => new OfferCodeResource($offerCode),
                    'applied_at' => now(),
                    'benefit' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error applying offer code', [
                'user_id' => Auth::id(),
                'code' => $request->input('code'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to apply offer code'
            ], 500);
        }
    }

    /**
     * Get user's applied offer codes
     */
    public function userCodes(): JsonResponse
    {
        try {
            $user = Auth::user();
            $appliedCodes = $this->offerCodeRepository->getUserAppliedCodes($user->id);

            return response()->json([
                'status' => 'success',
                'data' => new OfferCodeCollection($appliedCodes)
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching user offer codes', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch applied offer codes'
            ], 500);
        }
    }

    /**
     * Generate unique offer code
     */
    private function generateUniqueCode(): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        } while ($this->offerCodeRepository->findByCode($code));

        return $code;
    }
}
