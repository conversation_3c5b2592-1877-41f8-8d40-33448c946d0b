<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferCode\IndexRequest;
use App\Http\Requests\OfferCode\StoreOfferCodeRequest;
use App\Http\Requests\OfferCode\UpdateOfferCodeRequest;
use App\Http\Requests\OfferCode\ApplyOfferCodeRequest;
use App\Http\Resources\OfferCodeResource;
use App\Http\Resources\OfferCodeResourceCollection;
use App\Models\OfferCode;
use App\Repositories\Contracts\OfferCodeRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class OfferCodeController extends Controller
{
    protected OfferCodeRepository $offerCodeRepository;

    public function __construct(OfferCodeRepository $offerCodeRepository)
    {
        $this->offerCodeRepository = $offerCodeRepository;
    }

    public function index(IndexRequest $request)
    {
        $requestData = $request->all();
        $offerCodes = $this->offerCodeRepository->findBy($requestData);
        return new OfferCodeResourceCollection($offerCodes);
    }

    public function store(StoreOfferCodeRequest $request)
    {
        $offerCode = $this->offerCodeRepository->save($request->all());
        return new OfferCodeResource($offerCode);
    }

    public function update(UpdateOfferCodeRequest $request, OfferCode $offerCode)
    {
        $updatedOfferCode = $this->offerCodeRepository->update($offerCode, $request->all());
        return new OfferCodeResource($updatedOfferCode);
    }

    public function show($id)
    {
        $offerCode = $this->offerCodeRepository->findOne($id);

        if ($offerCode === null) {
            throw new ModelNotFoundException();
        }

        return new OfferCodeResource($offerCode);
    }

    public function destroy(OfferCode $offerCode)
    {
        try {
            $this->offerCodeRepository->delete($offerCode);
            return response()->json(null, 204);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete offer code. Please try again later.',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function apply(ApplyOfferCodeRequest $request)
    {
        $code = $request->input('code');
        $offerCode = $this->offerCodeRepository->findByCode($code);

        if (!$offerCode) {
            return response()->json(['error' => 'Invalid offer code'], 404);
        }

        // Apply offer code logic here
        $result = $this->offerCodeRepository->applyToUser(auth()->user(), $offerCode);

        return response()->json([
            'message' => 'Offer code applied successfully',
            'data' => new OfferCodeResource($offerCode),
            'benefit' => $result
        ]);
    }
}
