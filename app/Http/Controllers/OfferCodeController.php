<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferCode\IndexRequest;
use App\Http\Requests\OfferCode\StoreOfferCodeRequest;
use App\Http\Requests\OfferCode\UpdateOfferCodeRequest;
use App\Http\Resources\OfferCodeResource;
use App\Http\Resources\OfferCodeResourceCollection;
use App\Models\OfferCode;
use App\Repositories\Contracts\OfferCodeRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class OfferCodeController extends Controller
{
    protected $offerCodeRepository;

    public function __construct(OfferCodeRepository $offerCodeRepository)
    {
        $this->offerCodeRepository = $offerCodeRepository;
    }

    public function index(IndexRequest $request)
    {
        $offerCodes = $this->offerCodeRepository->findBy($request->all());
        return new OfferCodeResourceCollection($offerCodes);
    }

    public function store(StoreOfferCodeRequest $request)
    {
        $offerCode = $this->offerCodeRepository->save($request->all());
        return new OfferCodeResource($offerCode);
    }

    public function show($id)
    {
        $offerCode = $this->offerCodeRepository->findOne($id);
        if ($offerCode === null) {
            throw new ModelNotFoundException();
        }
        return new OfferCodeResource($offerCode);
    }

    public function update(UpdateOfferCodeRequest $request, OfferCode $offerCode)
    {
        $offerCode = $this->offerCodeRepository->update($offerCode, $request->all());
        return new OfferCodeResource($offerCode);
    }

    public function destroy(OfferCode $offerCode)
    {
        $this->offerCodeRepository->delete($offerCode);
        return response()->json(null, 204);
    }
}
