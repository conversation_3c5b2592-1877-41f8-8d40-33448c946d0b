<?php

namespace App\Http\Controllers;

use App\Http\Requests\ConnectionRequest\CreateOrUpdateConnectionRequest;
use App\Http\Requests\ConnectionRequest\StoreConnectionRequestRequest;
use App\Http\Requests\ConnectionRequest\UpdateConnectionRequestRequest;
use App\Http\Resources\ConnectionRequestResource;
use App\Http\Resources\ConnectionRequestResourceCollection;
use App\Models\ConnectionRequest;
use App\Repositories\Contracts\ConnectionRequestRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ConnectionRequestController extends Controller
{
    protected $connectionRequestRepository;

    public function __construct(ConnectionRequestRepository $connectionRequestRepository)
    {
        $this->connectionRequestRepository = $connectionRequestRepository;

    }

    public function index()
    {
        $connectionRequests = $this->connectionRequestRepository->findBy();

        return new ConnectionRequestResourceCollection($connectionRequests);
    }

    public function store(StoreConnectionRequestRequest $request)
    {
        $connectionRequest = $this->connectionRequestRepository->save($request->all());

        return new ConnectionRequestResource($connectionRequest);
    }
    public function createOrUpdate(CreateOrUpdateConnectionRequest $request)
    {
        $connectionRequest = $this->connectionRequestRepository->createOrUpdate($request->validated());

        return new ConnectionRequestResource($connectionRequest);
    }

    public function update(UpdateConnectionRequestRequest $request, ConnectionRequest $connectionRequest)
    {
        $connectionRequest = $this->connectionRequestRepository->update($connectionRequest, $request->all());
        return new ConnectionRequestResource($connectionRequest);
    }

    public function show($id)
    {
        $connectionRequest = $this->connectionRequestRepository->findOne($id);
        if ($connectionRequest === null) {
            throw new ModelNotFoundException();
        }

        return new ConnectionRequestResource($connectionRequest);
    }

    public function destroy(ConnectionRequest $connectionRequest)
    {
        $this->connectionRequestRepository->delete($connectionRequest);

        return response()->json(null, 204);
    }

    public function discardedList()
    {
        $discardedRequests = $this->connectionRequestRepository->getDiscardedConnectionRequests();
        
        return new ConnectionRequestResourceCollection($discardedRequests);
    }

    public function deleteWithRelatedData($connectionRequestId)
    {
        try {
            $this->connectionRequestRepository->deleteConnectionRequestAndRelatedData($connectionRequestId);
            return response()->json(null, 204);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Connection request not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete connection request'], 500);
        }
    }
}
