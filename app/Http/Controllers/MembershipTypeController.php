<?php

namespace App\Http\Controllers;

use App\Http\Requests\MembershipType\IndexRequest;
use App\Http\Requests\MembershipType\StoreMembershipTypeRequest;
use App\Http\Requests\MembershipType\UpdateMembershipTypeRequest;
use App\Http\Resources\MembershipTypeResource;
use App\Http\Resources\MembershipTypeResourceCollection;
use App\Models\MembershipType;
use App\Repositories\Contracts\MembershipTypeRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class MembershipTypeController extends Controller
{
    protected $membershipTypeRepository;

    public function __construct(MembershipTypeRepository $membershipTypeRepository)
    {
        $this->membershipTypeRepository = $membershipTypeRepository;
    }

    public function index(IndexRequest $request)
    {
        $membershipTypes = $this->membershipTypeRepository->findBy($request->all());
        return new MembershipTypeResourceCollection($membershipTypes);
    }

    public function store(StoreMembershipTypeRequest $request)
    {
        $membershipType = $this->membershipTypeRepository->save($request->all());
        return new MembershipTypeResource($membershipType);
    }

    public function show($id)
    {
        $membershipType = $this->membershipTypeRepository->findOne($id);
        if ($membershipType === null) {
            throw new ModelNotFoundException();
        }
        return new MembershipTypeResource($membershipType);
    }

    public function update(UpdateMembershipTypeRequest $request, MembershipType $membershipType)
    {
        $membershipType = $this->membershipTypeRepository->update($membershipType, $request->all());
        return new MembershipTypeResource($membershipType);
    }

    public function destroy(MembershipType $membershipType)
    {
        $this->membershipTypeRepository->delete($membershipType);
        return response()->json(null, 204);
    }
} 