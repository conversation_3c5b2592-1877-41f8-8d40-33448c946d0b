<?php

namespace App\Http\Controllers;

use App\Events\TranscriptionCompleted;
use App\Events\VideoUploaded;
use App\Http\Requests\Video\HomeFeedRequest;
use App\Http\Requests\Video\IndexRequest;
use App\Http\Requests\Video\StoreVideoRequest;
use App\Http\Requests\Video\UpdateVideoRequest;
use App\Http\Requests\Video\UploadUrlRequest;
use App\Http\Requests\Video\UploadVideoRequest;
use App\Http\Resources\VideoResource;
use App\Http\Resources\VideoResourceCollection;
use App\Models\Video;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\TranscribeService;
use App\Services\Contracts\VideoProcessor;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VideoController extends Controller
{
    protected $videoRepository;
    protected $videoProcessor;
    protected $transcribeService;

    public function __construct(VideoRepository $videoRepository, VideoProcessor $videoProcessor, TranscribeService $transcribeService)
    {
        $this->videoRepository = $videoRepository;
        $this->videoProcessor = $videoProcessor;
        $this->transcribeService = $transcribeService;
    }
    public function index(IndexRequest $request)
    {
        $videos = $this->videoRepository->findBy($request?->all());

        return new VideoResourceCollection($videos);
    }

    public function update(UpdateVideoRequest $request, Video $video)
    {

        $video = $this->videoRepository->update($video, $request->all());
        return new VideoResource($video);
    }

    public function store(StoreVideoRequest $request)
    {
        $video = $this->videoRepository->save($request->all());

        return new VideoResource($video);
    }

    public function show($id)
    {
        $video = $this->videoRepository->findOne($id);
        if ($video === null) {
            throw new ModelNotFoundException();
        }

        return new VideoResource($video);
    }

    public function destroy(Video $video)
    {
        $this->videoRepository->delete($video);

        return response()->json(null, 204);
    }

    public function createUploadUrl(UploadUrlRequest $request)
    {
        $authUser = Auth::user();
        try {
            list($uploadUrl, $uploadId) = $this->videoProcessor->createAuthenticatedUploadUrl();
            // Create the video record with the upload information
            $video = $this->videoRepository->save([
                'mux_id' => $uploadId,
                'owner_user_id' => $authUser->id,
                'question_id' => $request['question_id'],
                'status' => 'uploading',
                'duration' => 1,
            ]);

            return response()->json(['upload_url' => $uploadUrl, 'videoId' => $video['id']]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function uploadVideo(UploadVideoRequest $request)
    {
        $uploadUrl = $request->input('upload_url');
        $videoPath = $request->file('video')->path();

        try {
            $success = $this->videoProcessor->uploadVideo($uploadUrl, $videoPath);
            if ($success !== null) {
                return response()->json(['status' => 'success']);
            } else {
                return response()->json(['status' => 'error', 'message' => 'Video upload failed'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    public function getVideoUrl($assetId)
    {
        try {
            $videoUrl = $this->videoProcessor->getVideoUrl($assetId);

            if ($videoUrl) {
                return response()->json(['video_url' => $videoUrl]);
            } else {
                return response()->json(['message' => 'Video URL retrieval failed'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Video URL retrieval failed'], 500);
        }
    }

    public function homeFeed(HomeFeedRequest $request)
    {
        $userId = $request->query('userId');
        $videos = $this->videoRepository->getHomeFeedVideos($userId);

        return new VideoResourceCollection($videos);
    }

    public function handleWebhook(Request $request)
    {

        try {
            // Handle the incoming webhook event based on its type
            $event = $request->json('type');

            switch ($event) {
                case 'video.asset.created':
                    break;

                case 'video.asset.ready':
                    $data = $request->json('data');
                    $muxId = $data['upload_id'];

                    $video = $this->videoRepository->findOneBy(['mux_id' => $muxId]);

                    if ($video instanceof Video) {
                        $this->videoRepository->update($video, [
                            'mux_id' => $data['playback_ids'][0]['id'],
                            'status' => $data['status'],
                            'duration' => $data['duration'],
                        ]);
                    }
                    break;
                case 'video.asset.master.ready':
                    $data = $request->json('data');
                    $muxId = $data['playback_ids'][0]['id'];
                    $video = $this->videoRepository->findOneBy(['mux_id' => $muxId]);

                    Log::info('video.asset.master.ready webhook received', [
                        'muxId' => $muxId,
                        'video_found' => $video instanceof Video,
                        'video_file' => $video->video_file ?? null,
                        'transcript_file' => $video->transcript_file ?? null,
                    ]);

                    if ($video instanceof Video && !$video->video_file && !$video->transcript_file) {
                        event(new \App\Events\VideoMasterReadyForProcessing($video, $data['master']['url']));
                        Log::info('Fired MasterReadyReceived event for muxId: ' . $muxId);
                    } else {
                        Log::info('Skipping video.asset.master.ready processing for muxId: ' . $muxId . ' (already processed or video not found)');
                    }
                    break;

                case 'video.upload.errored':
                    // Handle upload errored event
                    $data = $request->json('data');
                    $errorMessage = $data['error']['message'];
                    break;

                default:
                    // Handle unknown event type
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Webhook handling error: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

}
