<?php

namespace App\Http\Controllers;

use App\Http\Requests\ConnectionRequestStatus\StoreConnectionRequestStatusRequest;
use App\Http\Requests\ConnectionRequestStatus\UpdateConnectionRequestStatusRequest;
use App\Http\Resources\ConnectionRequestStatusResource;
use App\Http\Resources\ConnectionRequestStatusResourceCollection;
use App\Models\ConnectionRequestStatus;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ConnectionRequestStatusController extends Controller
{
    protected $connectionRequestStatusRepository;

    public function __construct(ConnectionRequestStatusRepository $connectionRequestStatusRepository)
    {
        $this->connectionRequestStatusRepository = $connectionRequestStatusRepository;
    }

    public function index()
    {
        $connectionRequestStatuses = $this->connectionRequestStatusRepository->findBy();

        return new ConnectionRequestStatusResourceCollection($connectionRequestStatuses);
    }

    public function store(StoreConnectionRequestStatusRequest $request)
    {
        $connectionRequestStatus = $this->connectionRequestStatusRepository->save($request->all());

        return new ConnectionRequestStatusResource($connectionRequestStatus);
    }

    public function update(UpdateConnectionRequestStatusRequest $request, ConnectionRequestStatus $connectionRequestStatus)
    {
        $connectionRequestStatus = $this->connectionRequestStatusRepository->update($connectionRequestStatus, $request->all());
        return new ConnectionRequestStatusResource($connectionRequestStatus);
    }

    public function show($id)
    {
        $connectionRequestStatus = $this->connectionRequestStatusRepository->findOne($id);
        if ($connectionRequestStatus === null) {
            throw new ModelNotFoundException();
        }

        return new ConnectionRequestStatusResource($connectionRequestStatus);
    }

    public function destroy(ConnectionRequestStatus $connectionRequestStatus)
    {
        $this->connectionRequestStatusRepository->delete($connectionRequestStatus);

        return response()->json(null, 204);
    }
}
