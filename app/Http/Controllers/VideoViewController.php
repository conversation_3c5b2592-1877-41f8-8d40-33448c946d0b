<?php

namespace App\Http\Controllers;

use App\Events\VideoViewed;
use App\Http\Requests\VideoView\IndexRequest;
use App\Http\Requests\VideoView\LikeVideoRequest;
use App\Http\Requests\VideoView\StoreVideoViewRequest;
use App\Http\Requests\VideoView\UpdateVideoViewRequest;
use App\Http\Resources\VideoViewResource;
use App\Http\Resources\VideoViewResourceCollection;
use App\Models\VideoView;
use App\Repositories\Contracts\VideoViewRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class VideoViewController extends Controller
{
    protected $videoViewRepository;

    public function __construct(VideoViewRepository $videoViewRepository)
    {
        $this->videoViewRepository = $videoViewRepository;
    }

    public function index(IndexRequest $request)
    {
        $videoViews = $this->videoViewRepository->findBy($request?->all());

        return new VideoViewResourceCollection($videoViews);
    }

    public function store(StoreVideoViewRequest $request)
    {
        $videoView = $this->videoViewRepository->findOneBy([
            'viewer_user_id' => $request['viewer_user_id'],
            'video_id' => $request['video_id'],
        ]);

        if ($videoView) {
            return response()->json([
                'message' => 'Video already viewed',
            ], 200);
        }

        $videoView = $this->videoViewRepository->save($request->all());

        event(new VideoViewed($videoView));

        return new VideoViewResource($videoView);
    }

    public function update(UpdateVideoViewRequest $request, VideoView $videoView)
    {

        $videoView = $this->videoViewRepository->update($videoView, $request->all());
        return new VideoViewResource($videoView);
    }

    public function show($id)
    {
        $videoView = $this->videoViewRepository->findOne($id);
        if ($videoView === null) {
            throw new ModelNotFoundException();
        }

        return new VideoViewResource($videoView);
    }

    public function destroy(VideoView $videoView)
    {
        $this->videoViewRepository->delete($videoView);

        return response()->json(null, 204);
    }

    public function likeVideo(LikeVideoRequest $request)
    {
        $videoView = $this->videoViewRepository->likeVideo($request->all());

        return new VideoViewResource($videoView);
    }
}
