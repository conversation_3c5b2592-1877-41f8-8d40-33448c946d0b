<?php

namespace App\Http\Controllers;

use App\Http\Requests\Tag\IndexRequest;
use App\Http\Requests\Tag\StoreTagRequest;
use App\Http\Requests\Tag\UpdateTagRequest;
use App\Http\Resources\TagResource;
use App\Http\Resources\TagResourceCollection;
use App\Models\Tag;
use App\Repositories\Contracts\TagRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Storage;

class TagController extends Controller
{
    protected $tagRepository;

    public function __construct(TagRepository $tagRepository)
    {
        $this->tagRepository = $tagRepository;
    }

    public function index(IndexRequest $request)
    {
        $tags = $this->tagRepository->findBy($request?->all());

        return new TagResourceCollection($tags);
    }

    public function store(StoreTagRequest $request)
    {
        $data = $request->all();

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = 'tag_images/' . uniqid() . '.' . $image->getClientOriginalExtension();
            Storage::disk('s3')->put($filename, file_get_contents($image), 'public');
            $data['image'] = $filename;
        }

        $tag = $this->tagRepository->save($data);

        return new TagResource($tag);
    }

    public function update(UpdateTagRequest $request, Tag $tag)
    {
        $data = $request->all();

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = 'tag_images/' . uniqid() . '.' . $image->getClientOriginalExtension();
            Storage::disk('s3')->put($filename, file_get_contents($image), 'public');
            $data['image'] = $filename;
        }

        $tag = $this->tagRepository->update($tag, $data);

        return new TagResource($tag);
    }

    public function show($id)
    {
        $tag = $this->tagRepository->findOne($id);
        if ($tag === null) {
            throw new ModelNotFoundException();
        }

        return new TagResource($tag);
    }

    public function destroy(Tag $tag)
    {
        $this->tagRepository->delete($tag);

        return response()->json(null, 204);
    }
}
