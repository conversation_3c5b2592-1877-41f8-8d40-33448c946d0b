<?php

namespace App\Http\Controllers;

use App\Http\Requests\FollowupQuestionSend\StoreFollowupQuestionSendRequest;
use App\Http\Requests\FollowupQuestionSend\UpdateFollowupQuestionSendRequest;
use App\Http\Resources\FollowupQuestionSendResource;
use App\Http\Resources\FollowupQuestionSendResourceCollection;
use App\Models\FollowupQuestionSend;
use App\Repositories\Contracts\FollowupQuestionSendRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class FollowupQuestionSendController extends Controller
{
    protected $followupQuestionSendRepository;

    public function __construct(FollowupQuestionSendRepository $followupQuestionSendRepository)
    {
        $this->followupQuestionSendRepository = $followupQuestionSendRepository;
    }

    public function index()
    {
        $followupQuestionSends = $this->followupQuestionSendRepository->findBy();

        return new FollowupQuestionSendResourceCollection($followupQuestionSends);
    }

    public function store(StoreFollowupQuestionSendRequest $request)
    {
        $followupQuestionSend = $this->followupQuestionSendRepository->save($request->all());

        return new FollowupQuestionSendResource($followupQuestionSend);
    }

    public function update(UpdateFollowupQuestionSendRequest $request, FollowupQuestionSend $followupQuestionSend)
    {

        $followupQuestionSend = $this->followupQuestionSendRepository->update($followupQuestionSend, $request->all());
        return new FollowupQuestionSendResource($followupQuestionSend);
    }

    public function show($id)
    {
        $followupQuestionSend = $this->followupQuestionSendRepository->findOne($id);
        if ($followupQuestionSend === null) {
            throw new ModelNotFoundException();
        }

        return new FollowupQuestionSendResource($followupQuestionSend);
    }

    public function destroy(FollowupQuestionSend $followupQuestionSend)
    {
        $this->followupQuestionSendRepository->delete($followupQuestionSend);

        return response()->json(null, 204);
    }
    public function create(StoreFollowupQuestionSendRequest $request)
    {
        $followupQuestionSend = $this->followupQuestionSendRepository->create($request->all());

        return new FollowupQuestionSendResource($followupQuestionSend);
    }
}
