<?php

namespace App\Http\Controllers;

use App\Http\Requests\RevenuecatEventLog\StoreRevenuecatEventLogRequest;
use App\Http\Requests\RevenuecatEventLog\UpdateRevenuecatEventLogRequest;
use App\Http\Requests\RevenuecatEventLog\IndexRequest;
use App\Http\Resources\RevenuecatEventLogResource;
use App\Http\Resources\RevenuecatEventLogResourceCollection;
use App\Models\RevenuecatEventLog;
use App\Repositories\Contracts\RevenuecatEventLogRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RevenuecatEventLogController extends Controller
{
    protected $revenuecatEventLogRepository;

    public function __construct(RevenuecatEventLogRepository $revenuecatEventLogRepository)
    {
        $this->revenuecatEventLogRepository = $revenuecatEventLogRepository;
    }

    public function index(IndexRequest $request)
    {
        $logs = $this->revenuecatEventLogRepository->findBy($request->all());
        return new RevenuecatEventLogResourceCollection($logs);
    }

    public function store(StoreRevenuecatEventLogRequest $request)
    {
        $log = $this->revenuecatEventLogRepository->save($request->all());
        return new RevenuecatEventLogResource($log);
    }

    public function update(UpdateRevenuecatEventLogRequest $request, RevenuecatEventLog $revenuecatEventLog)
    {
        $log = $this->revenuecatEventLogRepository->update($revenuecatEventLog, $request->all());
        return new RevenuecatEventLogResource($log);
    }

    public function show($id)
    {
        $log = $this->revenuecatEventLogRepository->findOne($id);
        if ($log === null) {
            throw new ModelNotFoundException();
        }
        return new RevenuecatEventLogResource($log);
    }

    public function destroy(RevenuecatEventLog $revenuecatEventLog)
    {
        $this->revenuecatEventLogRepository->delete($revenuecatEventLog);
        return response()->json(null, 204);
    }
}
