<?php

namespace App\Http\Controllers;

use App\Http\Requests\RevenuecatEventLog\StoreRevenuecatEventLogRequest;
use App\Http\Requests\RevenuecatEventLog\UpdateRevenuecatEventLogRequest;
use App\Http\Requests\RevenuecatEventLog\IndexRequest;
use App\Http\Resources\RevenuecatEventLogResource;
use App\Http\Resources\RevenuecatEventLogResourceCollection;
use App\Models\RevenuecatEventLog;
use App\Repositories\Contracts\RevenuecatEventLogRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class RevenuecatEventLogController extends Controller
{
    protected $revenuecatEventLogRepository;

    /**
     * Dependency injection constructor.
     *
     * @param RevenuecatEventLogRepository $revenuecatEventLogRepository
     */
    public function __construct(RevenuecatEventLogRepository $revenuecatEventLogRepository)
    {
        $this->revenuecatEventLogRepository = $revenuecatEventLogRepository;
    }

    /**
     * Retrieve a list of all revenuecat event logs.
     *
     * @param IndexRequest $request
     * @return RevenuecatEventLogResourceCollection
     */
    public function index(IndexRequest $request)
    {
        $logs = $this->revenuecatEventLogRepository->findBy($request->all());
        return new RevenuecatEventLogResourceCollection($logs);
    }

    /**
     * Create a new revenuecat event log.
     *
     * @param StoreRevenuecatEventLogRequest $request
     * @return RevenuecatEventLogResource
     */
    public function store(StoreRevenuecatEventLogRequest $request)
    {
        $log = $this->revenuecatEventLogRepository->save($request->all());
        return new RevenuecatEventLogResource($log);
    }

    /**
     * Update a revenuecat event log.
     *
     * @param UpdateRevenuecatEventLogRequest $request
     * @param RevenuecatEventLog $revenuecatEventLog
     * @return RevenuecatEventLogResource
     */
    public function update(UpdateRevenuecatEventLogRequest $request, RevenuecatEventLog $revenuecatEventLog)
    {
        $log = $this->revenuecatEventLogRepository->update($revenuecatEventLog, $request->all());
        return new RevenuecatEventLogResource($log);
    }

    /**
     * Retrieve a single revenuecat event log.
     *
     * @param int $id
     * @return RevenuecatEventLogResource
     * @throws ModelNotFoundException
     */
    public function show($id)
    {
        $log = $this->revenuecatEventLogRepository->findOne($id);
        if ($log === null) {
            throw new ModelNotFoundException();
        }
        return new RevenuecatEventLogResource($log);
    }

    /**
     * Delete a revenuecat event log.
     *
     * @param RevenuecatEventLog $revenuecatEventLog
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(RevenuecatEventLog $revenuecatEventLog)
    {
        $this->revenuecatEventLogRepository->delete($revenuecatEventLog);
        return response()->json(null, 204);
    }
}
