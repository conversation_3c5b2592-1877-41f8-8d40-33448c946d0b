<?php

namespace App\Http\Controllers;

use App\Events\MembershipNotification;
use App\Http\Requests\Membership\IndexRequest;
use App\Http\Requests\Membership\StoreMembershipRequest;
use App\Http\Requests\Membership\UpdateMembershipRequest;
use App\Http\Resources\MembershipResource;
use App\Http\Resources\MembershipResourceCollection;
use App\Models\Membership;
use App\Models\MembershipType;
use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\MembershipTypeRepository;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    protected $membershipRepository;
    protected $userRepository;
    protected $membershipTypeRepository;



    public function __construct(
        MembershipRepository $membershipRepository,
        UserRepository $userRepository,
        MembershipTypeRepository $membershipTypeRepository
    ) {
        $this->membershipRepository = $membershipRepository;
        $this->userRepository = $userRepository;
        $this->membershipTypeRepository = $membershipTypeRepository;
    }

    public function index(IndexRequest $request)
    {
        $memberships = $this->membershipRepository->findBy($request->all());
        return new MembershipResourceCollection($memberships);
    }

    public function store(StoreMembershipRequest $request)
    {

        $user = $this->userRepository->findOne(Auth::user()->id);

        $membershipType = $this->membershipTypeRepository->findOne($request['membership_type_id']);

        $data = $request->all();

        // Set default values based on membership type
        if ($membershipType && $membershipType->name === MembershipType::TRIAL_MEMBERSHIP) {
            $now = now();
            $data['started_at'] = $now->toIso8601String();
            $data['end_at'] = $now->addDays(14)->toIso8601String();
            $data['is_autopay_enabled'] = true;
            $data['free_trials_used_count'] = 1;
            $data['available_gifts_count'] = 3;
        }

        if ($user->membership) {
            $membership = $this->membershipRepository->update($user->membership, $data);
        } else {
            $membership = $this->membershipRepository->save($data);

            $this->userRepository->update($user, [
                'membership_id' => $membership->id
            ]);
        }

        if ($membership->membershipType->name === 'full_membership') {
            event(new MembershipNotification(
                'membership_activated',
                $membership
            ));
        }

        return new MembershipResource($membership);
    }

    public function show($id)
    {
        $membership = $this->membershipRepository->findOne($id);
        if ($membership === null) {
            throw new ModelNotFoundException();
        }
        return new MembershipResource($membership);
    }

    public function update(UpdateMembershipRequest $request, Membership $membership)
    {
        $membership = $this->membershipRepository->update($membership, $request->all());
        return new MembershipResource($membership);
    }

    public function destroy(Membership $membership)
    {
        $this->membershipRepository->delete($membership);
        return response()->json(null, 204);
    }
}
