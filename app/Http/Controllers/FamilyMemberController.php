<?php

namespace App\Http\Controllers;

use App\Http\Resources\FamilyMembersResourceCollection;
use App\Http\Resources\VideoResourceCollection;
use App\Models\User;
use App\Repositories\Contracts\FamilyMemberRepository;
use Illuminate\Support\Facades\Auth;

class FamilyMemberController extends Controller
{

    private $familyMemberRepository;

    public function __construct(FamilyMemberRepository $familyMemberRepository)
    {
        $this->familyMemberRepository = $familyMemberRepository;
    }

    public function index()
    {
        // Get the authenticated user
        $authUser = Auth::user();

        // Retrieve family members using the repository
        $familyMembers = $this->familyMemberRepository->getFamilyMembers($authUser->id);

        return new FamilyMembersResourceCollection($familyMembers);
    }

    public function followers()
    {
        $authUser = Auth::user();

        // Retrieve followers using the repository
        $followers = $this->familyMemberRepository->getFollowers($authUser->id);

        return new FamilyMembersResourceCollection($followers);
    }

    public function following()
    {
        $authUser = Auth::user();

        // Retrieve following users using the repository
        $following = $this->familyMemberRepository->getFollowing($authUser->id);

        return new FamilyMembersResourceCollection($following);
    }

    public function homeFeed()
    {
        $authUser = Auth::user();

        // Retrieve home feed videos using the repository
        $homeFeedVideos = $this->familyMemberRepository->getHomeFeedVideos($authUser->id);

        return new VideoResourceCollection($homeFeedVideos);

    }
}
