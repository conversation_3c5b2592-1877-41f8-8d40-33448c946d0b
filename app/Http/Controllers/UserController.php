<?php

namespace App\Http\Controllers;

use App\Cognito\CognitoClient;
use App\Events\UpdateInvitationAndRelationship;
use App\Http\Requests\User\FindByContactsUserRequest;
use App\Http\Requests\User\IndexRequest;
use App\Http\Requests\User\LoginUserRequest;
use App\Http\Requests\User\PhoneNumberVerificationUserRequest;
use App\Http\Requests\User\ProfilePicUploadUserRequest;
use App\Http\Requests\User\ResetPasswordUserRequest;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Requests\User\VerifyUserRequest;
use App\Http\Resources\UserContactsResource;
use App\Http\Resources\UserProfileDetailsResourceCollection;
use App\Http\Resources\UserResource;
use App\Http\Resources\UserResourceCollection;
use App\Models\User;
use App\Repositories\Contracts\InvitationRepository;
use App\Repositories\Contracts\UserRepository;
use App\Services\Contracts\SMSProvider;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class UserController extends Controller
{
    /**
     * @var UserRepository
     */
    private $userRepository;
    private $invitationRepository;
    private $cognitoClient;
    private $smsProvider;

    public function __construct(
        UserRepository $userRepository,
        InvitationRepository $invitationRepository,
        CognitoClient $cognitoClient,
        SMSProvider $smsProvider
    ) {
        $this->userRepository = $userRepository;
        $this->invitationRepository = $invitationRepository;
        $this->cognitoClient = $cognitoClient;
        $this->smsProvider = $smsProvider;
    }

    public function index(IndexRequest $request)
    {
        $requestData = $request->all();

        // If phone number is provided, prepend '+' sign
        $phoneNumber = $requestData['phone_number'] ?? null;
        if ($phoneNumber !== null) {
            $phoneNumber = '+' . $phoneNumber;
        }

        // Find user based on phone number if provided, otherwise get all users
        $user = $phoneNumber ? $this->userRepository->findBy(['phone_number' => $phoneNumber]) : $this->userRepository->findBy($requestData);

        return new UserResourceCollection($user);
    }

    public function store(StoreUserRequest $request)
    {
        $user = $this->userRepository->save($request->all());

        return new UserResource($user);
    }

    public function update(UpdateUserRequest $request, User $user)
    {
        if ($request['password']) {
            $this->cognitoClient->setPassword($request->phone_number, $request->password);

            // Update user first to ensure we have the latest user data
            $user = $this->userRepository->update($user, $request->all());

            // Check if the phone number exists in the invitation table
            $invitations = $this->invitationRepository->findBy(['to_phone_number' => $request->phone_number]);

            // If an invitation with the phone number exists, fire the event
            if ($invitations) {
                event(new UpdateInvitationAndRelationship($invitations, $user));
            }
        } else {
            $user = $this->userRepository->update($user, $request->all());
        }

        return new UserResource($user);
    }

    public function show($id)
    {
        $user = $this->userRepository->findOne($id);

        if ($user === null) {
            throw new ModelNotFoundException();
        }

        return new UserResource($user);
    }

    public function destroy(User $user)
    {
        try {
            $this->cognitoClient->deleteCognitoUser($user->phone_number);

            $this->userRepository->deleteUserRelatedData($user);

            return response()->json(null, 204);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete user account and related data. Please try again later.',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function register(VerifyUserRequest $request)
    {
        $user = $this->userRepository->findOneBy(['phone_number' => $request->phone_number]);
        $phoneNumber = $request->phone_number;

        if ($user && $user['first_name'] === null) {

            $this->cognitoClient->deleteCognitoUser($user->phone_number);

            $this->userRepository->deleteUserRelatedData($user);

        }
         // Only send verification code via Twilio for specific country codes
         $isTwilioCountry = $this->isTwilioSupportedCountry($phoneNumber);
         if ($isTwilioCountry) {
             $this->smsProvider->sendVerificationCode($phoneNumber);
         }
 
         $cognitoUser = $this->cognitoClient->register([
             'phone_number' => $request->phone_number,
             'suppress_messages' => $isTwilioCountry // Pass flag to suppress Cognito messages if using Twilio
         ]);

        $user = $this->userRepository->registerUser($request->all(), $cognitoUser);

        // // Check if the phone number exists in the invitation table
        // $invitations = $this->invitationRepository->findBy(['to_phone_number' => $request->phone_number]);

        // // If an invitation with the phone number exists, fire the event
        // if ($invitations) {
        //     event(new UpdateInvitationAndRelationship($invitations, $user));
        // }

        return new UserResource($user);
    }

    public function primaryRegister(PhoneNumberVerificationUserRequest $request)
    {
        $phoneNumber = $request->phone_number;

        $user = $this->userRepository->findOneBy(['phone_number' => $phoneNumber, 'registration_status' => User::REGISTRATION_STATUS_UNCONFIRMED]);

        if ($user) {
            $this->cognitoClient->deleteCognitoUser($user->phone_number);
            $this->userRepository->deleteUserRelatedData($user);
        }

        try {
            // Call the Cognito client to send OTP and create a user
            $sub = $this->cognitoClient->verifyAccountWithNumber($phoneNumber);

            if (!$sub) {
                return response()->json(['message' => 'Failed to send OTP. Please try again.'], 500);
            }

            $user = $this->userRepository->save([
                'phone_number' => $phoneNumber,
                'registration_status' => User::REGISTRATION_STATUS_UNCONFIRMED,
                'sub' => $sub,
            ]);

            return response()->json(['message' => 'OTP sent successfully.', 'user' => new UserResource($user)], 201);
        } catch (CognitoIdentityProviderException $e) {
            return response()->json(['error' => $e->getAwsErrorMessage()], 400);
        } catch (\Exception $e) {
            return response()->json(['error' => 'An unexpected error occurred during registration.'], 500);
        }
    }

    public function verify(VerifyUserRequest $request)
    {
        try {
            $phoneNumber = $request->phone_number;
            $code = $request->code;

            $isVerified = false;

            if ($this->isTwilioSupportedCountry($phoneNumber)) {
                // First verify with Twilio
                $twilioVerification = $this->smsProvider->checkVerificationCode($phoneNumber, $code);

                if ($twilioVerification->status === 'approved') {
                    // If Twilio verification successful
                    $isVerified = $this->cognitoClient->confirmSignUpAfterTwilioVerification($phoneNumber);
                }
            } else {
                // For non-Twilio countries, just use Cognito
                $isVerified = $this->cognitoClient->verify($phoneNumber, $code);
            }

            if (!$isVerified) {
                return response()->json(['message' => 'Verification failed or already verified'], 400);
            }

            $this->userRepository->verifyUser($request->phone_number);

            // Authenticate the user after successful verification
            $cognitoResult = $this->cognitoClient->authenticate($request->phone_number, User::TEMPORARY_PASSWORD, );

            if (!$cognitoResult) {
                return response()->json(['message' => 'Authentication failed after verification.'], 401);
            }

            $token = getCognitoTokeDetails($cognitoResult->get('AuthenticationResult')['AccessToken']);
            $user = $this->userRepository->findOneBy(['sub' => $token['sub']]);

            if (!$user) {
                return response()->json(['message' => 'User not found after verification.'], 404);
            }

            return response()->json(array_merge($cognitoResult->get('AuthenticationResult'), ['user' => new UserResource($user)]));

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    public function login(LoginUserRequest $request)
    {
        try {
            // Use the new method to initiate authentication with USER_PASSWORD_AUTH flow.
            $cognitoResult = $this->cognitoClient->authenticate($request->phone_number, $request->password);

            if (!$cognitoResult) {
                // Authentication failed
                return response()->json(['message' => 'Authentication failed.'], 401);
            }
            $token = getCognitoTokeDetails($cognitoResult->get('AuthenticationResult')['AccessToken']);
            $user = $this->userRepository->findOneBy(['sub' => $token['sub']]);
            if (!$user) {
                return response()->json(['message' => 'User not found.'], 404);
            }
            return response()->json(array_merge($cognitoResult->get('AuthenticationResult'), ['user' => new UserResource($user)]));
        } catch (CognitoIdentityProviderException $e) {
            return response()->json(['message' => $e->getAwsErrorMessage()], 401);
        }
    }

    public function resendVerificationCode(PhoneNumberVerificationUserRequest $request)
    {
        try {
            $phoneNumber = $request->phone_number;

            // Only send verification code via Twilio for specific country codes
            if ($this->isTwilioSupportedCountry($phoneNumber)) {

                $this->smsProvider->sendVerificationCode($phoneNumber);
            } else {
                $this->cognitoClient->resendVerificationCode($request->phone_number);
            }
            return response()->json(['message' => 'Verification code resent successfully'], 200);
        } catch (CognitoIdentityProviderException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
    public function forgotPassword(PhoneNumberVerificationUserRequest $request)
    {
        try {
            $phoneNumber = $request->phone_number;
            // Only send verification code via Twilio for specific country codes
            if ($this->isTwilioSupportedCountry($phoneNumber)) {
                $this->smsProvider->sendVerificationCode($phoneNumber);
            } else {
                $this->cognitoClient->forgotPassword($phoneNumber);
            }
            return response()->json(['message' => 'Password reset code sent successfully'], 200);
        } catch (CognitoIdentityProviderException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    public function resetPassword(ResetPasswordUserRequest $request)
    {
        try {
            $phoneNumber = $request->phone_number;
            // Only send verification code via Twilio for specific country codes
            if ($this->isTwilioSupportedCountry($phoneNumber)) {
                $twilioVerification = $this->smsProvider->checkVerificationCode($phoneNumber, $request->code);
                if ($twilioVerification->status === 'approved') {
                    // If Twilio verification successful
                    $this->cognitoClient->adminSetUserPassword($phoneNumber, $request->password);
                }
            } else {
                $this->cognitoClient->resetPassword($phoneNumber, $request->code, $request->password);
            }
            return response()->json(['message' => 'Password reset successful'], 200);
        } catch (CognitoIdentityProviderException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    public function getProfile($userId)
    {
        $profileDetails = $this->userRepository->getProfileDetails($userId);

        return new UserProfileDetailsResourceCollection($profileDetails);

    }

    public function findByContacts(FindByContactsUserRequest $request)
    {

        $contactList = $request->input('contact_list');

        $userAndcontacts = $this->userRepository->findByContacts($contactList);

        return new UserContactsResource($userAndcontacts);

    }

    public function uploadOrUpdateProfilePic(ProfilePicUploadUserRequest $request)
    {
        $image = $request->file('profile_pic');

        $user = $this->userRepository->uploadOrUpdateProfilePic($image, $request->user_id);

        return new UserResource($user);

    }
    public function findByPhoneNumber(PhoneNumberVerificationUserRequest $request)
    {

        $user = $this->userRepository->findByPhoneNumber($request->phone_number);

        if ($user && $user['first_name'] === null) {

            $this->cognitoClient->deleteCognitoUser($user->phone_number);

            $this->userRepository->deleteUserRelatedData($user);

            return response()->json(null, 204);

        }

        if ($user === null) {
            throw new ModelNotFoundException();
        }

        return new UserResource($user);

    }

    public function addAdmin(PhoneNumberVerificationUserRequest $request)
    {
        $phoneNumber = $request->phone_number;

        $user = $this->userRepository->findOneBy(['phone_number' => $phoneNumber]);

        if (!$user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        $user = $this->userRepository->update($user, ['is_admin' => true]);

        // Add user to the Admins group
        $this->cognitoClient->addUserToAdminsGroup($phoneNumber);

        return response()->json(['message' => 'User added to Admins successfully'], 200);
    }

    /**
     * Check if phone number belongs to a Twilio-supported country
     * 
     * @param string $phoneNumber
     * @return bool
     */
    private function isTwilioSupportedCountry($phoneNumber)
    {
        $countryCodes = ['+44', '+86', '+91']; // UK, China, India
        foreach ($countryCodes as $code) {
            if (strpos($phoneNumber, $code) === 0) {
                return true;
            }
        }
        return false;
    }

}
