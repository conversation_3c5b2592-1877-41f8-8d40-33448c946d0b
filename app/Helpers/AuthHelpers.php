<?php


use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use \Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Cache;


/**
 * get jwks
 *
 * @return array
 */
function getJwks(): array
{
    // todo - don't directly use env variable set in conifg/auth.php
    $jwksUrl = 'https://cognito-idp.' . env('AWS_COGNITO_REGION') . '.amazonaws.com/' . env('AWS_COGNITO_USER_POOL_ID') . '/.well-known/jwks.json';

    $cachingMintues = 24 * 60; // 24 hours
    return Cache::remember('jwks', $cachingMintues, function () use ($jwksUrl) {
        $jwksClient = new \GuzzleHttp\Client();
        $jwksResponse = $jwksClient->get($jwksUrl);
        return json_decode($jwksResponse->getBody(), true);
    });

}

/**
 * get token details
 *
 * @param null $token
 * @return array
 * @throws ValidationException
 */
function getCognitoTokeDetails($token = null) : array
{
    try {
        $jwks = getJwks();
        $token = $token ?? request()->bearerToken();
        $decodedToken = JWT::decode($token, JWK::parseKeySet($jwks));
        return (array) $decodedToken;
    } catch (\Exception $e) {
        throw ValidationException::withMessages([
            'token' => ['Invalid token'],
        ]);
    }
}
