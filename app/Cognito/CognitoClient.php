<?php

namespace App\Cognito;

use App\Models\User;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Aws\Result;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

class CognitoClient
{
    const NEW_PASSWORD_CHALLENGE = 'NEW_PASSWORD_REQUIRED';
    const FORCE_PASSWORD_STATUS = 'FORCE_CHANGE_PASSWORD';
    const RESET_REQUIRED = 'PasswordResetRequiredException';
    const USER_NOT_FOUND = 'UserNotFoundException';
    const USERNAME_EXISTS = 'UsernameExistsException';
    const INVALID_PASSWORD = 'InvalidPasswordException';
    const CODE_MISMATCH = 'CodeMismatchException';
    const EXPIRED_CODE = 'ExpiredCodeException';
    const ADMINS_GROUP = 'admins';

    /**
     * @var CognitoIdentityProviderClient
     */
    protected $client;

    /**
     * @var string
     */
    protected $clientId;

    /**
     * @var string
     */
    protected $clientSecret;

    /**
     * @var string
     */
    protected $poolId;

    /**
     * CognitoClient constructor.
     * @param CognitoIdentityProviderClient $client
     * @param string $clientId
     * @param string $clientSecret
     * @param string $poolId
     */
    public function __construct(
        CognitoIdentityProviderClient $client,
        $clientId,
        $clientSecret,
        $poolId
    ) {
        $this->client = $client;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->poolId = $poolId;
    }

    /**
     * Checks if credentials of a user are valid
     *
     * @see http://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_AdminInitiateAuth.html
     * @param string $email
     * @param string $password
     * @return \Aws\Result|bool
     */
    public function authenticate($email, $password)
    {
        try {
            $response = $this->client->adminInitiateAuth([
                'AuthFlow' => 'ADMIN_NO_SRP_AUTH',
                'AuthParameters' => [
                    'USERNAME' => $email,
                    'PASSWORD' => $password,
                    'SECRET_HASH' => $this->cognitoSecretHash($email),
                ],
                'ClientId' => $this->clientId,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $exception) {
            if (
                $exception->getAwsErrorCode() === self::RESET_REQUIRED ||
                $exception->getAwsErrorCode() === self::USER_NOT_FOUND
            ) {
                return false;
            }

            throw $exception;
        }

        return $response;
    }

    public function authenticateWithoutPassword($phoneNumber)
    {
        try {
            // Use the 'ADMIN_NO_SRP_AUTH' flow for authentication without a password
            $result = $this->client->adminInitiateAuth([
                'UserPoolId' => $this->poolId,
                'ClientId' => $this->clientId,
                'AuthFlow' => 'ADMIN_NO_SRP_AUTH',
                'AuthParameters' => [
                    'USERNAME' => $phoneNumber,
                ],
            ]);

            // Return the result containing the authentication details
            return $result;
        } catch (CognitoIdentityProviderException $e) {
            Log::error('Cognito authentication error: ' . $e->getAwsErrorMessage());
            return false;
        } catch (\Exception $e) {
            Log::error('General authentication error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Registers a user in the given user pool
     *
     * @param array $data
     * @return Result | bool
     */
    public function register(array $data)
    {
        try {

            $userFields = ['phone_number'];

            foreach ($userFields as $userField) {
                $attributes[$userField] = $data[$userField];
            }

            return $this->client->signUp([
                'ClientId' => $this->clientId,
                'Password' => User::TEMPORARY_PASSWORD,
                'SecretHash' => $this->cognitoSecretHash($data['phone_number']),
                'UserAttributes' => $this->formatAttributes($attributes),
                'Username' => $data['phone_number'],
            ]);

        } catch (CognitoIdentityProviderException $e) {
            if ($e->getAwsErrorCode() === self::USERNAME_EXISTS) {
                throw ValidationException::withMessages(['username' => 'Username exists!']);
            }

            Log::debug($e->getMessage());
            throw ValidationException::withMessages(['username' => 'Error in registering user!']);
        }
    }

    /**
     * Authenticate a user using Cognito
     *
     * @param string $email
     * @param string $password
     * @return bool
     * @throws CognitoIdentityProviderException
     */
    public function login($email, $password)
    {
        try {
            $response = $this->client->adminInitiateAuth([
                'AuthFlow' => 'ADMIN_NO_SRP_AUTH',
                'AuthParameters' => [
                    'USERNAME' => $email,
                    'PASSWORD' => $password,
                    'SECRET_HASH' => $this->cognitoSecretHash($email),
                ],
                'ClientId' => $this->clientId,
                'UserPoolId' => $this->poolId,
            ]);
            return $response;
        } catch (CognitoIdentityProviderException $exception) {
            return false;
        }
    }

    /**
     * Send a password reset code to a user.
     * @see http://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_ForgotPassword.html
     *
     * @param  string $username
     * @return string
     */
    public function sendResetLink($username)
    {
        try {
            $result = $this->client->forgotPassword([
                'ClientId' => $this->clientId,
                'SecretHash' => $this->cognitoSecretHash($username),
                'Username' => $username,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            if ($e->getAwsErrorCode() === self::USER_NOT_FOUND) {
                return Password::INVALID_USER;
            }

            throw $e;
        }

        return Password::RESET_LINK_SENT;
    }

    # HELPER FUNCTIONS

    /**
     * Set a users attributes.
     * http://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_AdminUpdateUserAttributes.html
     *
     * @param string $username
     * @param array  $attributes
     * @return bool
     */
    public function setUserAttributes($username, array $attributes)
    {
        $this->client->AdminUpdateUserAttributes([
            'Username' => $username,
            'UserPoolId' => $this->poolId,
            'UserAttributes' => $this->formatAttributes($attributes),
        ]);

        return true;
    }

    /**
     * Creates the Cognito secret hash
     * @param string $username
     * @return string
     */
    protected function cognitoSecretHash($username)
    {
        return $this->hash($username . $this->clientId);
    }

    /**
     * Creates a HMAC from a string
     *
     * @param string $message
     * @return string
     */
    protected function hash($message)
    {
        $hash = hash_hmac(
            'sha256',
            $message,
            $this->clientSecret,
            true
        );

        return base64_encode($hash);
    }

    /**
     * Get user details.
     * http://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_GetUser.html
     *
     * @param  string $username
     * @return mixed
     */
    public function getUser($username)
    {
        try {
            $user = $this->client->AdminGetUser([
                'Username' => $username,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return $user;
    }

    /**
     * Format attributes in Name/Value array
     *
     * @param  array $attributes
     * @return array
     */
    protected function formatAttributes(array $attributes)
    {
        $userAttributes = [];

        foreach ($attributes as $key => $value) {
            $userAttributes[] = [
                'Name' => $key,
                'Value' => $value,
            ];
        }

        return $userAttributes;
    }

    /**
     * Verify a user using Cognito
     *
     * @param string $email
     * @param string $code
     * @return bool
     * @throws CognitoIdentityProviderException
     */
    public function verify($email, $code)
    {
        // Check if not in production environment and the provided code is '12345'
        if (env('APP_ENV') !== 'production' && $code === '123456') {
            // Automatically confirm sign-up
            try {
                $this->client->adminConfirmSignUp([
                    'Username' => $email,
                    'UserPoolId' => $this->poolId,
                ]);
                return true;
            } catch (CognitoIdentityProviderException $exception) {
                return false;
            }
        }

        // If in production or code is not '12345', perform normal verification
        try {
            return $this->client->confirmSignUp([
                'ClientId' => $this->clientId,
                'ConfirmationCode' => $code,
                'Username' => $email,
                'SecretHash' => $this->cognitoSecretHash($email),
            ]);
        } catch (CognitoIdentityProviderException $exception) {
            return false;
        }
    }

    public function resendVerificationCode($email)
    {
        if (env('APP_ENV') !== 'production') {
            // Return true without making the AWS Cognito call
            return true;
        }

        try {
            $this->client->resendConfirmationCode([
                'ClientId' => $this->clientId,
                'Username' => $email,
                'SecretHash' => $this->cognitoSecretHash($email),
            ]);
        } catch (CognitoIdentityProviderException $e) {
            throw $e;
        }
    }

    public function forgotPassword($email)
    {
        if (env('APP_ENV') !== 'production') {
            // Return true without making the AWS Cognito call
            return true;
        }

        try {
            $this->client->forgotPassword([
                'ClientId' => $this->clientId,
                'SecretHash' => $this->cognitoSecretHash($email),
                'Username' => $email,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            throw $e;
        }
    }

    public function resetPassword($email, $code, $newPassword)
    {
        try {
            // Check if not in production environment and the provided code is '123456'
            if (env('APP_ENV') !== 'production' && $code === '123456') {
                // Bypass actual AWS Cognito call and directly set the password
                return $this->adminSetUserPassword($email, $newPassword);
            }

            $this->client->confirmForgotPassword([
                'ClientId' => $this->clientId,
                'Username' => $email,
                'SecretHash' => $this->cognitoSecretHash($email),
                'ConfirmationCode' => $code,
                'Password' => $newPassword,
            ]);

        } catch (CognitoIdentityProviderException $e) {
            throw $e;
        }
    }

    public function adminSetUserPassword($email, $newPassword)
    {
        try {
            // Directly set the user's password without requiring confirmation
            $this->client->adminSetUserPassword([
                'Username' => $email,
                'Password' => $newPassword,
                'Permanent' => true,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $exception) {
            throw $exception;
        }
    }

/**
 * Adds a user to the Admins user group.
 *
 * @param string $phoneNumber
 * @return bool
 */
    public function addUserToAdminsGroup($phoneNumber)
    {
        try {
            $result = $this->client->adminAddUserToGroup([
                'GroupName' => self::ADMINS_GROUP,
                'UserPoolId' => $this->poolId,
                'Username' => $phoneNumber,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return true;
    }

/**
 * Updates the user attribute 'isAdmin' to true.
 *
 * @param string $phoneNumber
 * @return bool
 */
    public function updateUserAttributeIsAdmin($phoneNumber)
    {
        try {
            $this->client->adminUpdateUserAttributes([
                'UserPoolId' => $this->poolId,
                'Username' => $phoneNumber,
                'UserAttributes' => [
                    [
                        'Name' => 'custom:isAdmin',
                        'Value' => 'true',
                    ],
                ],
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return true;
    }

    /**
     * Retrieve user attributes based on the phone number and check if the user is an admin.
     *
     * @param string $phoneNumber
     * @return bool
     */
    public function getUserAttributesAndCheckIsAdmin($phoneNumber)
    {
        try {
            $user = $this->client->adminGetUser([
                'UserPoolId' => $this->poolId,
                'Username' => $phoneNumber,
            ]);

            $userAttributes = $user['UserAttributes'];

            $isAdmin = false;

            foreach ($userAttributes as $attribute) {
                if ($attribute['Name'] === 'custom:isAdmin' && $attribute['Value'] === 'true') {
                    $isAdmin = true;
                    break;
                }
            }

            return $isAdmin;
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }
    }

    /**
     * Check if the user belongs to the Admins group.
     *
     * @param string $phoneNumber
     * @return bool
     */
    public function isInAdminsGroup($phoneNumber)
    {
        try {
            $result = $this->client->adminListGroupsForUser([
                'UserPoolId' => $this->poolId,
                'Username' => $phoneNumber,
            ]);

            foreach ($result['Groups'] as $group) {
                if ($group['GroupName'] === self::ADMINS_GROUP) {
                    return true;
                }
            }

            return false; // User not in Admins group
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }
    }

    public function deleteCognitoUser($email)
    {
        try {
            $this->client->adminDeleteUser([
                'Username' => $email,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return true;
    }

    public function disableCognitoUser($email)
    {
        try {
            $this->client->adminDisableUser([
                'Username' => $email,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return true;
    }

    public function enableCognitoUser($email)
    {
        try {
            $this->client->adminEnableUser([
                'Username' => $email,
                'UserPoolId' => $this->poolId,
            ]);
        } catch (CognitoIdentityProviderException $e) {
            return false;
        }

        return true;
    }

    /**
     * Verify account using phone number by sending an OTP.
     *
     * @param string $phoneNumber
     * @return bool|string Returns OTP sent status or false if there was an error
     */
    public function verifyAccountWithNumber($phoneNumber)
    {
        try {
            $cognitoUserData = $this->client->adminCreateUser([
                'UserPoolId' => $this->poolId,
                'Username' => $phoneNumber,
                'UserAttributes' => [
                    [
                        'Name' => 'phone_number',
                        'Value' => $phoneNumber,
                    ],
                    [
                        'Name' => 'phone_number_verified',
                        'Value' => 'false',
                    ],
                ],
                'DesiredDeliveryMediums' => ['SMS'],
            ]);

            $sub = array_column($cognitoUserData['User']['Attributes'], 'Value', 'Name')['sub'] ?? null;
            return $sub;
        } catch (CognitoIdentityProviderException $e) {
            if ($e->getAwsErrorCode() === self::USERNAME_EXISTS) {
                throw ValidationException::withMessages(['phone_number' => 'Phone Number exists!']);
            }

            Log::debug($e->getMessage());
            throw ValidationException::withMessages(['phone_number' => 'Error in registering user!']);
        }
    }

    /**
     * Set password for a verified account.
     *
     * @param string $phoneNumber
     * @param string $newPassword
     * @return bool
     * @throws CognitoIdentityProviderException
     */
    public function setPassword($phoneNumber, $newPassword)
    {
        try {
            // Directly set the user's password without requiring confirmation
            $this->adminSetUserPassword($phoneNumber, $newPassword);

            return true;
        } catch (CognitoIdentityProviderException $e) {
            Log::debug($e->getMessage());
            throw $e;
        }
    }

}
