<?php
namespace App\Repositories;

use App\Events\ConnectionRequestNotification;
use App\Models\ConnectionRequestStatus;
use App\Models\User;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use App\Repositories\Contracts\RelationshipRepository;
use App\Repositories\Contracts\UserRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EloquentConnectionRequestRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\ConnectionRequestRepository
{
    public function createOrUpdate(array $data)
    {
        $relationshipRepository            = app(RelationshipRepository::class);
        $connectionRequestStatusRepository = app(ConnectionRequestStatusRepository::class);

        $connectionRequestStatus = $connectionRequestStatusRepository->findOneBy(['title' => $data['connection_request_status']]);

        $relationship = $relationshipRepository->findRelationship(
            $data['from_user_id'], $data['to_user_id'] ?? null
        );

        // Handle recovery status
        if ($data['connection_request_status'] === ConnectionRequestStatus::STATUS_RECOVER) {
            if ($relationship && $relationship->connectionRequest) {
                $pendingStatus = $connectionRequestStatusRepository->findOneBy(['title' => ConnectionRequestStatus::STATUS_PENDING]);
                return $this->update($relationship->connectionRequest, [
                    'status_id' => $pendingStatus->id,
                ]);
            }
        }

        // If status is being set to discarded, delete unanswered questions
        if ($data['connection_request_status'] === ConnectionRequestStatus::STATUS_DISCARDED && $relationship) {
            DB::table('question_sends')
                ->where(function ($query) use ($relationship) {
                    $query->where(function ($q) use ($relationship) {
                        $q->where('sent_from_user_id', $relationship->from_user_id)
                            ->where('sent_to_user_id', $relationship->to_user_id);
                    })
                        ->orWhere(function ($q) use ($relationship) {
                            $q->where('sent_from_user_id', $relationship->to_user_id)
                                ->where('sent_to_user_id', $relationship->from_user_id);
                        });
                })
                ->whereNull('video_id') // Only delete unanswered questions
                ->delete();
        }

        // If relationship exists and is approved, prevent new pending request
        if ($relationship &&
            $relationship->connectionRequest &&
            $relationship->connectionRequest->status->title === ConnectionRequestStatus::STATUS_APPROVED &&
            $data['connection_request_status'] === 'pending') {

            throw new \InvalidArgumentException('Cannot create pending request: Connection is already approved');
        }

        // If sending new pending request
        if ($data['connection_request_status'] === 'pending') {
            // If relationship exists but was discarded, delete everything and create new
            if ($relationship &&
                $relationship->connectionRequest &&
                $relationship->connectionRequest->status->title === ConnectionRequestStatus::STATUS_DISCARDED) {

                $this->deleteConnectionRequestAndRelatedData($relationship->connectionRequest->id);
                $relationship = null;
            }

            // Create new relationship if none exists
            if (! $relationship) {
                $relationship = $relationshipRepository->save([
                    'from_user_id'     => $data['from_user_id'],
                    'to_user_id'       => $data['to_user_id'],
                    'name_as_from'     => $data['name_as_from'],
                    'name'             => $data['name_as_outsider'],
                    'name_as_outsider' => $data['name_as_outsider'],
                ]);

                $connectionRequest = $this->save([
                    'relationship_id' => $relationship->id,
                    'status_id'       => $connectionRequestStatus->id,
                ]);
            }
        } else {
            $connectionRequest = $this->findOneBy([
                'relationship_id' => $relationship->id,
            ]);

            $connectionRequest = $this->update($connectionRequest, [
                'relationship_id' => $relationship->id,
                'status_id'       => $connectionRequestStatus->id,
            ]);
        }
      
        // Only fire notification event if not discarded or resent
        if ($data['connection_request_status'] !== ConnectionRequestStatus::STATUS_DISCARDED && 
            $data['connection_request_status'] !== ConnectionRequestStatus::STATUS_RESENT) {
            event(new ConnectionRequestNotification($connectionRequest));
        }

        return $connectionRequest;
    }

    /**
     * Get the profiles of users connected to the authenticated user (or a target user if provided).
     *
     * @param int|null $targetUserId Optionally filter by a specific target user's relationships.
     *
     * @return array List of connected user IDs (including the authenticated user or target user).
     */
    public function getConnectedUserProfiles($targetUserId = null)
    {

        $userRepository = app(UserRepository::class);

        // Use the authenticated user if no target user is provided.
        $user = $targetUserId
        ? $userRepository->findOne($targetUserId)
        : Auth::user();

        $relationships = $user->fromUserRelationships->merge($user->toUserRelationships);

        $filteredRelationships = $relationships->filter(function ($relationship) {
            return $relationship->connectionRequest &&
            $relationship->connectionRequest->status &&
            ! in_array(
                $relationship->connectionRequest->status->title,
                [
                    ConnectionRequestStatus::STATUS_DISCARDED,
                    ConnectionRequestStatus::STATUS_INVITED,
                    ConnectionRequestStatus::STATUS_PENDING,
                    ConnectionRequestStatus::STATUS_RESENT,
                ]
            );
        });

        $userIds = $filteredRelationships->map(function ($relationship) use ($user) {
            return ($relationship->from_user_id === $user->id) ? $relationship->to_user_id : $relationship->from_user_id;
        })->toArray();

        $userIds[] = $user->id;

        return $userIds;
    }

    public function getDiscardedConnectionRequests()
    {
        $authUser                          = Auth::user();
        $relationshipRepository            = app(RelationshipRepository::class);
        $connectionRequestStatusRepository = app(ConnectionRequestStatusRepository::class);

        // Get discarded status
        $discardedStatus = $connectionRequestStatusRepository->findOneBy([
            'title' => ConnectionRequestStatus::STATUS_DISCARDED,
        ]);

        // Get relationships where auth user is either from_user or to_user
        $relationships = $relationshipRepository->findBy([
            'from_user_id' => $authUser->id,
        ])->merge($relationshipRepository->findBy([
            'to_user_id' => $authUser->id,
        ]));

        // Filter relationships and map to connection requests
        $discardedRequests = $relationships->filter(function ($relationship) use ($discardedStatus) {
            return $relationship->connectionRequest &&
            $relationship->connectionRequest->status_id === $discardedStatus->id;
        })->map(function ($relationship) {
            return $relationship->connectionRequest;
        });

        return $discardedRequests;
    }

    /**
     * Delete a connection request and all related data.
     * This includes:
     * - All question sends between the users
     * - The relationship between users
     * - The connection request itself
     *
     * @param int $connectionRequestId The ID of the connection request to delete
     * @return bool True if deletion was successful
     * @throws ModelNotFoundException If connection request not found
     * @throws \Exception If deletion fails
     */
    public function deleteConnectionRequestAndRelatedData($connectionRequestId)
    {
        try {
            DB::beginTransaction();

            $connectionRequest = $this->findOne($connectionRequestId);
            if (! $connectionRequest) {
                throw new ModelNotFoundException('Connection request not found');
            }

            // Get the relationship
            $relationship = $connectionRequest->relationship;
            if ($relationship) {
                // Delete question sends using query builder
                DB::table('question_sends')
                    ->where(function ($query) use ($relationship) {
                        $query->where(function ($q) use ($relationship) {
                            $q->where('sent_from_user_id', $relationship->from_user_id)
                                ->where('sent_to_user_id', $relationship->to_user_id);
                        })
                            ->orWhere(function ($q) use ($relationship) {
                                $q->where('sent_from_user_id', $relationship->to_user_id)
                                    ->where('sent_to_user_id', $relationship->from_user_id);
                            });
                    })
                    ->whereNull('video_id') // Only delete unanswered questions
                    ->delete();

                $relationship->delete();
            }

            // Delete connection request using repository
            $this->delete($connectionRequest);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting connection request and related data: ' . $e->getMessage());
            throw $e;
        }
    }
}
