<?php

namespace App\Repositories;

use App\Models\Relationship;
use App\Models\Video;

class EloquentFamilyMemberRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\FamilyMemberRepository

{
    public function getFamilyMembers($userId)
    {
        // Initiator relationships
        $initiator = Relationship::with('toUser')
            ->where('from_user_id', $userId)
            ->where('is_hidden', 1)
            ->get();

        // Receiver relationships
        $receiver = Relationship::with('fromUser')
            ->where('to_user_id', $userId)
            ->where('is_hidden', 1)
            ->get();

        $familyMembers = collect();

        // Process initiator relationships
        foreach ($initiator as $relationship) {
            $fromUser = $relationship->toUser;

            if ($fromUser !== null) {
                $userId = $fromUser->id;
                $username = $fromUser->username;
                $profilePic = $fromUser->profile_pic;

                // Call familyMemberCount to get the total family member count
                $familyMemberCount = $this->familyMemberCount($userId);

                $familyMembers->push([
                    'id' => $userId,
                    'username' => $username,
                    'profile_pic' => $profilePic,
                    'family_member_count' => $familyMemberCount,
                    'relationshipName' => $fromUser->name_as_from,
                ]);
            }
        }

        // Process receiver relationships
        foreach ($receiver as $relationship) {
            $toUser = $relationship->fromUser;

            if ($toUser !== null) {
                $userId = $toUser->id;
                $username = $toUser->username;
                $profilePic = $toUser->profile_pic;

                // Call familyMemberCount to get the total family member count
                $familyMemberCount = $this->familyMemberCount($userId);

                $familyMembers->push([
                    'id' => $userId,
                    'username' => $username,
                    'profile_pic' => $profilePic,
                    'family_member_count' => $familyMemberCount,
                    'relationshipName' => $toUser->name_as_outsider,
                ]);
            }
        }

        return $familyMembers;
    }

    public function familyMemberCount($userId)
    {
        // Get the count of both initiator and receiver family members for the specified user ID
        $count = Relationship::where(function ($query) use ($userId) {
            $query->where('from_user_id', $userId)
                ->orWhere('to_user_id', $userId);
        })
            ->where('is_hidden', 1)
            ->count();

        return $count;
    }
    public function getFollowers($userId)
    {
        $followers = Relationship::with('fromUser')
            ->where('to_user_id', $userId)
            ->where('is_hidden', 1)
            ->get();

        $followerList = [];

        foreach ($followers as $follower) {
            $fromUser = $follower->fromUser;

            if ($fromUser !== null) {
                $userId = $fromUser->id;
                $username = $fromUser->username;
                $profilePic = $fromUser->profile_pic;

                $followerList[] = [
                    'id' => $userId,
                    'username' => $username,
                    'profile_pic' => $profilePic,
                    'family_member_count' => $this->familyMemberCount($userId),
                    'relationshipName' => $follower->name_as_outsider,
                ];
            }
        }

        return $followerList;
    }

    public function getFollowing($userId)
    {
        $following = Relationship::with('toUser')
            ->where('from_user_id', $userId)
            ->where('is_hidden', 1)
            ->get();

        $followingList = [];

        foreach ($following as $followed) {
            $toUser = $followed->toUser;

            if ($toUser !== null) {
                $userId = $toUser->id;
                $username = $toUser->username;
                $profilePic = $toUser->profile_pic;

                $followingList[] = [
                    'id' => $userId,
                    'username' => $username,
                    'profile_pic' => $profilePic,
                    'family_member_count' => $this->familyMemberCount($userId),
                    'relationshipName' => $following->name_as_from,
                ];
            }
        }

        return $followingList;
    }

    public function getHomeFeedVideos($userId)
    {
        // Retrieve family members
        $familyMembers = $this->getFamilyMembers($userId);

        // Get the latest videos from family members
        $latestVideos = Video::whereIn('owner_user_id', $familyMembers->pluck('id'))
            ->latest()
            ->take(10)
            ->get();

        return $latestVideos;
    }
}
