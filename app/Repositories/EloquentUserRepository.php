<?php

namespace App\Repositories;

use App\Models\QuestionActivity;
use App\Models\QuestionSend;
use App\Models\User;
use App\Models\VideoComment;
use App\Models\VideoView;
use App\Repositories\Contracts\InvitationRepository;
use App\Repositories\Contracts\MembershipGiftRepository;
use App\Repositories\Contracts\RelationshipRepository;
use Aws\Result;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Events\MembershipNotification;
use Carbon\Carbon;

class EloquentUserRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\UserRepository
{

    public function findBy(array $searchCriteria = [], $withTrashed = false)
    {
        if (isset($searchCriteria['query'])) {
            $searchCriteria['id'] = $this->model->where('first_name', 'like', '%' . $searchCriteria['query'] . '%')
                ->orWhere('last_name', 'like', '%' . $searchCriteria['query'] . '%')
                ->orWhere('phone_number', 'like', '%' . $searchCriteria['query'] . '%')
                ->pluck('id')->toArray();
            unset($searchCriteria['query']);
        }

        return parent::findBy($searchCriteria);
    }
    /**
     * register a user
     *
     * @param array $data
     * @return User
     */
    public function registerUser(array $data, Result $cognitoUserData)
    {
        // $data['full_name'] = trim($data['first_name'] . ' ' . ($data['middle_name'] ?? '') . ' ' . $data['last_name']);
        $data['sub'] = $cognitoUserData->get('UserSub');
        $data['registration_status'] = $cognitoUserData->get('UserConfirmed') ? User::REGISTRATION_STATUS_VERIFIED : User::REGISTRATION_STATUS_UNCONFIRMED;

        return $this->save($data);

    }

    /**
     * verify a user
     *
     * @param string $username
     * @return \ArrayAccess | bool
     */
    public function verifyUser(string $phoneNumber)
    {
        $user = $this->findOneBy(['phone_number' => $phoneNumber, 'registration_status' => User::REGISTRATION_STATUS_UNCONFIRMED]);

        if (!$user) {
            return false;
        }

        return $this->update($user, ['registration_status' => User::REGISTRATION_STATUS_VERIFIED]);
    }

    /**
     * Get user profile details including relationships and stories.
     *
     * @param int $userId
     * @return array
     * @throws ModelNotFoundException
     */
    public function getProfileDetails(int $userId)
    {
        // Find the user by ID or throw an exception if not found
        $user = $this->findOne($userId);
        if ($user === null) {
            throw new ModelNotFoundException();
        }

        $authUser = Auth::user();
        $relationship = null;

        // Check if the authenticated user is viewing another user's profile
        if ($authUser->id !== $userId) {
            $relationshipRepository = app(RelationshipRepository::class);

            // Check if there is a relationship initiated by the authenticated user to the requested user
            $relationship = $relationshipRepository->findOneBy([
                'from_user_id' => $authUser->id,
                'to_user_id' => $user->id ?? null,
            ]);

            // If no relationship is found, check if there is a relationship initiated by the requested user to the authenticated user
            if (!$relationship) {
                $relationship = $relationshipRepository->findOneBy([
                    'from_user_id' => $user->id,
                    'to_user_id' => $authUser->id,
                ]);
            }
        }

        // Calculate total family count using the mapRelationships method
        $fromUserRelationships = $this->mapRelationships($user->fromUserRelationships, 'fromUser');
        $toUserRelationships = $this->mapRelationships($user->toUserRelationships, 'toUser');

        $totalFamilyCount = count($fromUserRelationships) + count($toUserRelationships);

        // Return the user profile details
        return [
            'id' => $user->id,
            'phone_number' => $user->phone_number,
            'first_name' => $user->first_name,
            'middle_name' => $user->middle_name,
            'last_name' => $user->last_name,
            'profile_pic' => $user->profile_pic,
            'total_family_count' => $totalFamilyCount,
            'stories' => $user->videos->count(),
            'question_sends' => $user->questionSends->count(),
            // Include relationship details if available
            'relationship' => $relationship,
            'fromUserRelationshipsDetails' => array_values($fromUserRelationships->toArray()),
            'toUserRelationshipsDetails' => array_values($toUserRelationships->toArray()),
            'membership' => $user->membership,
            'membership_type' => $user->membership?->membershipType?->name,
            'sent_gifts' => $user->sentGifts,
            'received_gifts' => $user->receivedGifts,
        ];
    }

    /**
     * Map user relationships based on relationship type (fromUser/toUser).
     *
     * @param \Illuminate\Database\Eloquent\Collection $relationships
     * @param string $relationshipType
     * @return \Illuminate\Support\Collection
     */
    private function mapRelationships($relationships, $relationshipType)
    {
        return $relationships->map(function ($relationship) use ($relationshipType) {
            return $this->mapUserData($relationship, $relationshipType);
        })->filter(); // Use filter to remove null values (relationships with 'discarded' status)
    }

    /**
     * Map user data within a relationship.
     *
     * @param \App\Models\Relationship $relationship
     * @param string $userType
     * @return array|null
     */
    private function mapUserData($relationship, $userType)
    {
        if ($userType == 'fromUser' && $relationship?->connectionRequest?->status?->title !== 'discarded') {
            $currentUser = $relationship->toUser;

        } elseif ($userType == 'toUser' && $relationship?->connectionRequest?->status?->title !== 'discarded') {
            $currentUser = $relationship->fromUser;

        } else {
            return null; // Return null for relationships with 'discarded' status
        }

        // If the invited user has no details, return relationship information without user details
        if ($currentUser === null) {

            $invitationRepository = app(InvitationRepository::class);
            $membershipGiftRepository = app(MembershipGiftRepository::class);
            $invitation = $invitationRepository->findOne($relationship->to_invitation_id);
            $membershipGifts = $membershipGiftRepository->findBy(['invitation_id' => $invitation->id]);


            return [
                'invitation_id' => $invitation->id,
                'relationship_id' => $relationship->id,
                'name_as_from' => $relationship->name_as_from,
                'name' => $relationship->name,
                'to_phone_number' => $invitation->to_phone_number,
                'name_as_outsider' => $relationship->name_as_outsider,
                'created_at' => $relationship->created_at,
                'updated_at' => $relationship->updated_at,
                'connection_request_status' => $relationship?->connectionRequest?->status?->title,
                'received_gifts' => $invitation->membershipGift
            ];
        }

        // Exclude relationships with 'discarded' status from the count
        $fromUserCount = $currentUser->fromUserRelationships->where('connectionRequest.status.title', '!=', 'discarded')->count();
        $toUserCount = $currentUser->toUserRelationships->where('connectionRequest.status.title', '!=', 'discarded')->count();

        // Calculate total family count and stories count for the current user
        $totalFamilyCount = $fromUserCount + $toUserCount;
        $storiesCount = $currentUser->videos->count();

        // Return detailed user information within the relationship
        return [
            'id' => $currentUser->id,
            'first_name' => $currentUser->first_name,
            'middle_name' => $currentUser->middle_name,
            'last_name' => $currentUser->last_name,
            'phone_number' => $currentUser->phone_number,
            'profile_pic' => $currentUser->profile_pic ? Storage::temporaryUrl($currentUser->profile_pic, now()->addDays(7)) : null,
            'total_family_count' => $totalFamilyCount,
            'stories' => $storiesCount,
            // 'question_sends' => $currentUser->questionSends->count(),
            'relationship_id' => $relationship->id,
            'name_as_from' => $relationship->name_as_from,
            'name_as_outsider' => $relationship->name_as_outsider,
            'created_at' => $relationship->created_at,
            'updated_at' => $relationship->updated_at,
            'connection_request_status' => $relationship?->connectionRequest?->status?->title,
            'membership' => $currentUser->membership,
            'membership_type' => $currentUser->membership?->membershipType?->name,
            'sent_gifts' => $currentUser->sentGifts,
            'received_gifts' => $currentUser->receivedGifts,
        ];
    }
    /**
     * Find users based on a list of contacts, providing details such as family count and stories.
     *
     * @param  array  $contactList
     * @return array
     */

    public function findByContacts(array $contactList)
    {
        // Normalize phone numbers by adding +1 to those without a country code
        $normalizedContactList = array_map(function ($contact) {
            return (strpos($contact, '+') === false) ? '+1' . $contact : $contact;
        }, $contactList);

        $authUser = Auth::user();

        // Remove auth user's phone number from the contact list
        $normalizedContactList = array_filter($normalizedContactList, function ($contact) use ($authUser) {
            return $contact !== $authUser->phone_number;
        });

        $existingUsers = User::whereIn('phone_number', $normalizedContactList)->get();

        $usersWithDetails = $existingUsers->map(function ($user) {
            $relationshipRepository = app(RelationshipRepository::class);
            $authUser = Auth::user();

            // Check if there is a relationship between the authenticated user and the existing user
            $relationship = $relationshipRepository->findRelationship($authUser->id, $user->id);

            if (!$relationship) {
                $totalFamilyCount = $user->fromUserRelationships?->where('connectionRequest.status.title', '!=', 'discarded')->count() +
                $user->toUserRelationships?->where('connectionRequest.status.title', '!=', 'discarded')->count();

                $storiesCount = $user->videos?->count();

                return [
                    'id' => $user->id,
                    'phone_number' => $user->phone_number,
                    'first_name' => $user->first_name,
                    'middle_name' => $user->middle_name,
                    'last_name' => $user->last_name,
                    'total_family_count' => $totalFamilyCount,
                    'stories' => $storiesCount,
                    'profile_pic' => $user->profile_pic ? Storage::temporaryUrl($user->profile_pic, now()->addDays(7)) : null,
                ];
            }
            return null;
        })->filter()->values(); // Filter to remove null values and reset keys

        // Get the authenticated user and their discarded relationships
        $authUser = Auth::user();
        $authUserDiscardedRelationships = collect($authUser->toUserRelationships)
            ->where('connectionRequest.status.title', '==', 'discarded')
            ->merge($authUser->fromUserRelationships->where('connectionRequest.status.title', '==', 'discarded'));

        // Map discarded relationships with detailed information
        $discardedRelationshipsDetails = $authUserDiscardedRelationships->map(function ($relationship) {
            $user = $relationship->fromUser->is(Auth::user()) ? $relationship->toUser : $relationship->fromUser;

            if (!$user) {
                return null;
            }

            $totalFamilyCount = $user->fromUserRelationships?->where('connectionRequest.status.title', '!=', 'discarded')->count() +
            $user->toUserRelationships?->where('connectionRequest.status.title', '!=', 'discarded')->count();

            $storiesCount = $user->videos?->count();

            return [
                'id' => $user->id,
                'phone_number' => $user->phone_number,
                'first_name' => $user->first_name,
                'middle_name' => $user->middle_name,
                'last_name' => $user->last_name,
                'total_family_count' => $totalFamilyCount,
                'stories' => $storiesCount,
                'profile_pic' => $user->profile_pic ? Storage::temporaryUrl($user->profile_pic, now()->addDays(7)) : null,
            ];
        })->filter()->values(); // Filter to remove null values and reset keys

        $combinedUsers = array_merge($usersWithDetails->toArray(), $discardedRelationshipsDetails->toArray());

        // Identify other contacts not found in the database
        $otherContacts = array_diff($normalizedContactList, $existingUsers->pluck('phone_number')->toArray());

        return [
            'existing_users' => $combinedUsers,
            'other_contacts' => $otherContacts,
        ];
    }

    public function uploadOrUpdateProfilePic(UploadedFile $image, $userId)
    {
        $user = $this->findOne($userId);

        $filename = 'profile_pictures/' . uniqid() . '.' . $image->getClientOriginalExtension();

        Storage::disk('s3')->put($filename, file_get_contents($image), 'public');

        return $this->update($user, ['profile_pic' => $filename]);

    }

    public function findByPhoneNumber(string $phoneNumber)
    {
        return $this->findOneBy(['phone_number' => $phoneNumber]);
    }

    public function deleteUserRelatedData(User $user)
    {
        DB::beginTransaction();

        try {
            // Store video mappings before deleting user data
            $user->videos->each(function ($video) use ($user) {
                DB::table('video_recovery_with_phone')->insert([
                    'phone_number' => $user->phone_number,
                    'video_id' => $video->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            });

            // Set video owner to null instead of deleting
            $user->videos()->update(['owner_user_id' => null]);

            //  Delete QuestionActivities related to the user's questions or QuestionSend
            $user->questions->each(function ($question) {
                $question->questionActivities()->delete();
            });

            QuestionActivity::whereHas('questionSends', function ($query) use ($user) {
                $query->where('sent_from_user_id', $user->id)
                    ->orWhere('sent_to_user_id', $user->id);
            })->delete();

            //  Delete ConnectionRequests related to the user through relationships
            $user->fromUserRelationships->each(function ($relationship) {
                $relationship->connectionRequest()->delete();
            });

            $user->toUserRelationships->each(function ($relationship) {
                $relationship->connectionRequest()->delete();
            });

            // Delete the user's questions
            $user->questions()->delete();

            // Delete the user's relationships
            $user->fromUserRelationships()->delete();
            $user->toUserRelationships()->delete();

            // Delete invitations, notifications, QuestionSend records, VideoComments, and VideoViews
            $user->invitations()->delete();
            $user->notifications()->delete();

            QuestionSend::where('sent_from_user_id', $user->id)
                ->orWhere('sent_to_user_id', $user->id)
                ->delete();

            VideoComment::where('from_user_id', $user->id)->delete();
            VideoView::where('viewer_user_id', $user->id)->delete();

            // Handle sent gifts based on membership type
            if ($user->membership && $user->membership->membershipType->name === MembershipType::TRIAL_MEMBERSHIP) {
                // For trial members, delete all sent gifts
                $user->sentGifts()->delete();
            } else {
                // For non-trial members, just set from_user_id to null
                $user->sentGifts()->update(['from_user_id' => null]);
            }
            
            // Get all gifts that need reimbursement (unaccepted or early-deleted accepted)
            $giftsToReimburse = $user->receivedGifts()
                ->where(function ($query) {
                    $query->where(function ($q) {
                        // Unaccepted gifts
                        $q->whereNull('accepted_at')
                            ->whereNull('rejected_at');
                    })->orWhere(function ($q) {
                        // Early-deleted accepted gifts (less than 30 days)
                        $q->whereNotNull('accepted_at')
                            ->whereNull('rejected_at')
                            ->where('accepted_at', '>=', Carbon::now()->subDays(30));
                    });
                })
                ->get();

            // Increment available_gifts_count for all qualifying gifts
            foreach ($giftsToReimburse as $gift) {
                if ($gift->fromUser) {
                    $gift->fromUser->increment('available_gifts_count');
                }
            }

            // Send notifications for all received gifts before deletion
            $allReceivedGifts = $user->receivedGifts()->get();
            foreach ($allReceivedGifts as $gift) {
                if ($gift->fromUser) {
                    event(new MembershipNotification(
                        'gift_membership_rejected',
                        null,
                        $gift
                    ));
                }
            }

            // Delete received gifts
            $user->receivedGifts()->delete();

            // Store membership ID before deleting user
            $membershipId = $user->membership_id;

            
            $user->videos()->delete();

            // Delete user first to avoid foreign key constraint
            $user->forceDelete();

            // Delete user's membership after user is deleted
            if ($membershipId) {
                DB::table('memberships')->where('id', $membershipId)->delete();
            }

            // Commit the transaction if everything is successful
            DB::commit();

        } catch (\Exception $e) {
            // Rollback the transaction if any operation fails
            DB::rollBack();
            throw $e;
        }
    }

}
