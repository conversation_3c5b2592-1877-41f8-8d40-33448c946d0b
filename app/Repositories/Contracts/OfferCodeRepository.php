<?php

namespace App\Repositories\Contracts;

use App\Models\OfferCode;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface OfferCodeRepository extends BaseRepository
{
    /**
     * Find offer code by code string
     */
    public function findByCode(string $code): ?OfferCode;

    /**
     * Check if user has used specific offer code
     */
    public function hasUserUsedCode(int $userId, int $offerCodeId): bool;

    /**
     * Apply offer code to user
     */
    public function applyToUser(User $user, OfferCode $offerCode): array;

    /**
     * Get user's applied offer codes
     */
    public function getUserAppliedCodes(int $userId): Collection;

    /**
     * Get active offer codes
     */
    public function getActiveCodes(): Collection;

    /**
     * Get expired offer codes
     */
    public function getExpiredCodes(): Collection;

    /**
     * Get offer codes by status
     */
    public function getByStatus(string $status): Collection;

    /**
     * Increment usage count
     */
    public function incrementUsageCount(OfferCode $offerCode): OfferCode;

    /**
     * Get offer code statistics
     */
    public function getStatistics(OfferCode $offerCode): array;

    /**
     * Search offer codes
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;
}
