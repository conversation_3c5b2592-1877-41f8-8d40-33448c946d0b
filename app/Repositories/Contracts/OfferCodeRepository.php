<?php

namespace App\Repositories\Contracts;

use App\Models\OfferCode;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface OfferCodeRepository extends BaseRepository
{
    public function findByCode(string $code): ?OfferCode;
    public function hasUserUsedCode(int $userId, int $offerCodeId): bool;
    public function applyToUser(User $user, OfferCode $offerCode): array;
    public function getUserAppliedCodes(int $userId): Collection;
    public function getActiveCodes(): Collection;
    public function getExpiredCodes(): Collection;
    public function getByStatus(string $status): Collection;
    public function incrementUsageCount(OfferCode $offerCode): OfferCode;
    public function getStatistics(OfferCode $offerCode): array;
    public function search(string $query, int $perPage = 15): LengthAwarePaginator;
}
