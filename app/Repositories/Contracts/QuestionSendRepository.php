<?php

namespace App\Repositories\Contracts;

interface QuestionSendRepository extends BaseRepository
{
    /**
     * Create a new QuestionSend and associated QuestionActivities.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * Update a QuestionSend and associated QuestionActivity based on the provided data.
     *
     * @param mixed $questionSend
     * @param array $data
     * @return mixed
     */
    public function updateQuestionSendAndActivity($questionSend, array $data);

    public function findByConnectedPeopleQuestionActivity(array $searchCriteria = []);

}
