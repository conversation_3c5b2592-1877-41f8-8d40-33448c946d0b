<?php

namespace App\Repositories\Contracts;

use App\Models\User;
use Aws\Result;
use Illuminate\Http\UploadedFile;

interface UserRepository extends BaseRepository
{
    /**
     * register a user
     *
     * @param array $data
     * @param Result $cognitoUser
     * @return mixed
     */
    public function registerUser(array $data, Result $cognitoUser);

    /**
     * verify a user
     *
     * @param string $username
     * @return mixed
     */
    public function verifyUser(string $username);

    public function getProfileDetails(int $userId);

    public function findByContacts(array $contactList);

    public function uploadOrUpdateProfilePic(UploadedFile $image,int $userId);

    public function findByPhoneNumber(string $phoneNumber);

    public function deleteUserRelatedData(User $user);

}
