<?php

namespace App\Repositories;

use App\Models\OfferCode;
use App\Models\User;
use App\Repositories\Contracts\OfferCodeRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OfferCodeRepositoryImpl extends BaseRepositoryImpl implements OfferCodeRepository
{
    public function __construct(OfferCode $model)
    {
        parent::__construct($model);
    }

    /**
     * Find offer code by code string
     */
    public function findByCode(string $code): ?OfferCode
    {
        return $this->model->where('code', $code)->first();
    }

    /**
     * Check if user has used specific offer code
     */
    public function hasUserUsedCode(int $userId, int $offerCodeId): bool
    {
        return DB::table('offer_code_usages')
            ->where('user_id', $userId)
            ->where('offer_code_id', $offerCodeId)
            ->exists();
    }

    /**
     * Apply offer code to user
     */
    public function applyToUser(User $user, OfferCode $offerCode): array
    {
        return DB::transaction(function () use ($user, $offerCode) {
            // Record usage
            DB::table('offer_code_usages')->insert([
                'user_id' => $user->id,
                'offer_code_id' => $offerCode->id,
                'applied_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Increment usage count
            $offerCode->increment('used_count');

            // Apply benefits based on offer type
            $benefit = [];
            
            switch ($offerCode->type) {
                case OfferCode::TYPE_MEMBERSHIP_EXTENSION:
                    $benefit = $this->applyMembershipExtension($user, $offerCode);
                    break;
                    
                case OfferCode::TYPE_DISCOUNT:
                    $benefit = $this->applyDiscount($user, $offerCode);
                    break;
                    
                case OfferCode::TYPE_FREE_TRIAL:
                    $benefit = $this->applyFreeTrial($user, $offerCode);
                    break;
                    
                default:
                    $benefit = ['type' => 'unknown', 'message' => 'Offer code applied'];
            }

            return $benefit;
        });
    }

    /**
     * Get user's applied offer codes
     */
    public function getUserAppliedCodes(int $userId): Collection
    {
        return $this->model->whereHas('usages', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->with(['usages' => function ($query) use ($userId) {
            $query->where('user_id', $userId);
        }])->get();
    }

    /**
     * Get active offer codes
     */
    public function getActiveCodes(): Collection
    {
        return $this->model->where('status', OfferCode::STATUS_ACTIVE)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->get();
    }

    /**
     * Get expired offer codes
     */
    public function getExpiredCodes(): Collection
    {
        return $this->model->where('expires_at', '<=', now())->get();
    }

    /**
     * Get offer codes by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->model->where('status', $status)->get();
    }

    /**
     * Increment usage count
     */
    public function incrementUsageCount(OfferCode $offerCode): OfferCode
    {
        $offerCode->increment('used_count');
        return $offerCode->fresh();
    }

    /**
     * Get offer code statistics
     */
    public function getStatistics(OfferCode $offerCode): array
    {
        $usages = DB::table('offer_code_usages')
            ->where('offer_code_id', $offerCode->id)
            ->get();

        return [
            'total_uses' => $usages->count(),
            'unique_users' => $usages->unique('user_id')->count(),
            'recent_uses' => $usages->where('applied_at', '>=', now()->subDays(7))->count(),
            'usage_rate' => $offerCode->max_uses ? 
                ($usages->count() / $offerCode->max_uses) * 100 : 0,
            'days_until_expiry' => $offerCode->expires_at ? 
                now()->diffInDays($offerCode->expires_at, false) : null,
        ];
    }

    /**
     * Search offer codes
     */
    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where(function ($q) use ($query) {
            $q->where('code', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%");
        })->paginate($perPage);
    }

    /**
     * Apply membership extension
     */
    private function applyMembershipExtension(User $user, OfferCode $offerCode): array
    {
        if (!$user->membership) {
            return [
                'type' => 'membership_extension',
                'message' => 'User does not have an active membership to extend'
            ];
        }

        $currentEndDate = $user->membership->end_at ?? now();
        $extensionDays = $this->calculateExtensionDays($offerCode);
        
        $newEndDate = Carbon::parse($currentEndDate)->addDays($extensionDays);
        
        $user->membership->update(['end_at' => $newEndDate]);

        return [
            'type' => 'membership_extension',
            'message' => "Membership extended by {$extensionDays} days",
            'new_end_date' => $newEndDate->toDateString(),
            'days_added' => $extensionDays
        ];
    }

    /**
     * Apply discount
     */
    private function applyDiscount(User $user, OfferCode $offerCode): array
    {
        // Store discount for next purchase
        // This would typically be handled in your payment/subscription system
        
        return [
            'type' => 'discount',
            'message' => "Discount of {$offerCode->discount_percentage}% applied for next purchase",
            'discount_percentage' => $offerCode->discount_percentage,
            'discount_amount' => $offerCode->discount_amount
        ];
    }

    /**
     * Apply free trial
     */
    private function applyFreeTrial(User $user, OfferCode $offerCode): array
    {
        $trialDays = $this->calculateExtensionDays($offerCode);
        $trialEndDate = now()->addDays($trialDays);

        // Create or extend trial membership
        // Implementation depends on your membership system
        
        return [
            'type' => 'free_trial',
            'message' => "Free trial activated for {$trialDays} days",
            'trial_end_date' => $trialEndDate->toDateString(),
            'trial_days' => $trialDays
        ];
    }

    /**
     * Calculate extension days from offer code
     */
    private function calculateExtensionDays(OfferCode $offerCode): int
    {
        if (!$offerCode->duration_num || !$offerCode->duration_units) {
            return 0;
        }

        switch ($offerCode->duration_units) {
            case OfferCode::DURATION_UNIT_DAY:
                return $offerCode->duration_num;
            case OfferCode::DURATION_UNIT_WEEK:
                return $offerCode->duration_num * 7;
            case OfferCode::DURATION_UNIT_MONTH:
                return $offerCode->duration_num * 30;
            case OfferCode::DURATION_UNIT_YEAR:
                return $offerCode->duration_num * 365;
            default:
                return 0;
        }
    }
}
