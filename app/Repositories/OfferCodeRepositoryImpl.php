<?php

namespace App\Repositories;

use App\Models\OfferCode;
use App\Models\User;
use App\Repositories\Contracts\OfferCodeRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class EloquentOfferCodeRepository extends EloquentBaseRepository implements OfferCodeRepository
{
    public function findByCode(string $code): ?OfferCode
    {
        return $this->findOneBy(['code' => $code]);
    }

    public function hasUserUsedCode(int $userId, int $offerCodeId): bool
    {
        return DB::table('offer_code_usages')
            ->where('user_id', $userId)
            ->where('offer_code_id', $offerCodeId)
            ->exists();
    }

    public function applyToUser(User $user, OfferCode $offerCode): array
    {
        return DB::transaction(function () use ($user, $offerCode) {
            // Record usage
            DB::table('offer_code_usages')->insert([
                'user_id' => $user->id,
                'offer_code_id' => $offerCode->id,
                'applied_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Increment usage count
            $offerCode->increment('used_count');

            return [
                'type' => $offerCode->type,
                'message' => 'Offer code applied successfully',
                'code' => $offerCode->code
            ];
        });
    }

    public function getUserAppliedCodes(int $userId): Collection
    {
        return $this->model->whereHas('usages', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->get();
    }

    public function getActiveCodes(): Collection
    {
        return $this->findBy(['status' => 'active']);
    }

    public function getExpiredCodes(): Collection
    {
        return $this->model->where('expires_at', '<=', now())->get();
    }

    public function getByStatus(string $status): Collection
    {
        return $this->findBy(['status' => $status]);
    }

    public function incrementUsageCount(OfferCode $offerCode): OfferCode
    {
        $offerCode->increment('used_count');
        return $offerCode->fresh();
    }

    public function getStatistics(OfferCode $offerCode): array
    {
        $usages = DB::table('offer_code_usages')
            ->where('offer_code_id', $offerCode->id)
            ->get();

        return [
            'total_uses' => $usages->count(),
            'unique_users' => $usages->unique('user_id')->count(),
        ];
    }

    public function search(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->where(function ($q) use ($query) {
            $q->where('code', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%");
        })->paginate($perPage);
    }
}
