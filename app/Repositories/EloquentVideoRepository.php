<?php

namespace App\Repositories;

use App\Repositories\Contracts\UserRepository;
use Illuminate\Support\Facades\Auth;

class EloquentVideoRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\VideoRepository
{
    public function getHomeFeedVideos($userId = null)
    {
        $userRepository = app(UserRepository::class);
        $authUser = $userId ? $userRepository->findOne($userId) : Auth::user();

        $relationships = $authUser->fromUserRelationships->merge($authUser->toUserRelationships);

        $filteredRelationships = $relationships->filter(function ($relationship) {
            return $relationship->connectionRequest &&
            $relationship->connectionRequest->status &&
            !in_array($relationship->connectionRequest->status->title, ['pending', 'discarded', 'invited', 'resent']);
        });

        $userIds = $filteredRelationships->map(function ($relationship) use ($authUser) {
            return ($relationship->from_user_id === $authUser->id) ? $relationship->to_user_id : $relationship->from_user_id;
        });

        $userIds[] = $authUser->id;

        return $this->findBy([
            'owner_user_id' => $userIds->toArray(),
            'withoutPagination' => true,
            'order_by' => 'updated_at',
            // 'order_direction' => 'desc'
        ]);
    }
}
