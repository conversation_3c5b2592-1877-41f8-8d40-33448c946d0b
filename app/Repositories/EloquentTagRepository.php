<?php

namespace App\Repositories;

use App\Models\Tag;
use Illuminate\Support\Facades\Auth;

class EloquentTagRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\TagRepository

{
    public function findBy(array $searchCriteria = [], $withTrashed = false)
    {
        $queryBuilder = $this->model->newQuery();

        // Check if 'user_id' is present in search criteria
        $hasUserId = !empty($searchCriteria['user_id']);
        if ($hasUserId) {
            $authUserId = Auth::user()->id;
            $userId = $searchCriteria['user_id'];

            // Filter based on related questions and include QuestionSend details if applicable
            $queryBuilder->whereHas('questions', function ($query) use ($authUserId, $userId) {
                $query->with(['questionSends' => function ($query) use ($authUserId, $userId) {
                    $query->where('sent_from_user_id', $authUserId)
                        ->where('sent_to_user_id', $userId);
                }]);
            });

            unset($searchCriteria['user_id']);

        }

        // Apply base repository filters without 'user_id'
        $this->applySearchCriteriaInQueryBuilder($queryBuilder, $searchCriteria);

        if ($withTrashed) {
            $queryBuilder->withTrashed();
        }

        return $queryBuilder->get();
    }
    public function getTagsWithQuestionCount($selectedUserId = null)
    {
        $authUserId = Auth::user()->id;

        return Tag::withCount([
            'questions as custom_question_count' => function ($query) use ($authUserId) {
                $query->where('question_type', 'custom')
                    ->where('creator__user_id', $authUserId);
            },
            'questions as total_question_count' => function ($query) use ($authUserId) {
                $query->where(function ($query) use ($authUserId) {
                    $query->where('question_type', 'public')
                        ->orWhere(function ($query) use ($authUserId) {
                            $query->where('question_type', 'private')
                                ->where('to_user_id', $authUserId);
                        });
                });
            },

            // Conditionally calculate sent question count only if selectedUserId is provided
            'questions as sent_question_count' => function ($query) use ($selectedUserId, $authUserId) {
                if ($selectedUserId) {
                    $query->whereHas('questionSends', function ($sendQuery) use ($selectedUserId, $authUserId) {
                        $sendQuery->where('sent_from_user_id', $authUserId)
                            ->where('sent_to_user_id', $selectedUserId);
                    });
                }
            },
        ])->get();
    }

    public function getSingleTagWithDetails($tagId, $selectedUserId = null)
    {
        $authUserId = Auth::user()->id;

        // Fetch tag with related questions, question activities, and optional QuestionSend details
        $tag = Tag::with(['questions' => function ($query) use ($authUserId, $selectedUserId) {
            if ($selectedUserId) {
                // If user_id is provided, include QuestionSend details
                $query->with(['questionSends' => function ($query) use ($authUserId, $selectedUserId) {
                    $query->where('sent_from_user_id', $authUserId)
                        ->where('sent_to_user_id', $selectedUserId);
                }]);
            }
            // Always include question activities
            $query->with('questionActivities');
        }])->findOrFail($tagId);

        return $tag;
    }

}
