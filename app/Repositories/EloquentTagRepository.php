<?php

namespace Tests\Traits;

use App\Models\User;

trait CognitoTestHelpers
{
    /**
     * Register, verify, and login a user using phone number and password.
     * Returns the login response array (tokens, user, etc).
     */
    public function registerVerifyAndLogin($phoneNumber, $password = 'Test@123456')
    {
        // Register
        $this->postJson('/api/register', [
            'phone_number' => $phoneNumber,
        ])->assertStatus(201);

        // Verify (code is always 123456 in non-production)
        $this->postJson('/api/verify', [
            'phone_number' => $phoneNumber,
            'code' => '123456',
        ])->assertStatus(200);

        // Set password (if your flow requires it, otherwise skip)
        // $this->postJson('/api/set-password', [
        //     'phone_number' => $phoneNumber,
        //     'password' => $password,
        // ])->assertStatus(200);

        // Login
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => $phoneNumber,
            'password' => $password,
        ])->assertStatus(200);

        return $loginResponse->json();
    }
}
