<?php

namespace App\Repositories;

use App\Models\Question;
use App\Repositories\Contracts\ConnectionRequestRepository;
use App\Repositories\Contracts\FollowupQuestionSendRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EloquentQuestionRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\QuestionRepository

{

    /**
     * Find questions based on the provided search criteria, with optional trashed records.
     *
     * @param array $searchCriteria Search filters such as 'user_id', 'order_by', 'per_page', etc.
     * @param bool $withTrashed     Include trashed questions if true.
     *
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection
     *         Paginated results if 'withoutPagination' is not set, otherwise all results.
     */
    public function findBy(array $searchCriteria = [], $withTrashed = false)
    {
        $queryBuilder = $this->model->newQuery();

        $authUser = Auth::user();
        $authUserId = $authUser->id;
        $isAdmin = $authUser && $authUser->isAdmin();

        // Determine the target user ID (use either searchCriteria['user_id'] or the authenticated user ID)
        $targetUserId = $isAdmin && !empty($searchCriteria['user_id'])
        ? (int) $searchCriteria['user_id']
        : $authUserId;

        unset($searchCriteria['user_id']);

        $this->applySearchCriteriaInQueryBuilder($queryBuilder, $searchCriteria);

        if ($withTrashed) {
            $queryBuilder->withTrashed();
        }

        // Exclude questions with the 'Followup' tag
        $queryBuilder->whereDoesntHave('tags', function ($query) {
            $query->where('title', 'Followup');
        });

        $orderDirection = !empty($searchCriteria['order_direction']) ? $searchCriteria['order_direction'] : 'desc';

        if (!empty($searchCriteria['creator__user_id'])) {
            $creatorUserId = $searchCriteria['creator__user_id'];
            $queryBuilder->where('creator__user_id', $creatorUserId);
        } else {

            $queryBuilder->where(function ($query) use ($targetUserId) {
                // Public questions or private ones where the authenticated user is the to_user_id
                $query->where(function ($query) use ($targetUserId) {
                    $query->where('question_type', Question::PUBLIC_QUESTION_TYPE)
                        ->orWhere(function ($query) use ($targetUserId) {
                            $query->where('question_type', Question::PRIVATE_QUESTION_TYPE)
                                ->where('to_user_id', $targetUserId);
                        });
                })
                // Also include questions where the authenticated user is the creator
                    ->orWhere('creator__user_id', $targetUserId);
            });

            // Order by creator_user_id condition and then by id
            $queryBuilder->orderByRaw("CASE WHEN creator__user_id = ? THEN 0 ELSE 1 END, id {$orderDirection}", [$targetUserId]);
        }

        $queryBuilder->with(['questionActivities' => function ($query) use ($targetUserId) {
            $this->filterQuestionActivities($query, $targetUserId);
        }]);

        $orderBy = !empty($searchCriteria['order_by']) ? $searchCriteria['order_by'] : 'id';

        if (isset($searchCriteria['rawOrder'])) {
            $queryBuilder->orderByRaw(DB::raw("FIELD(id, {$searchCriteria['id']})"));
        } else {
            $queryBuilder->orderBy($orderBy, $orderDirection);
        }

        $limit = !empty($searchCriteria['per_page']) ? (int) $searchCriteria['per_page'] : 300;

        if (empty($searchCriteria['withoutPagination'])) {
            return $queryBuilder->paginate($limit);
        } else {
            return $queryBuilder->get();
        }

    }

    /**
     * Filter question activities based on connections of the authenticated or target user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query Query builder instance.
     * @param int|null $targetUserId Optionally filter activities based on a specific user's connections.
     *
     * @return void
     */
    protected function filterQuestionActivities($query, $targetUserId = null)
    {
        $connectionRequestRepository = app(ConnectionRequestRepository::class);

        $userIds = $connectionRequestRepository->getConnectedUserProfiles($targetUserId);
        $query->whereHas('questionSends', function ($query) use ($userIds) {
            $query->whereIn('sent_from_user_id', $userIds)
                ->orWhereIn('sent_to_user_id', $userIds);
        });
    }

    public function getFollowupQuestions()
    {

        $authUser = Auth::user();
        $questionSendRepository = app(QuestionSendRepository::class);
        $questionSendIds = $questionSendRepository->findBy(['sent_from_user_id' => $authUser->id])->pluck('id')
            ->merge($questionSendRepository->findBy(['sent_to_user_id' => $authUser->id])->pluck('id'));

        $followupQuestionSendRepository = app(FollowupQuestionSendRepository::class);
        $followupQuestionIds = $followupQuestionSendRepository->findBy(['question_send_id' => $questionSendIds])->pluck('question_id');
        return $this->findBy(['id' => $followupQuestionIds]);

    }

}
