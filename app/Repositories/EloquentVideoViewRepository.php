<?php

namespace App\Repositories;

use App\Events\VideoLiked;
use App\Events\VideoViewed;
use Illuminate\Support\Facades\Auth;

class EloquentVideoViewRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\VideoViewRepository

{

    public function likeVideo(array $data)
    {
        $authUser = Auth::user();

        $videoView = $this->findOneBy(['viewer_user_id' => $authUser->id, 'video_id' => $data['video_id'], 'liked_at' => $data['liked_at'] ?? null]);
        $now = now();

        if ($data["liked_at"] !== null) {
            // Unlike the video if already liked
            if ($videoView && $videoView->liked_at !== null) {
                $this->update($videoView, ['liked_at' => null]);
            }
        } else {
            // Like the video
            if (!$videoView) {
                $videoView = $this->save([
                    'viewer_user_id' => $authUser->id,
                    'video_id' => $data['video_id'],
                    'viewed_at' => $now,
                    'liked_at' => $now,
                ]);
                // Dispatch both VideoViewed and VideoLiked events
                event(new VideoViewed($videoView));
                event(new VideoLiked($videoView));
            } else {
                if ($videoView->liked_at === null) {
                    $this->update($videoView, ['liked_at' => $now]);

                    event(new VideoLiked($videoView));
                }
            }
        }

        return $videoView;
    }

}
