<?php

namespace App\Repositories;

use App\Repositories\Contracts\QuestionActivityRepository;
use App\Repositories\Contracts\QuestionStatusRepository;
use App\Repositories\Contracts\RelationshipRepository;
use Illuminate\Support\Facades\Auth;
use App\Events\QuestionSent;

class EloquentQuestionSendRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\QuestionSendRepository

{
    /**
     * Create a new QuestionSend and associated QuestionActivities.
     *
     * @param array $data
     * @return mixed
     */

    public function create(array $data)
    {
        $authUser = Auth::user();

        $video = $data['video_id'] ?? null;

        $relationshipRepository = app(RelationshipRepository::class);
        $relationship = null;
        $sentToUserId = $data['sent_to_user_id'] ?? null;
        $invitationId = $data['invitation_id'] ?? null;

         // Check for existing question send
         if (!$video && isset($data['sent_to_user_id'])) {
            $existingQuestionSend = $this->findOneBy([
                'question_id' => $data['question_id'],
                'sent_from_user_id' => $authUser->id,
                'sent_to_user_id' => $data['sent_to_user_id'],
                'video_id' => null
            ]);
            if ($existingQuestionSend) {
                // If trashed_at is null, it's an active question send
                if (is_null($existingQuestionSend->trashed_at)) {
                    throw new \InvalidArgumentException('Question already sent to this user');
                }

                // If trashed_at has a date, delete it to allow resending
                $this->delete($existingQuestionSend);
            }
        }

        // If no video and 'sent_to_user_id' is provided, find the relationship
        if (!$video && $sentToUserId !== null) {
            $relationship = $relationshipRepository->findRelationship($authUser->id, $sentToUserId);
        }

        if (!$video && $invitationId !== null && $sentToUserId === null) {
            $relationship = $relationshipRepository->findOneBy(['to_invitation_id' => $invitationId]);
        }
        $questionSend = $this->save([
            'question_id' => $data['question_id'],
            'relationship_id' => $relationship['id'] ?? null,
            'sent_from_user_id' => $video ? null : $authUser->id,
            'sent_to_user_id' => $data['sent_to_user_id'] ?? null,
            'sent_at' => $data['sent_at'],
            'video_id' => $data['video_id'] ?? null,
            'invitation_id' => $data['invitation_id'] ?? null,
        ]);
        if (!$video) {
            $this->createOrUpdateQuestionActivity($questionSend, 'sent');
            $this->createOrUpdateQuestionActivity($questionSend, 'received');
        } else {
            $this->createOrUpdateQuestionActivity($questionSend, 'selfAnswered');
        }
        return $questionSend;
    }

    /**
     * Update a QuestionSend and associated QuestionActivity based on the provided data.
     *
     * @param mixed $questionSend
     * @param array $data
     * @return mixed
     */

    public function updateQuestionSendAndActivity($questionSend, array $data)
    {
        // Update video_id if provided
        if (isset($data['video_id'])) {
            $questionSend = $this->update($questionSend, [
                'video_id' => $data['video_id'],
            ]);
        }

        // Handle resending question
        if ($data['question_status'] === 'resent') {
            // Update sent_at with correct MySQL datetime format
            $currentTime = now()->format('Y-m-d H:i:s');
            $questionSend = $this->update($questionSend, [
                'sent_at' => $currentTime,
            ]);

            // Trigger QuestionSent event
            event(new QuestionSent($questionSend));
        }

        // Update question activity for all statuses
        $this->createOrUpdateQuestionActivity($questionSend, $data['question_status']);

        return $questionSend;
    }

    /**
     * Create or update a QuestionActivity based on the provided status.
     *
     * @param mixed $questionSend
     * @param string $statusTitle
     * @return void
     */

    protected function createOrUpdateQuestionActivity($questionSend, $statusTitle)
    {
        $questionStatusRepository = app(QuestionStatusRepository::class);
        $questionActivityRepository = app(QuestionActivityRepository::class);

        if ($statusTitle === 'answered') {
            // Update the 'received' activity to 'answered'
            $questionStatus = $questionStatusRepository->findOneBy(['title' => $statusTitle]);
            $preQuestionStatus = $questionStatusRepository->findOneBy(['title' => 'received']);

            if ($questionStatus && $preQuestionStatus) {
                $questionActivity = $questionActivityRepository->findOneBy([
                    'question_send_id' => $questionSend->id,
                    'question_status_id' => $preQuestionStatus->id,
                ]);

                if ($questionActivity) {
                    $questionActivityRepository->update($questionActivity, [
                        'question_status_id' => $questionStatus['id'],
                    ]);
                    return;
                }
            }
        } else {
            $statusTitle = $statusTitle === 'selfAnswered' ? 'answered' : $statusTitle;
            $questionStatus = $questionStatusRepository->findOneBy(['title' => $statusTitle]);

            if ($questionStatus) {
                $questionActivityRepository->save([
                    'question_id' => $questionSend['question_id'],
                    'question_send_id' => $questionSend['id'],
                    'question_status_id' => $questionStatus['id'],
                ]);
            }
        }
    }

    public function findByConnectedPeopleQuestionActivity(array $searchCriteria = [])
    {
        $questionSends = $this->findBy($searchCriteria);

        $questionActivityRepository = app(QuestionActivityRepository::class);

        $result = [];

        foreach ($questionSends as $questionSend) {

            $questionActivities = $questionActivityRepository->connected_people_question_activity($questionSend->question_id);
            if ($questionActivities->isNotEmpty()) {
                $result[] = [
                    'question_send' => $questionSend,
                    'question_activities' => $questionActivities->filter()->values()->all(),
                ];
            }
        }

        return collect($result);
    }
}
