<?php

namespace App\Repositories;

use App\Repositories\Contracts\ConnectionRequestRepository;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use App\Repositories\Contracts\RelationshipRepository;
use Illuminate\Support\Facades\Auth;

class EloquentInvitationRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\InvitationRepository

{
    public function createInvitationAndRelationship(array $data)
    {
        $authUser = Auth::user();

        // Separate the Invitation and Relationship data
        $invitationData = [
            'from_user_id' => $authUser->id,
            'to_phone_number' => $data['to_phone_number'],
            'to_user_id' => $data['to_user_id'] ?? null,
        ];

        // Create the Invitation
        $invitation = $this->save($invitationData);

        $relationshipData = [
            'name' => $data['name'],
            'from_user_id' => $authUser->id,
            'to_user_id' => $data['to_user_id'] ?? null,
            'to_invitation_id' => $invitation->id,
            'name_as_from' => $data['name_as_from'],
            'name_as_outsider' => $data['name_as_outsider'],
        ];

        $relationshipRepository = app(RelationshipRepository::class);
        $relationship = $relationshipRepository->save($relationshipData);

        $connectionRequestStatusRepository = app(ConnectionRequestStatusRepository::class);
        $connectionRequestStatus = $connectionRequestStatusRepository->findOneBy(['title' => 'invited']);

        $connectionRequestRepository = app(ConnectionRequestRepository::class);
        $connectionRequestRepository->save([
            'relationship_id' => $relationship->id,
            'status_id' => $connectionRequestStatus->id,
        ]);

        return $invitation;
    }

    public function deleteInvitation(array $data)
    {
        $authUser = Auth::user();
        $invitation = $this->findOneBy(['from_user_id' => $authUser->id, 'to_phone_number' => $data['to_phone_number']]);

        if ($invitation) {
            $relationshipRepository = app(RelationshipRepository::class);
            $relationship = $relationshipRepository->findOneBy(['to_invitation_id' => $invitation->id]);

            if ($relationship) {
                $relationshipRepository->delete($relationship);
            }

            $this->delete($invitation);
        }
    }

}
