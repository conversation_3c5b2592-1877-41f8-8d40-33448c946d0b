<?php

namespace App\Repositories;

class EloquentRelationshipRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\RelationshipRepository

{
    /**
     * Find a relationship between two users.
     *
     * @param int $fromUserId
     * @param int $toUserId
     * @return Relationship|null
     */
    public function findRelationship(int $fromUserId, int $toUserId)
    {
        $relationship = $this->findOneBy([
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
        ]);

        if (!$relationship) {
            $relationship = $this->findOneBy([
                'from_user_id' => $toUserId,
                'to_user_id' => $fromUserId,
            ]);
        }

        return $relationship;
    }
}
