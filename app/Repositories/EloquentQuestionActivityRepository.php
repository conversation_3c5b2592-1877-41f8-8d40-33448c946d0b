<?php

namespace App\Repositories;

use App\Models\QuestionSend;
use Illuminate\Support\Facades\Auth;

class EloquentQuestionActivityRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\QuestionActivityRepository

{
    public function connected_people_question_activity(int $questionId)
    {
        $authUser = Auth::user();

        $relationships = $authUser->fromUserRelationships->merge($authUser->toUserRelationships);

        $filteredRelationships = $relationships->filter(function ($relationship) {
            return $relationship->connectionRequest &&
            $relationship->connectionRequest->status &&
            !in_array($relationship->connectionRequest->status->title, ['discarded', 'invited', 'pending']);
        });

        $userIds = $filteredRelationships->map(function ($relationship) use ($authUser) {
            return ($relationship->from_user_id === $authUser->id) ? $relationship->to_user_id : $relationship->from_user_id;
        })->toArray();

        $userIds[] = $authUser->id;

        $questionSends = QuestionSend::where('question_id', $questionId)
            ->where(function ($query) use ($userIds) {
                $query->whereIn('sent_from_user_id', $userIds)
                    ->orWhereIn('sent_to_user_id', $userIds);
            })
            ->pluck('id')
            ->toArray();

        $questionActivities = $this->findBy(['question_send_id' => $questionSends]);

        return $questionActivities;
    }
}
