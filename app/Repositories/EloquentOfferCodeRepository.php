<?php

namespace App\Repositories;

use App\Models\MembershipType;
use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\MembershipTypeRepository;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\UserRepository;

class EloquentOfferCodeRepository extends EloquentBaseRepository implements OfferCodeRepository
{


    public function applyOfferCodeToUser($user, $offerCode)
    {
        $membershipRepository = app(MembershipRepository::class);
        $membershipTypeRepository = app(MembershipTypeRepository::class);

        $startDate = now();
        $endDate = $this->calculateEndDate($startDate, $offerCode->duration_num, $offerCode->duration_units);

        // Determine membership type based on offer code
        $membershipTypeName = $this->getMembershipTypeByOfferCode($offerCode->code);
        $membershipType = $membershipTypeRepository->findOneBy(['name' => $membershipTypeName]);

        $membershipData = [
            'started_at' => $startDate->toIso8601String(),
            'end_at' => $endDate->toIso8601String(),
            'is_autopay_enabled' => $offerCode->is_auto_renew,
            'membership_type_id' => $membershipType ? $membershipType->id : 1, // Default to 1 if not found
        ];

        if ($user->membership) {
            // Update existing membership
            $membershipRepository->update($user->membership, $membershipData);
        } else {
            // Create new membership
            $membership = $membershipRepository->save($membershipData);

            $userRepository = app(UserRepository::class);

            $userRepository->update($user, [
                'membership_id' => $membership->id
            ]);
        }

        return $membershipData;
    }

    private function getMembershipTypeByOfferCode($offerCode)
    {
        // Much cleaner mapping:
        switch ($offerCode) {
            case 'OURLEGACY':
                return MembershipType::OURLEGACY_MEMBERSHIP;
            case 'SUMMER25':
                return MembershipType::SUMMER25_MEMBERSHIP;
            case '1WEEK25':
                return MembershipType::ONEWEEK25_MEMBERSHIP;
            default:
                return MembershipType::TRIAL_MEMBERSHIP;
        }
    }

    public function calculateEndDate($startDate, $durationNum, $durationUnits)
    {
        switch ($durationUnits) {
            case 'day':
                return $startDate->copy()->addDays($durationNum);
            case 'week':
                return $startDate->copy()->addWeeks($durationNum);
            case 'month':
                return $startDate->copy()->addMonths($durationNum);
            case 'year':
                return $startDate->copy()->addYears($durationNum);
            default:
                return $startDate->copy()->addMonths($durationNum);
        }
    }
}
