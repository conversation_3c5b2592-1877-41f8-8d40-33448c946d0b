<?php

namespace App\Repositories;

use App\Repositories\Contracts\MembershipRepository;
use App\Repositories\Contracts\OfferCodeRepository;
use App\Repositories\Contracts\UserRepository;

class EloquentOfferCodeRepository extends EloquentBaseRepository implements OfferCodeRepository
{


    public function applyOfferCodeToUser($user, $offerCode)
    {
        $membershipRepository = app(MembershipRepository::class);
        $startDate = now();
        $endDate = $this->calculateEndDate($startDate, $offerCode->duration_num, $offerCode->duration_units);

        $membershipData = [
            'started_at' => $startDate->toIso8601String(),
            'end_at' => $endDate->toIso8601String(),
            'is_autopay_enabled' => $offerCode->is_auto_renew,
        ];

        if ($user->membership) {
            // Update existing membership
            $membershipRepository->update($user->membership, $membershipData);
        } else {
            // Create new membership - need to add membership_type_id
            $membershipData['membership_type_id'] = 1; // Default or get from offer code
            $membership = $membershipRepository->save($membershipData);

            $userRepository = app(UserRepository::class);

            $userRepository->update($user, [
                'membership_id' => $membership->id
            ]);
        }

        return $membershipData;
    }

    public function calculateEndDate($startDate, $durationNum, $durationUnits)
    {
        switch ($durationUnits) {
            case 'day':
                return $startDate->copy()->addDays($durationNum);
            case 'week':
                return $startDate->copy()->addWeeks($durationNum);
            case 'month':
                return $startDate->copy()->addMonths($durationNum);
            case 'year':
                return $startDate->copy()->addYears($durationNum);
            default:
                return $startDate->copy()->addMonths($durationNum);
        }
    }
}
