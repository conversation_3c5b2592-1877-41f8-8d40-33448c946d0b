<?php
namespace App\Repositories;

use App\Events\QuestionUnlocked;
use App\Repositories\Contracts\QuestionActivityRepository;
use App\Repositories\Contracts\QuestionRepository;
use App\Repositories\Contracts\QuestionStatusRepository;
use App\Repositories\Contracts\TagQuestionRepository;
use App\Repositories\Contracts\VideoFollowupQuestionRepository;

class EloquentFollowupQuestionSendRepository extends EloquentBaseRepository implements \App\Repositories\Contracts\FollowupQuestionSendRepository
{
    public function create(array $data)
    {
        $questionRepository              = app(QuestionRepository::class);
        $tagQuestionRepository           = app(TagQuestionRepository::class);
        $videoFollowupQuestionRepository = app(VideoFollowupQuestionRepository::class);

        if (isset($data['question_id'])) {
            $videoFollowupQuestionRepository = $videoFollowupQuestionRepository->findOneBy(['followup_question_id' => $data['question_id']]);
            $videoFollowupQuestionRepository->delete($videoFollowupQuestionRepository);
            $question = $questionRepository->findOne($data['question_id']);

        } else {
            $question = $questionRepository->save([
                'question_text' => $data['question_text'], 'to_user_id' => $data['to_user_id'], 'question_type' => $data['question_type'],
            ]);
            $tagQuestionRepository->save(['question_id' => $question->id, 'tag_id' => $data['tag_id']]);

        }

        event(new QuestionUnlocked($question, $data['to_user_id'], $data['question_send_id'], $data['tag_id']));

        $questionStatusRepository   = app(QuestionStatusRepository::class);
        $questionActivityRepository = app(QuestionActivityRepository::class);
        $questionStatus             = $questionStatusRepository->findOneBy(['title' => 'unlocked']);

        if ($questionStatus) {
            $questionActivityRepository->save([
                'question_id'        => $question->id,
                'question_send_id'   => $data['question_send_id'],
                'question_status_id' => $questionStatus['id'],
            ]);
        }
        return $this->save(['question_id' => $question->id, 'question_send_id' => $data['question_send_id']]);

    }
}
