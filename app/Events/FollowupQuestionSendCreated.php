<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FollowupQuestionSendCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $questionSendId;
    public $followupQuestionId;

    public function __construct($questionSendId, $followupQuestionId)
    {
        $this->questionSendId = $questionSendId;
        $this->followupQuestionId = $followupQuestionId;
    }

}
