<?php

namespace App\Events;

use App\Models\Invitation;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvitationCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public $invitation;
    public $relationshipData;

    public function __construct(Invitation $invitation, array $relationshipData)
    {
        $this->invitation = $invitation;
        $this->relationshipData = $relationshipData;

    }

}
