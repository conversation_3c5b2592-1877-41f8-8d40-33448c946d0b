<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuestionAnswered
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $questionSend;

    /**
     * Create a new event instance.
     *
     * @param $videoView
     */
    public function __construct($questionSend)
    {
        $this->questionSend = $questionSend;
    }
}
