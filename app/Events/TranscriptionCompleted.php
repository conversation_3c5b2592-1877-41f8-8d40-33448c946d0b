<?php

namespace App\Events;

use App\Models\Video;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TranscriptionCompleted
{
    use Dispatchable, SerializesModels;

    public $video;
    public $transcription;

    public function __construct(Video $video, string $transcription)
    {
        $this->video = $video;
        $this->transcription = $transcription;
    }
}
