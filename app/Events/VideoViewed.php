<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VideoViewed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $videoView;

    /**
     * Create a new event instance.
     *
     * @param $videoView
     */
    public function __construct($videoView)
    {
        $this->videoView = $videoView;
    }
}
