<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpdateInvitationAndRelationship
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $invitations;
    public $user;

    /**
     * Create a new event instance.
     *
     * @param \Illuminate\Database\Eloquent\Collection $invitations
     * @param User $user
     */
    public function __construct($invitations, User $user)
    {
        $this->invitations = $invitations;
        $this->user = $user;
    }

}
