<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $notificationData;
    public $notificationType;

    /**
     * Create a new event instance.
     */
    public function __construct($notificationData, $notificationType)
    {
        $this->notificationData = $notificationData;
        $this->notificationType = $notificationType;
    }

}
