<?php

namespace App\Events;

use App\Models\Question;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuestionUnlocked
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $question;
    public $toUserId;
    public $questionSendId;
    public $tagId;

    /**
     * Create a new event instance.
     *
     * @param Question $question
     * @param int $toUserId
     */
    public function __construct(Question $question, $toUserId, $questionSendId, $tagId)
    {
        $this->question = $question;
        $this->toUserId = $toUserId;
        $this->questionSendId = $questionSendId;
        $this->tagId = $tagId;
    }
}
