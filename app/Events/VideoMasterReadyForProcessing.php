<?php  

namespace App\Events;

use App\Models\Video;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VideoMasterReadyForProcessing
{
    use Dispatchable, SerializesModels;

    public $video;
    public $masterUrl;

    public function __construct(Video $video, string $masterUrl)
    {
        $this->video = $video;
        $this->masterUrl = $masterUrl;
    }
}