<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationSent
{
    use Dispatchable, SerializesModels;

    public $userId;
    public $notificationText;

    /**
     * Create a new event instance.
     */
    public function __construct(string $userId, string $notificationText)
    {
        $this->userId = $userId;
        $this->notificationText = $notificationText;
    }
} 