<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VideoCommented
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $videoComment;

    /**
     * Create a new event instance.
     *
     * @param $videoView
     */
    public function __construct($videoComment)
    {
        $this->videoComment = $videoComment;
    }
}
