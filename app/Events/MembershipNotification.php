<?php

namespace App\Events;

use App\Models\Membership;
use App\Models\MembershipGift;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MembershipNotification
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private $notificationType;
    private $membership;
    private $giftMembership;
    private $membershipCost;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $notificationType,
        ?Membership $membership = null,
        ?MembershipGift $giftMembership = null,
        ?string $membershipCost = null
    ) {
        $this->notificationType = $notificationType;
        $this->membership = $membership;
        $this->giftMembership = $giftMembership;
        $this->membershipCost = $membershipCost;
    }

    /**
     * Get the notification type.
     */
    public function getNotificationType(): string
    {
        return $this->notificationType;
    }

    /**
     * Get the membership instance.
     */
    public function getMembership(): ?Membership
    {
        return $this->membership;
    }

    /**
     * Get the gift membership instance.
     */
    public function getGiftMembership(): ?MembershipGift
    {
        return $this->giftMembership;
    }

    /**
     * Get the membership cost.
     */
    public function getMembershipCost(): ?string
    {
        return $this->membershipCost;
    }
}
