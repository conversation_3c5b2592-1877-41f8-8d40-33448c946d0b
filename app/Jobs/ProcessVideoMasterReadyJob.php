<?php

namespace App\Jobs;

use App\Services\VideoMasterProcessingService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessVideoMasterReadyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3; // Retry up to 3 times
    public $backoff = [30, 60, 120]; // Exponential backoff in seconds

    protected int $videoId;
    protected string $masterUrl;

    /**
     * Create a new job instance.
     */
    public function __construct(int $videoId, string $masterUrl)
    {
        $this->videoId = $videoId;
        $this->masterUrl = $masterUrl;
    }

    /**
     * Execute the job.
     */
    public function handle(VideoMasterProcessingService $processingService): void
    {
        try {
            Log::info('ProcessVideoMasterReadyJob: Starting processing', [
                'video_id' => $this->videoId,
                'master_url' => $this->masterUrl
            ]);

            $processingService->processVideoMaster($this->videoId, $this->masterUrl);

            Log::info('ProcessVideoMasterReadyJob: Successfully completed', [
                'video_id' => $this->videoId
            ]);

        } catch (Exception $e) {
            Log::error('ProcessVideoMasterReadyJob: Failed to process video', [
                'video_id' => $this->videoId,
                'master_url' => $this->masterUrl,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }



    /**
     * Handle job failure
     */
    public function failed(Exception $exception): void
    {
        Log::error('ProcessVideoMasterReadyJob: Job failed permanently', [
            'video_id' => $this->videoId,
            'master_url' => $this->masterUrl,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Optionally, you could update the video status to indicate failure
        // or send notifications to administrators
    }
}
