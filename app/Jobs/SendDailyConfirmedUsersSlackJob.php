<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class SendDailyConfirmedUsersSlackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
            $this->sendDailyRecapToSlack();
        } catch (\Exception $e) {
            Log::error('Failed to send daily funnel recap to Slack', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send daily recap to #funnel-events Slack channel
     */
    private function sendDailyRecapToSlack()
    {
        // Get yesterday's date range (PST timezone)
        $timezone = new \DateTimeZone('America/Los_Angeles');
        $yesterday = Carbon::now($timezone)->subDay()->startOfDay();
        $today = Carbon::now($timezone)->startOfDay();
        
        // Get registrations
        $registrations = DB::table('users')
            ->whereBetween('created_at', [$yesterday, $today])
            ->whereNotNull('first_name')
            ->select('id', 'first_name', 'last_name', 'phone_number')
            ->get();
        
        // Format message
        $message = "*DAILY RECAP*. Over the last 24 hours…\n\n";
        
        // Add registrations
        $message .= "*" . $registrations->count() . " people completed registration*. ";
        $message .= $this->formatUserList($registrations);
        
        // Send to Slack
        Http::post(config('services.slack.user_confirmed_webhook_url'), [
            'text' => $message
        ]);
        
        Log::info('Sent daily recap to #funnel-events Slack channel');
    }
    
    /**
     * Format a list of users for the Slack message
     */
    private function formatUserList($users)
    {
        if ($users->isEmpty()) {
            return "None.";
        }
        
        return $users->map(function ($user) {
            return "{$user->first_name} {$user->last_name}, {$user->phone_number}";
        })->implode('; ') . '.';
    }
}
