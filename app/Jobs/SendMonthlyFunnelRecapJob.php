<?php

namespace App\Jobs;

use App\Models\MembershipType;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class SendMonthlyFunnelRecapJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
            $this->sendMonthlyRecapToSlack();
        } catch (\Exception $e) {
            Log::error('Failed to send monthly funnel recap', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send monthly recap to #funnel-events Slack channel
     */
    private function sendMonthlyRecapToSlack()
    {
        // Get last month's date range (PST timezone)
        $timezone = new \DateTimeZone('America/Los_Angeles');
        $firstDayOfLastMonth = Carbon::now($timezone)->subMonth()->startOfMonth();
        $lastDayOfLastMonth = Carbon::now($timezone)->subMonth()->endOfMonth()->endOfDay();
        
        $registrations = DB::table('users')
            ->whereBetween('created_at', [$firstDayOfLastMonth, $lastDayOfLastMonth])
            ->select('id', 'first_name', 'last_name', 'phone_number')
            ->get();
        
        $freeTrials = DB::table('users')
            ->join('memberships', 'users.membership_id', '=', 'memberships.id')
            ->join('membership_types', 'memberships.membership_type_id', '=', 'membership_types.id')
            ->where('membership_types.name', MembershipType::TRIAL_MEMBERSHIP)
            ->whereBetween('memberships.started_at', [$firstDayOfLastMonth, $lastDayOfLastMonth])
            ->select('users.id', 'users.first_name', 'users.last_name', 'users.phone_number')
            ->get();
        
        $upgrades = DB::table('users')
            ->join('memberships', 'users.membership_id', '=', 'memberships.id')
            ->join('membership_types', 'memberships.membership_type_id', '=', 'membership_types.id')
            ->where('membership_types.name', MembershipType::FULL_MEMBERSHIP)
            ->whereBetween('memberships.started_at', [$firstDayOfLastMonth, $lastDayOfLastMonth])
            ->select('users.id', 'users.first_name', 'users.last_name', 'users.phone_number')
            ->get();
        
        // Format message
        $message = "*MONTHLY RECAP*. Over the last month…\n\n";
        
        // Add registrations
        $message .= "*" . $registrations->count() . " people completed registration (Stage 2)*. ";
        $message .= $this->formatUserList($registrations);
        $message .= "\n\n";
        
        // Add free trials
        $message .= "*" . $freeTrials->count() . " person started a free trial (Stage 3)*. ";
        $message .= $this->formatUserList($freeTrials);
        $message .= "\n\n";
        
        // Add upgrades
        $message .= "*" . $upgrades->count() . " people upgraded from free trial to paid (Stage 4)*. ";
        $message .= $this->formatUserList($upgrades);
        
        // Send to Slack
        Http::post(config('services.slack.funnel_events_webhook_url'), [
            'text' => $message
        ]);
        
        Log::info('Sent monthly recap to #funnel-events Slack channel');
    }
    
    /**
     * Format a list of users for the Slack message
     */
    private function formatUserList($users)
    {
        if ($users->isEmpty()) {
            return "None.";
        }
        
        return $users->map(function ($user) {
            return "{$user->first_name} {$user->last_name}, {$user->phone_number}";
        })->implode('; ') . '.';
    }
}