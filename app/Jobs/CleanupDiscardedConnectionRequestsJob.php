<?php
namespace App\Jobs;

use App\Models\ConnectionRequestStatus;
use App\Repositories\Contracts\ConnectionRequestRepository;
use App\Repositories\Contracts\ConnectionRequestStatusRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupDiscardedConnectionRequestsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(
        ConnectionRequestRepository $connectionRequestRepository,
        ConnectionRequestStatusRepository $connectionRequestStatusRepository
    ) {
        try {
            // Determine cleanup time based on environment
            $cleanupTime = env('APP_ENV') === 'production'
            ? Carbon::now()->subDays(7)
            : Carbon::now()->subMinutes(5);

            // Get discarded status ID
            $discardedConnectionRequestStatus = $connectionRequestStatusRepository->findOneBy(['title' => ConnectionRequestStatus::STATUS_DISCARDED]);

            if (! $discardedConnectionRequestStatus) {
                Log::info('No discarded status found');
                return;
            }

            // Find old discarded connection requests
            $oldDiscardedRequests = DB::table('connection_requests')
                ->where('status_id', $discardedConnectionRequestStatus->id)
                ->where('updated_at', '<=', $cleanupTime)
                ->get();

            foreach ($oldDiscardedRequests as $request) {
                $connectionRequestRepository->deleteConnectionRequestAndRelatedData($request->id);
                Log::info('Deleted old discarded connection request', ['id' => $request->id]);
            }

            Log::info("Cleanup job completed. Processed discarded connection requests.", [
                'cleanup_time' => $cleanupTime,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in cleanup job: ' . $e->getMessage());
            throw $e;
        }
    }
}
