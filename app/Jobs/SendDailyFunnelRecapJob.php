<?php

namespace App\Jobs;

use App\Models\MembershipType;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class SendDailyFunnelRecapJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
            $this->sendDailyRecapToSlack();
        } catch (\Exception $e) {
            Log::error('Failed to send daily funnel recap', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send daily recap to #funnel-events Slack channel
     */
    private function sendDailyRecapToSlack()
    {
        // Get yesterday's date range (PST timezone)
        $timezone = new \DateTimeZone('America/Los_Angeles');
        $yesterday = Carbon::now($timezone)->subDay()->startOfDay();
        $today = Carbon::now($timezone)->startOfDay();
        
        // Get registrations (Stage 2)
        $registrations = DB::table('users')
            ->whereBetween('created_at', [$yesterday, $today])
            ->select('id', 'first_name', 'last_name', 'phone_number')
            ->get();
        
        $freeTrials = DB::table('users')
            ->join('memberships', 'users.membership_id', '=', 'memberships.id')
            ->join('membership_types', 'memberships.membership_type_id', '=', 'membership_types.id')
            ->where('membership_types.name', MembershipType::TRIAL_MEMBERSHIP)
            ->whereBetween('memberships.started_at', [$yesterday, $today])
            ->select('users.id', 'users.first_name', 'users.last_name', 'users.phone_number')
            ->get();
        
        $upgrades = DB::table('users')
            ->join('memberships', 'users.membership_id', '=', 'memberships.id')
            ->join('membership_types', 'memberships.membership_type_id', '=', 'membership_types.id')
            ->where('membership_types.name', MembershipType::FULL_MEMBERSHIP)
            ->whereBetween('memberships.started_at', [$yesterday, $today])
            ->select('users.id', 'users.first_name', 'users.last_name', 'users.phone_number')
            ->get();
        
        // Format message
        $message = "*DAILY RECAP*. Over the last 24 hours…\n\n";
        
        // Add registrations
        $message .= "*" . $registrations->count() . " people completed registration (Stage 2)*. ";
        $message .= $this->formatUserList($registrations);
        $message .= "\n\n";
        
        // Add free trials
        $message .= "*" . $freeTrials->count() . " person started a free trial (Stage 3)*. ";
        $message .= $this->formatUserList($freeTrials);
        $message .= "\n\n";
        
        // Add upgrades
        $message .= "*" . $upgrades->count() . " people upgraded from free trial to paid (Stage 4)*. ";
        $message .= $this->formatUserList($upgrades);
        
        // Send to Slack
        Http::post(config('services.slack.funnel_events_webhook_url'), [
            'text' => $message
        ]);
        
        Log::info('Sent daily recap to #funnel-events Slack channel');
    }
    
    /**
     * Format a list of users for the Slack message
     */
    private function formatUserList($users)
    {
        if ($users->isEmpty()) {
            return "None.";
        }
        
        return $users->map(function ($user) {
            return "{$user->first_name} {$user->last_name}, {$user->phone_number}";
        })->implode('; ') . '.';
    }
}
