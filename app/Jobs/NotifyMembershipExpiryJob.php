<?php

namespace App\Jobs;

use App\Events\MembershipNotification;
use App\Repositories\Contracts\MembershipRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NotifyMembershipExpiryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Handle the job.
     *
     * This job processes membership notifications by sending notifications for memberships that are about to expire and
     * for memberships that have been downgraded.
     *
     * @param MembershipRepository $membershipRepository
     *
     * @throws \Exception
     */
    public function handle(
        MembershipRepository $membershipRepository,
    ) {
        try {
            $this->processExpiringMemberships($membershipRepository);
            $this->processDowngradedMemberships($membershipRepository);
        } catch (\Exception $e) {
            Log::error('Failed to process membership notifications', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Process memberships that are about to expire
     */
    private function processExpiringMemberships(MembershipRepository $membershipRepository)
    {
        $expiringDate = Carbon::now()->addDays(2);

        // Get memberships expiring in 2 days
        $expiringMemberships = $membershipRepository->findBy([
            'end_at' => $expiringDate->format('Y-m-d')
        ]);

        foreach ($expiringMemberships as $membership) {
            $user = $membership->user;
            $isTrial = $membership->membershipType->name === 'free_trial';

            // TODO: This hardcoded cost needs to be replaced with dynamic pricing
            $cost = '30';

            event(new MembershipNotification(
                $isTrial ? 'free_trial_expiring_soon' : 'membership_expiring_soon',
                $membership,
                null,
                $cost
            ));
        }
    }

    /**
     * Process memberships that have been downgraded
     */
    private function processDowngradedMemberships(MembershipRepository $membershipRepository)
    {
        // Get memberships that have ended today and don't have autopay enabled
        $downgradedMemberships = $membershipRepository->findBy([
            'end_at' => Carbon::now()->format('Y-m-d'),
            'is_autopay_enabled' => false
        ]);

        foreach ($downgradedMemberships as $membership) {
            event(new MembershipNotification(
                'membership_downgraded',
                $membership,
                null
            ));

            Log::info('Sent downgrade notification', [
                'membership_id' => $membership->id,
                'user_id' => $membership->user->id
            ]);
        }
    }
}
