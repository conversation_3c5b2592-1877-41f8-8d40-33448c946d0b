<?php

namespace App\Jobs;

use App\Models\Video;
use App\Services\Contracts\VideoProcessor;
use App\Repositories\Contracts\VideoRepository;
use App\Repositories\Contracts\NotificationRepository;
use App\Repositories\Contracts\QuestionSendRepository;
use App\Services\Contracts\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CleanupTrashedVideosJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        // No dependencies in constructor
    }

    /**
     * Execute the job.
     */
    public function handle(
        VideoProcessor $videoProcessor,
        NotificationRepository $notificationRepository,
        VideoRepository $videoRepository,
        QuestionSendRepository $questionSendRepository,
        NotificationService $notificationService
    )
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        $videos = Video::onlyTrashed()
            ->where('deleted_at', '<=', $thirtyDaysAgo)
            ->get();

        foreach ($videos as $video) {
            DB::beginTransaction();

            try {
                // Delete from Mux
                if ($video->mux_id) {
                    $videoProcessor->deleteVideo($video->mux_id);
                }

                // Delete video file from S3
                if ($video->video_file) {
                    Storage::disk('s3')->delete($video->video_file);
                }

                // Delete transcript file from S3
                if ($video->transcript_file) {
                    Storage::disk('s3')->delete($video->transcript_file);
                }

                $questionSend = $questionSendRepository->findOneBy(['video_id' => $video['id']]);

                if ($questionSend) {
                    $questionSendRepository->update($questionSend, ['video_id' => null]);

                    $notification = $notificationRepository->findOneBy([
                        'question_id' => $questionSend['question_id'],
                        'video_id' => $video['id'],
                    ]);

                    if ($notification) {
                        // Delete from Courier if courier_id exists
                        if ($notification->courier_id) {
                            $notificationService->archiveMessage($notification->courier_id);
                        }

                        $notificationRepository->delete($notification);
                    }
                }

                $video->videoRecoveryWithPhones()->delete();

                // Force delete the video record
                $video->forceDelete();

                DB::commit();
                \Log::info("Successfully deleted video ID: {$video->id}");
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error("Failed to delete video ID: {$video->id}. Error: {$e->getMessage()}");
            }
        }

        \Log::info('Video cleanup completed');
    }
}
