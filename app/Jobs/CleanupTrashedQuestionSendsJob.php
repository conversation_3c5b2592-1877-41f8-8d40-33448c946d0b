<?php
namespace App\Jobs;

use App\Models\QuestionSend;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CleanupTrashedQuestionSendsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        try {
            // Determine cleanup time based on environment
            $cleanupTime = env('APP_ENV') === 'production' ? Carbon::now()->subDays(7) : Carbon::now()->subMinutes(5);

            QuestionSend::whereNotNull('trashed_at')
                ->where('trashed_at', '<=', $cleanupTime)
                ->chunk(100, function ($questionSends) {
                    foreach ($questionSends as $questionSend) {
                        $questionSend->delete();
                    }
                });

            Log::info("Cleanup job completed. Processed trashed question sends.", [
                'cleanup_time' => $cleanupTime,
            ]);
        } catch (\Exception $e) {
            Log::error("Error in question sends cleanup job: " . $e->getMessage());
            throw $e;
        }
    }
}
