<?php
namespace App\Services;

use App\Services\Contracts\TranscribeService;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Storage;

class VideoTranscribeService implements TranscribeService
{

    /**
     * The Guzzle HTTP client.
     *
     * @var Client
     */
    protected $httpClient;

    /**
     * The Deepgram API key.
     *
     * @var string
     */
    protected $apiKey;

    /**
     * VideoTranscribeService constructor.
     *
     * @param Client $httpClient
     */
    public function __construct(Client $httpClient)
    {
        $this->httpClient = new Client([
            'base_uri' => 'https://api.deepgram.com/v1/',
            'headers'  => [
                'Authorization' => 'Token ' . config('services.deepgram.api_key'),
                'Content-Type'  => 'application/json',
            ],
        ]);
    }

    /**
     * Transcribe audio or video using Deepgram.
     *
     * @param string $audioUrl
     * @param array $options
     * @return array
     * @throws \Exception
     */
    public function transcribe(string $audioUrl, array $options = []): array
    {
        $defaultOptions = [
            'model'    => 'nova-2',
            'language' => 'en',
        ];

        $transcriptionOptions = array_merge($defaultOptions, $options);

        try {

            $response = $this->httpClient->post('listen', [
                'json' => [
                    'url'     => $audioUrl,
                    'options' => $transcriptionOptions,
                ],
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new Exception('Deepgram API error: ' . $response->getBody()->getContents());
            }

            $responseData = json_decode($response->getBody()->getContents(), true);
            $transcript   = $responseData['results']['channels'][0]['alternatives'][0]['transcript'] ?? null;

            if (! $transcript) {
                throw new Exception('Transcript not found in Deepgram response.');
            }

            return [
                'transcription' => $transcript,
                'raw_response'  => $responseData,
            ];
        } catch (Exception $e) {
            throw new Exception('Error during transcription: ' . $e->getMessage());
        }
    }

    /**
     * Save the transcription data to an S3 file.
     *
     * @param array $transcriptionData
     * @return string
     * @throws \Exception
     */
    public function saveTranscription(array $transcriptionData): string
    {
        try {
            $filename = 'transcripts/' . uniqid() . '.json';

            Storage::disk('s3')->put($filename, json_encode($transcriptionData), 'public');

            return $filename;
        } catch (Exception $e) {
            // Handle errors during file saving to S3
            throw new Exception('Error saving transcription file: ' . $e->getMessage());
        }
    }
}
