<?php
namespace App\Services;

use App\Services\Contracts\AIService;
use GuzzleHttp\Client;

class OpenAIService implements AIService
{
    /**
     * The OpenAI API base URL.
     *
     * @var string
     */
    protected $apiUrl = 'https://api.openai.com/v1/';

    /**
     * The Guzzle HTTP client.
     *
     * @var Client
     */
    protected $httpClient;

    /**
     * OpenAIService constructor.
     *
     * @param Client $httpClient
     */
    public function __construct(Client $httpClient)
    {
        $this->httpClient = new Client([
            'base_uri' => $this->apiUrl,
            'headers'  => [
                'Authorization' => 'Bearer ' . config('services.openai.api_key'),
                'Content-Type'  => 'application/json',
            ],
        ]);
    }

    /**
     * Generate follow-up questions using OpenAI API
     *
     * @param string $originalQuestion
     * @param string $transcription
     * @param string $context
     * @param array $pastQuestions
     * @param string $category
     * @return array
     */
    public function generateFollowUpQuestions(
        string $originalQuestion,
        string $transcription,
        string $context,
        array $pastQuestions,
        string $category
    ): array {
        // Build prompt based on video type
        $prompt = $this->buildPrompt(
            $context === 'themselves',
            $pastQuestions,
            $category
        );

        $response = $this->httpClient->post('chat/completions', [
            'json' => [
                'model' => 'gpt-4',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $prompt,
                    ],
                    [
                        'role' => 'user',
                        'content' => "Original Question: {$originalQuestion}\n\nResponse: {$transcription}",
                    ],
                ],
                'temperature' => 0.7,
            ],
        ]);

        $result = json_decode($response->getBody(), true);
        return $this->parseQuestions($result['choices'][0]['message']['content']);
    }

    /**
     * Build the prompt for OpenAI
     *
     * @param bool $isSelfRecorded
     * @param array $pastQuestions
     * @param string $category
     * @return string
     */
    protected function buildPrompt(bool $isSelfRecorded, array $pastQuestions, string $category): string
    {
        $pastQuestionsText = $this->formatPastQuestions($pastQuestions);
        
        if ($isSelfRecorded) {
            return <<<EOT
Capsle Stories is the video recording app that makes it fun for loved ones to record their memories, experiences, and wisdom, and easy to preserve them for generations to come.

PAST QUESTIONS & ANSWERS: {$pastQuestionsText}

NEXT QUESTION: Imagine that you are {$pastQuestions['user_name']}. You are interested in capturing your life experiences, memories, values, wisdom, and unique perspective, and sharing them with your family and with generations to come. List exactly 5 potential follow up questions that would fall into the category of {$category} that you might be interested in recording responses to that would help you dive deeper into your life story.

Write your list in the following format:
{question #1: Your first question}
{question #2: Your second question}
{question #3: Your third question}
{question #4: Your fourth question}
{question #5: Your fifth question}
EOT;
        }

        return <<<EOT
Capsle Stories is the video recording app that makes it fun for loved ones to record their memories, experiences, and wisdom, and easy to preserve them for generations to come.

PAST QUESTIONS & ANSWERS: {$pastQuestionsText}

NEXT QUESTION: Imagine that you are {$pastQuestions['user_name']}'s {$pastQuestions['relationship']}, {$pastQuestions['sender_name']}. You are aware of all of the PAST QUESTIONS ANSWERED & ANSWERS listed above, and you are interested in learning more about {$pastQuestions['user_name']}'s life experiences, memories, values, wisdom, and unique perspective. List exactly 5 potential follow up questions that would fall into the category of {$category}.

Write your list in the following format:
{question #1: Your first question}
{question #2: Your second question}
{question #3: Your third question}
{question #4: Your fourth question}
{question #5: Your fifth question}
EOT;
    }

    protected function formatPastQuestions(array $pastQuestions): string
    {
        $formatted = [];
        foreach ($pastQuestions['questions'] as $q) {
            if ($q['is_self_recorded']) {
                $formatted[] = "- {$pastQuestions['user_name']}'s selected the question for themselves, \"{$q['text']}\" from the {$q['category']} category. {$pastQuestions['user_name']} answered: \"{$q['transcript']}\"";
            } else {
                $formatted[] = "- {$pastQuestions['user_name']}'s {$q['relationship']} asked, \"{$q['text']}\" from the {$q['category']} category. {$pastQuestions['user_name']} answered: \"{$q['transcript']}\"";
            }
        }
        return implode("\n", $formatted);
    }

    protected function parseQuestions(string $response): array
    {
        $lines = explode("\n", $response);

        // Extract questions using regex (e.g., `{question #1: Text}`)
        $questions = [];
        foreach ($lines as $line) {
            if (preg_match('/\{question #\d+: (.+)\}/', trim($line), $matches)) {
                $questions[] = trim($matches[1]);
            }
        }

        return $questions;
    }

}
