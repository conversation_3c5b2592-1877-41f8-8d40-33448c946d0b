<?php

namespace App\Services;

use App\Services\Contracts\NotificationService;
use GuzzleHttp\Client;
use App\Events\NotificationSent;

class CourierNotificationService implements NotificationService
{
    /**
     * The Courier API base URL.
     *
     * @var string
     */
    protected $courierApiUrl;

    /**
     * The Guzzle HTTP client.
     *
     * @var Client
     */
    protected $httpClient;

    /**
     * CourierNotificationService constructor.
     *
     * @param Client $httpClient
     */
    public function __construct(Client $httpClient)
    {
        $this->courierApiUrl = config('services.courier.base_url');
        $this->httpClient    = $httpClient;
    }

    /**
     * Create notification data for a specific event.
     *
     * @param string $eventType
     * @param string $title
     * @param string $dynamicData
     * @return array
     */
    public function createNotificationData(string $eventType, string $title, string $dynamicData): array
    {
        $data   = json_decode($dynamicData, true);
        $userId = $data['notificationData']['userId'] ?? '';

        // Get push title and text from dynamic data or use defaults
        $pushTitle = $data['notificationData']['push_title'] ?? 'Capsle Stories';
        $pushText  = $data['notificationData']['push_text'] ?? $title;

        return [
            'message' => [
                'to'        => [
                    'user_id' => (string) $userId,
                ],
                'content'   => [
                    'title' => $pushTitle,
                    'body'  => $pushText,
                ],
                'routing'   => [
                    'method'   => 'all',
                    'channels' => ['push'],
                ],
                'providers' => [
                    'firebase-fcm' => [
                        'override' => [
                            'notification' => null,
                            'data'         => [
                                'title'    => $pushTitle,
                                'cta_type' => $eventType,
                            ],
                            'apns'         => [
                                'payload' => [
                                    'aps' => [
                                        'mutable-content' => 1,
                                        'sound'           => 'default',
                                        'alert'           => [
                                            'title' => $pushTitle,
                                            'body'  => $pushText,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * Create notification data for inbox
     */
    public function createNotificationDataForInbox(string $eventType, string $title, string $dynamicData): array
    {
        $data   = json_decode($dynamicData, true);
        $userId = $data['notificationData']['userId'] ?? '';

        if (env('APP_ENV') === 'production') {

            event(new NotificationSent($userId, $title));
        }


        return [
            'message' => [
                'to'        => [
                    'user_id' => (string) $userId,
                ],
                'channels'  => [
                    'push' => [
                        'providers' => [
                            'courier',
                        ],
                    ],
                ],
                'content'   => [
                    'title' => $title,
                    'body'  => $dynamicData,
                ],
                'routing'   => [
                    'method'   => 'all',
                    'channels' => [
                        'push',
                        [
                            'method'   => 'single',
                            'channels' => [],
                        ],
                    ],
                ],
                'providers' => [
                    'firebase-fcm' => [
                        'override' => [
                            'body' => [
                                'data' => [
                                    'cta_type' => $eventType,
                                ],
                                'apns' => [
                                    'payload' => [
                                        'aps' => $dynamicData,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * Send a notification using Courier API.
     *
     * @param array $data
     * @return string|null The unique ID of the sent notification or null if not available.
     * @throws \Exception
     */
    public function sendNotification(array $data): ?string
    {
        try {
            // Make the API request using Guzzle
            $response = $this->httpClient->post($this->courierApiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . config('services.courier.api_key'),
                    'Content-Type'  => 'application/json',
                ],
                'json'    => $data,
            ]);

            $responseData = json_decode($response->getBody(), true);

            // Use "requestId" as the unique ID
            return $responseData['requestId'] ?? null;
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Handle the exception, log the error, etc.
            $responseBody = $e->getResponse()->getBody()->getContents();
            // Log or handle the error message
            throw new \Exception("Courier API error: {$responseBody}", $e->getCode(), $e);
        }
    }

   /**
 * Get message details from Courier
 *
 * @param string $messageId
 * @return array|null
 */
public function getMessage(string $messageId): ?array
{
    try {
        $response = $this->httpClient->get("https://api.courier.com/messages/{$messageId}", [
            'headers' => [
                'Authorization' => 'Bearer ' . config('services.courier.api_key'),
                'Content-Type'  => 'application/json',
            ],
        ]);

        if ($response->getStatusCode() === 200) {
            return json_decode($response->getBody(), true);
        }

        return null;
    } catch (\Exception $e) {
        \Log::error('Failed to get message from Courier: ' . $e->getMessage());
        return null;
    }
} 

    public function archiveMessage(string $requestId): bool
    {
        try {
            $response = $this->httpClient->put("https://api.courier.com/requests/{$requestId}/archive", [
                'headers' => [
                    'Authorization' => 'Bearer ' . config('services.courier.api_key'),
                    'Content-Type'  => 'application/json',
                ],
            ]);
            return $response->getStatusCode() === 202;
        } catch (\Exception $e) {
            \Log::error('Failed to archive message in Courier: ' . $e->getMessage());
            return false;
        }
    }

   
}
