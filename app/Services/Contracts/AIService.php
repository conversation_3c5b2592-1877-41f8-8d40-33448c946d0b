<?php

namespace App\Services\Contracts;

interface AIService
{
    /**
     * Generate follow-up questions based on a video response
     *
     * @param string $questionText Original question text
     * @param string $transcript Video transcript
     * @param string $relationship Relationship type (e.g., "loved one")
     * @param array $pastQuestions Array of past questions and answers
     * @param string $category Question category
     * @return array Array of generated questions
     */
    public function generateFollowUpQuestions(
        string $questionText, 
        string $transcript, 
        string $relationship, 
        array $pastQuestions,
        string $category
    ): array;
} 