<?php

namespace App\Services\Contracts;

interface SMSProvider
{
    /**
     * Send an SMS message.
     *
     * @param string $to The recipient's phone number.
     * @param string $message The SMS message content.
     */
    public function sendSMS($to, $message);
    
    /**
     * Send verification code via SMS using Twilio Verify API.
     *
     * @param string $phoneNumber The recipient's phone number.
     * @param string $channel The channel to send the verification (default: sms).
     * @return object The verification response.
     * 
     * @throws \Exception
     */
    public function sendVerificationCode($phoneNumber, $channel = 'sms');
    
    /**
     * Check verification code using Twilio Verify API.
     *
     * @param string $phoneNumber The phone number to verify.
     * @param string $code The verification code to check.
     * @return object The verification check response.
     * 
     * @throws \Exception
     */
    public function checkVerificationCode($phoneNumber, $code);
}
