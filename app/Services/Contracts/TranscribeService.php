<?php

namespace App\Services\Contracts;

interface TranscribeService
{
    /**
     * Transcribe audio or video using Deepgram.
     *
     * @param string $audioUrl
     * @param array $options
     * @return array The transcription response.
     * @throws \Exception
     */
    public function transcribe(string $audioUrl, array $options = []): array;

    /**
     * Save the transcription data to a file.
     *
     * @param array $transcriptionData
     * @return string The file path where the transcription was saved.
     * @throws \Exception
     */
    public function saveTranscription(array $transcriptionData): string;
}
