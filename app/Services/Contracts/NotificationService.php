<?php

namespace App\Services\Contracts;

interface NotificationService
{
    /**
     * Create notification data for a specific event.
     *
     * @param string $eventType
     * @param string $title
     * @param string $dynamicData
     * @return array,
     */
    public function createNotificationData(string $eventType, string $title, string $dynamicData): array;
    public function createNotificationDataForInbox(string $eventType, string $title, string $dynamicData): array;

    /**
     * Send a notification using Courier API.
     *
     * @param array $data
     * @return string|null The unique ID of the sent notification or null if not available.
     * @throws \Exception
     */
    public function sendNotification(array $data): ?string;

   

    /**
     * Get message details from Courier
     *
     * @param string $messageId
     * @return array|null The message data as an array or null if failed
     * @throws \Exception
     */
    public function getMessage(string $messageId): ?array;

    /**
     * Archive a message in Courier
     *
     * @param string $requestId
     * @return bool True if the message was archived successfully, false otherwise
     * @throws \Exception
     */
    public function archiveMessage(string $requestId): bool;
}
