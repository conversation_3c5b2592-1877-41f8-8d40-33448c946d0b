<?php

namespace App\Services\Contracts;

interface VideoProcessor
{
    public function createAuthenticatedUploadUrl();
    public function uploadVideo($uploadUrl, $videoFilePath);
    public function getVideo($videoId);
    public function getDirectUpload($uploadId);
    public function getAssetId($uploadId);
    public function createAsset($videoUrl);
    public function getVideoUrl($assetId);
    public function handleVideoUploading($assetId);
    public function createPlaybackId($assetId);
    public function uploadAndProcessVideo($assetId);
    public function deleteVideo($playbackId);
}
