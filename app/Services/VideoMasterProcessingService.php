<?php

namespace App\Services;

use App\Events\TranscriptionCompleted;
use App\Events\VideoUploaded;
use App\Models\Video;
use App\Repositories\Contracts\VideoRepository;
use App\Services\Contracts\TranscribeService;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VideoMasterProcessingService
{
    protected VideoRepository $videoRepository;
    protected TranscribeService $transcribeService;
    protected Client $httpClient;

    public function __construct(
        VideoRepository $videoRepository,
        TranscribeService $transcribeService,
        Client $httpClient
    ) {
        $this->videoRepository = $videoRepository;
        $this->transcribeService = $transcribeService;
        $this->httpClient = $httpClient;
    }

    /**
     * Process video master ready webhook data
     */
    public function processVideoMaster(int $videoId, string $masterUrl): void
    {
        $video = $this->videoRepository->findOne($videoId);
        
        if (!$video instanceof Video) {
            throw new Exception("Video with ID {$videoId} not found");
        }

        // Validate video hasn't been processed already
        if ($video->video_file || $video->transcript_file) {
            Log::info('Video already processed, skipping', [
                'video_id' => $videoId,
                'video_file' => $video->video_file,
                'transcript_file' => $video->transcript_file
            ]);
            return;
        }

        Log::info('Starting video master processing', [
            'video_id' => $videoId,
            'master_url' => $masterUrl
        ]);

        // Process video in steps with proper error handling
        $videoFilename = $this->downloadAndUploadVideo($masterUrl, $videoId);
        $transcription = $this->transcribeVideo($masterUrl, $videoId);
        $transcriptFile = $this->saveTranscription($transcription, $videoId);
        
        // Update video record atomically
        $this->updateVideoRecord($video, $videoFilename, $transcriptFile);
        
        // Fire events after successful processing
        $this->fireCompletionEvents($video, $transcription);

        Log::info('Video master processing completed successfully', [
            'video_id' => $videoId,
            'video_file' => $videoFilename,
            'transcript_file' => $transcriptFile
        ]);
    }

    /**
     * Download video from master URL and upload to S3 using streaming
     */
    private function downloadAndUploadVideo(string $masterUrl, int $videoId): string
    {
        $filename = $this->generateVideoFilename();
        
        try {
            Log::info('Starting video download and upload', [
                'video_id' => $videoId,
                'filename' => $filename
            ]);

            // Configure HTTP client for streaming
            $response = $this->httpClient->get($masterUrl, [
                'stream' => true,
                'timeout' => 120, // 2 minutes timeout
                'connect_timeout' => 30,
                'headers' => [
                    'User-Agent' => 'LegacyLock-VideoProcessor/1.0'
                ]
            ]);

            $stream = $response->getBody();
            
            // Upload stream directly to S3 to avoid memory issues
            $result = Storage::disk('s3')->put($filename, $stream, 'public');
            
            if (!$result) {
                throw new Exception('Failed to upload video to S3');
            }
            
            Log::info('Video successfully uploaded to S3', [
                'video_id' => $videoId,
                'filename' => $filename,
                'size' => $response->getHeaderLine('Content-Length')
            ]);
            
            return $filename;
            
        } catch (RequestException $e) {
            Log::error('HTTP error during video download', [
                'video_id' => $videoId,
                'master_url' => $masterUrl,
                'error' => $e->getMessage(),
                'response_code' => $e->getCode()
            ]);
            throw new Exception("Failed to download video: {$e->getMessage()}", 0, $e);
            
        } catch (Exception $e) {
            Log::error('Error during video upload', [
                'video_id' => $videoId,
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);
            throw new Exception("Failed to upload video: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Transcribe video using the transcription service
     */
    private function transcribeVideo(string $masterUrl, int $videoId): array
    {
        try {
            Log::info('Starting video transcription', [
                'video_id' => $videoId
            ]);

            $transcription = $this->transcribeService->transcribe($masterUrl, [
                'model' => 'nova-2',
                'smart_format' => true,
                'punctuate' => true,
                'diarize' => false, // Set to true if you need speaker identification
                'language' => 'en'
            ]);
            
            Log::info('Video transcription completed', [
                'video_id' => $videoId,
                'transcript_length' => strlen($transcription['transcription'] ?? '')
            ]);
            
            return $transcription;
            
        } catch (Exception $e) {
            Log::error('Error during video transcription', [
                'video_id' => $videoId,
                'master_url' => $masterUrl,
                'error' => $e->getMessage()
            ]);
            throw new Exception("Failed to transcribe video: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Save transcription to S3
     */
    private function saveTranscription(array $transcription, int $videoId): string
    {
        try {
            $transcriptFile = $this->transcribeService->saveTranscription($transcription);
            
            Log::info('Transcription saved to S3', [
                'video_id' => $videoId,
                'transcript_file' => $transcriptFile
            ]);
            
            return $transcriptFile;
            
        } catch (Exception $e) {
            Log::error('Error saving transcription', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);
            throw new Exception("Failed to save transcription: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Update video record with processed files
     */
    private function updateVideoRecord(Video $video, string $videoFilename, string $transcriptFile): void
    {
        try {
            $this->videoRepository->update($video, [
                'video_file' => $videoFilename,
                'transcript_file' => $transcriptFile,
                'status' => 'ready' // Update status to indicate processing is complete
            ]);
            
            Log::info('Video record updated successfully', [
                'video_id' => $video->id,
                'video_file' => $videoFilename,
                'transcript_file' => $transcriptFile
            ]);
            
        } catch (Exception $e) {
            Log::error('Error updating video record', [
                'video_id' => $video->id,
                'error' => $e->getMessage()
            ]);
            throw new Exception("Failed to update video record: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Fire completion events
     */
    private function fireCompletionEvents(Video $video, array $transcription): void
    {
        try {
            $transcriptionText = json_encode($transcription);
            
            event(new TranscriptionCompleted($video, $transcriptionText));
            event(new VideoUploaded($video));
            
            Log::info('Completion events fired successfully', [
                'video_id' => $video->id
            ]);
            
        } catch (Exception $e) {
            Log::error('Error firing completion events', [
                'video_id' => $video->id,
                'error' => $e->getMessage()
            ]);
            // Don't throw here as the main processing is complete
        }
    }

    /**
     * Generate unique filename for video
     */
    private function generateVideoFilename(): string
    {
        return 'videos/' . uniqid('video_', true) . '.mp4';
    }
}
