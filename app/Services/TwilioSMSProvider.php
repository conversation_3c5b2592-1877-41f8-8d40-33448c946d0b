<?php

namespace App\Services;

use App\Services\Contracts\SMSProvider;
use Twilio\Rest\Client;

class TwilioSMSProvider implements SMSProvider
{
    /**
     * The Twilio client instance.
     *
     * @var Client
     */
    protected $twilio;

    /**
     * The Twilio Verify service SID.
     *
     * @var string
     */
    protected $verifyServiceSid;

    /**
     * TwilioSMSProvider constructor.
     */
    public function __construct()
    {
        $this->twilio = new Client(
            config('services.twilio.sid'),
            config('services.twilio.token')
        );
        $this->verifyServiceSid = config('services.twilio.verify_service_sid');
    }

    /**
     * Send an SMS using Twilio.
     *
     * @param string $to      The recipient's phone number.
     * @param string $message The SMS message content.
     *
     * @throws \Twilio\Exceptions\TwilioException
     */
    public function sendSMS($to, $message)
    {
        $this->twilio->messages->create(
            $to,
            [
                'from' => config('services.twilio.from'),
                'body' => $message,
            ]
        );
    }

    /**
     * Send verification code via SMS using Twilio Verify API.
     *
     * @param string $phoneNumber The recipient's phone number.
     * @param string $channel The channel to send the verification (default: sms).
     * @return object The verification response.
     * 
     * @throws \Twilio\Exceptions\TwilioException
     */
    public function sendVerificationCode($phoneNumber, $channel = 'sms')
    {
        $verification = $this->twilio->verify->v2
            ->services($this->verifyServiceSid)
            ->verifications
            ->create($phoneNumber, $channel);

        return $verification;
    }

    /**
     * Check verification code using Twilio Verify API.
     *
     * @param string $phoneNumber The phone number to verify.
     * @param string $code The verification code to check.
     * @return object The verification check response.
     * 
     * @throws \Twilio\Exceptions\TwilioException
     */
    public function checkVerificationCode($phoneNumber, $code)
    {
        $verificationCheck = $this->twilio->verify->v2
            ->services($this->verifyServiceSid)
            ->verificationChecks
            ->create([
                'to' => $phoneNumber,
                'code' => $code
            ]);

        return $verificationCheck;
    }
}
