<?php

namespace App\Services;

use App\Services\Contracts\SMSProvider;
use Twilio\Rest\Client;

class TwilioSMSProvider implements SMSProvider
{
    /**
     * The Twilio client instance.
     *
     * @var Client
     */
    protected $twilio;

    /**
     * TwilioSMSProvider constructor.
     */
    public function __construct()
    {
        $this->twilio = new Client(
            config('services.twilio.sid'),
            config('services.twilio.token')
        );
    }

    /**
     * Send an SMS using Twilio.
     *
     * @param string $to      The recipient's phone number.
     * @param string $message The SMS message content.
     *
     * @throws \Twilio\Exceptions\TwilioException
     */

    public function sendSMS($to, $message)
    {
        $this->twilio->messages->create(
            $to,
            [
                'from' => config('services.twilio.from'),
                'body' => $message,
            ]
        );
    }
}
