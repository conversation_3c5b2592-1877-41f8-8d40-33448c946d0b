<?php

namespace App\Services;

use App\Services\Contracts\VideoProcessor;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Storage;
use MuxPhp\Api\AssetsApi;
use MuxPhp\Api\DirectUploadsApi;
use MuxPhp\Configuration;
use MuxPhp\Models\CreateAssetRequest;
use MuxPhp\Models\CreateUploadRequest;

class MuxVideoProcessor implements VideoProcessor
{
    protected $assetsApi;
    protected $uploadsApi;

    public function __construct()
    {
        $config = Configuration::getDefaultConfiguration()
            ->setUsername(env('MUX_TOKEN_ID'))
            ->setPassword(env('MUX_TOKEN_SECRET'));

        $this->assetsApi = new AssetsApi(new Client(), $config);
        $this->uploadsApi = new DirectUploadsApi(new \GuzzleHttp\Client(), $config);
    }

    public function createAuthenticatedUploadUrl()
    {
        $watermarkUrl = 'https://upload.wikimedia.org/wikipedia/commons/4/47/PNG_transparency_demonstration_1.png';

        $watermarkSettings = [
            "url" => $watermarkUrl,
            "overlay_settings" => [
                "vertical_align" => "bottom",
                "horizontal_align" => "right",
                "vertical_margin" => "10%",
                "horizontal_margin" => "10%",
                "width" => "15%",
                "opacity" => "75%",
            ],
        ];
        // Create asset settings with watermark
        $assetSettings = new CreateAssetRequest([
            "playback_policy" => ["public"],
            "master_access" => "temporary",
            // "input" => [$watermarkSettings],
        ]);

        // Define upload settings, such as timeout and CORS origin
        $uploadSettings = new CreateUploadRequest([
            "timeout" => 3600,
            "new_asset_settings" => $assetSettings,
            "cors_origin" => "http: //legacylock-api/api/",
        ]);

        // Create a Direct Upload
        $upload = $this->uploadsApi->createDirectUpload($uploadSettings);
        $uploadUrl = $upload["data"]["url"];
        $uploadId = $upload->getData()->getId();
        return [$uploadUrl, $uploadId];
    }

    public function uploadVideo($uploadUrl, $videoFilePath)
    {

        // Make a PUT request to the authenticated upload URL to upload the video
        $client = new \GuzzleHttp\Client();
        $response = $client->put($uploadUrl, [
            'body' => fopen($videoFilePath, 'r'),
        ]);

        if ($response->getStatusCode() === 200) {

            return true;
        }
        return null;
    }

    public function getVideo($assetId)
    {
        try {

            $this->createPlaybackId($assetId);
            // Retrieve information about the video asset
            $assetInfo = $this->assetsApi->getAsset($assetId);

            // Extract relevant data from the assetInfo response
            $videoDetails = $assetInfo->getData();
            return $videoDetails;
        } catch (\Exception $e) {

            return null;
        }
    }

    public function handleVideoUploading($assetId)
    {
        do {
            $videoDetails = $this->getVideo($assetId);

            if ($videoDetails && isset($videoDetails['status']) && $videoDetails['status'] !== 'errored') {
                if (in_array($videoDetails['status'], ['preparing'])) {
                    sleep(1);
                }
            } else {
                throw new \Exception('Video processing failed or details not available');
            }
        } while ($videoDetails && $videoDetails['status'] !== 'ready');

        if ($videoDetails && $videoDetails['status'] === 'ready') {
            $requestData = [
                'mux_id' => $assetId,
                'owner_user_id' => 1,
                'status' => $videoDetails['status'],
                'duration' => $videoDetails['duration'],
            ];
            return $requestData;
        }
    }

    public function getAssetId($uploadId)
    {

        try {
            $directUploadInfo = $this->uploadsApi->getDirectUpload($uploadId);
            // Extract the asset_id from the response
            $assetId = $directUploadInfo->getData()->getAssetId();
            return $assetId;
        } catch (Exception $e) {

            return null;
        }
    }

    public function getDirectUpload($uploadId)
    {

        try {
            $result = $this->uploadsApi->getDirectUpload($uploadId);
            return $result;
        } catch (Exception $e) {
            return null;
        }
    }

    public function createAsset($videoUrl)
    {
        $watermarkUrl = 'https://upload.wikimedia.org/wikipedia/commons/4/47/PNG_transparency_demonstration_1.png';
        try {

            $inputVideo = ["url" => $videoUrl];

            // Create the watermark input with overlay settings
            $inputWatermark = [
                "url" => $watermarkUrl,
                "overlay_settings" => [
                    "vertical_align" => "bottom", // e.g., "top", "middle", "bottom"
                    "horizontal_align" => "right", // e.g., "left", "center", "right"
                    "vertical_margin" => "10%", // e.g., "10px" or "10%"
                    "horizontal_margin" => "10%", // e.g., "10px" or "10%"
                    "width" => "15%", // e.g., "100px" or "15%"
                    "opacity" => "75%", // e.g., "50%"
                ],
            ];

            // Create asset request with video and watermark inputs
            $createAssetRequest = new CreateAssetRequest([
                "input" => [$inputVideo, $inputWatermark],
                "playback_policy" => ["public"],
            ]);

            // Call the Mux API to create an asset with watermark
            $assetResponse = $this->assetsApi->createAsset($createAssetRequest);
            // Access the asset ID from the response
            $assetData = $assetResponse->getData();
            $assetId = $assetData->getId();
            return $assetId;
        } catch (Exception $e) {
            return null;
        }
    }

    public function createPlaybackId($assetId, $policy = 'public')
    {
        try {
            // Define the playback ID creation request
            $createPlaybackIdRequest = [
                'policy' => $policy,
            ];

            // Call the Mux API to create a playback ID for the specified asset
            $playbackIdResponse = $this->assetsApi->createAssetPlaybackId($assetId, $createPlaybackIdRequest);

            // Access the created playback ID from the response
            $playbackIdData = $playbackIdResponse->getData();
            $playbackId = $playbackIdData->getId();
            return $playbackId;
        } catch (Exception $e) {
            return null;
        }
    }

    public function retrievePlaybackId($assetId, $playbackId)
    {
        try {
            // Make a GET request to retrieve playback ID information
            $playbackIdInfo = $this->assetsApi->getAssetPlaybackId($assetId, $playbackId);

            // Access the playback ID details from the response
            $playbackIdData = $playbackIdInfo->getData();
            $playbackIdPolicy = $playbackIdData->getPolicy();
            // You can customize this logic based on your requirements

            if ($playbackIdPolicy === 'public') {
                // For public playback IDs, construct the playback URL
                $videoUrl = "https://stream.mux.com/{$playbackId}";
            } else {
                // Handle other policies as needed
                $videoUrl = null;
            }

            return $videoUrl;
        } catch (Exception $e) {
            return null;
        }
    }
    public function uploadAndProcessVideo($file)
    {
        try {
            list($uploadUrl, $uploadId) = $this->createAuthenticatedUploadUrl();
            $this->uploadVideo($uploadUrl, $file->path());
            $assetId = $this->getAssetId($uploadId);
            return $this->handleVideoUploading($assetId);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    public function getVideoUrl($assetId)
    {
        try {
            // Call the Mux API to get video details
            $videoDetails = $this->assetsApi->getAsset($assetId)->getData();

            // Extract relevant data from the video details
            $status = $videoDetails->getStatus();
            $playbackIds = $videoDetails->getPlaybackIds();

            // Check if the video status is "ready" and has playback IDs
            if ($status === 'ready' && !empty($playbackIds)) {
                // For simplicity, assume the first playback ID is public
                $playbackId = $playbackIds[0]->getId();
                $videoUrl = "https://stream.mux.com/{$playbackId}";

                return $videoUrl;
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    public function updateMasterAccess($assetId)
    {
        $body = json_encode(['master_access' => 'temporary']);

        $client = new \GuzzleHttp\Client();
        $response = $client->put("https://api.mux.com/video/v1/assets/{$assetId}/master-access", [
            'body' => $body,
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Basic ' . base64_encode(env('MUX_TOKEN_ID') . ':' . env('MUX_TOKEN_SECRET')),
            ],
        ]);

        return $response;
    }

    public function getMasterDownloadUrl($assetId)
    {
        $assetDetails = $this->assetsApi->getAsset($assetId)->getData();

        if ($assetDetails->getMasterAccess() === 'temporary') {
            $masterUrl = $assetDetails->getMaster()->getUrl();
            return $masterUrl;
        } else {
            return null;
        }
    }

    /**
     * Delete a video from Mux using playback ID
     *
     * @param string $playbackId The Mux playback ID to delete
     * @return bool
     */
    public function deleteVideo($playbackId)
    {
        try {
            // Get all assets and find the one with matching playback ID
            $assets = $this->assetsApi->listAssets();
            $assetId = null;

            // Search for the asset with the matching playback ID
            foreach ($assets->getData() as $asset) {
                $playbackIds = $asset->getPlaybackIds();
                foreach ($playbackIds as $pid) {
                    if ($pid->getId() === $playbackId) {
                        $assetId = $asset->getId();
                        break 2; // Exit both loops once we find the matching asset
                    }
                }
            }

            // Only delete if we found a matching asset
            if ($assetId) {
                // Delete the specific asset which will also delete all its playback IDs
                $this->assetsApi->deleteAsset($assetId);
                return true;
            }

            return false;
        } catch (Exception $e) {
            \Log::error('Failed to delete video from Mux: ' . $e->getMessage());
            return false;
        }
    }
}
