<?php

namespace App\Console\Commands;

use App\Jobs\SendMonthlyFunnelRecapJob;
use Illuminate\Console\Command;

class SendMonthlyFunnelRecap extends Command
{
    protected $signature = 'send:monthly-funnel-recap';
    protected $description = 'Send monthly funnel recap to Slack';

    public function handle()
    {
        try {
            SendMonthlyFunnelRecapJob::dispatch();
            $this->info('Monthly funnel recap job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing monthly funnel recap job: ' . $e->getMessage());
            return 1;
        }
    }
}