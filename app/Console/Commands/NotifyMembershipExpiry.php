<?php

namespace App\Console\Commands;

use App\Jobs\NotifyMembershipExpiryJob;
use Illuminate\Console\Command;

class NotifyMembershipExpiry extends Command
{
    protected $signature = 'notify:membership-expiry';
    protected $description = 'Notify users about trial/membership expiring in 2 days and process downgraded memberships';

    public function handle()
    {
        try {
            NotifyMembershipExpiryJob::dispatch();
            $this->info('Membership expiry notification job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing notification job: ' . $e->getMessage());
            return 1;
        }
    }
}
