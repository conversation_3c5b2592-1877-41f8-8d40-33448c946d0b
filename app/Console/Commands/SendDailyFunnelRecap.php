<?php

namespace App\Console\Commands;

use App\Jobs\SendDailyFunnelRecapJob;
use Illuminate\Console\Command;

class SendDailyFunnelRecap extends Command
{
    protected $signature = 'send:daily-funnel-recap';
    protected $description = 'Send daily funnel recap to Slack';

    public function handle()
    {
        try {
            SendDailyFunnelRecapJob::dispatch();
            $this->info('Daily funnel recap job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing daily funnel recap job: ' . $e->getMessage());
            return 1;
        }
    }
}