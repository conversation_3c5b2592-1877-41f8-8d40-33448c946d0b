<?php

namespace App\Console\Commands;

use App\Jobs\SendDailyConfirmedUsersSlackJob;
use Illuminate\Console\Command;

class SendDailyConfirmedUsersSlack extends Command
{
    protected $signature = 'send:daily-confirmed-users-slack';
    protected $description = 'Send daily confirmed users report to <PERSON>lack';

    public function handle()
    {
        try {
            SendDailyConfirmedUsersSlackJob::dispatch();
            $this->info('Daily confirmed users report job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing daily funnel recap job: ' . $e->getMessage());
            return 1;
        }
    }
}