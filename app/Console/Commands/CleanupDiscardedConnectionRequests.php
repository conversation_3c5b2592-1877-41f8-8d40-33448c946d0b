<?php

namespace App\Console\Commands;

use App\Jobs\CleanupDiscardedConnectionRequestsJob;
use Illuminate\Console\Command;

class CleanupDiscardedConnectionRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:discarded-connections';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up discarded connection requests';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            CleanupDiscardedConnectionRequestsJob::dispatch();
            $this->info('Cleanup discarded connections job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing cleanup job: ' . $e->getMessage());
            return 1;
        }
    }
} 