<?php

namespace App\Console\Commands;

use App\Jobs\CleanupTrashedVideosJob;
use Illuminate\Console\Command;

class CleanupTrashedVideos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:trashed-videos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up videos that have been soft deleted for more than 30 days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting video cleanup process...');
            
            CleanupTrashedVideosJob::dispatch();
            
            $this->info('Video cleanup job has been queued successfully.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to queue video cleanup job: ' . $e->getMessage());
            \Log::error('Video cleanup job queue failed: ' . $e->getMessage());
            return 1;
        }
    }
} 