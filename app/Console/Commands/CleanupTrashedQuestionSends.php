<?php

namespace App\Console\Commands;

use App\Jobs\CleanupTrashedQuestionSendsJob;
use Illuminate\Console\Command;

class CleanupTrashedQuestionSends extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:trashed-question-sends';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up trashed question sends older than the configured retention period';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            CleanupTrashedQuestionSendsJob::dispatch();
            $this->info('Cleanup trashed question sends job has been queued.');
        } catch (\Exception $e) {
            $this->error('Error queuing cleanup job: ' . $e->getMessage());
            return 1;
        }
    }
} 