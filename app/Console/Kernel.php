<?php
namespace App\Console;

use App\Jobs\CleanupDiscardedConnectionRequestsJob;
use App\Jobs\CleanupTrashedQuestionSendsJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Carbon\Carbon;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('cleanup:trashed-question-sends')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-trashed-question-sends.log'));

        $schedule->command('cleanup:discarded-connections')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-discarded-requests.log'));

        $schedule->command('notify:membership-expiry')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/membership-expiry-notifications.log'));
      
        $schedule->command('send:daily-funnel-recap')
            ->dailyAt('00:00')
            ->timezone('America/Los_Angeles')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/daily-funnel-recap.log'));
      
        $schedule->command('send:monthly-funnel-recap')
            ->monthlyOn(Carbon::now()->endOfMonth()->day, '00:00')
            ->timezone('America/Los_Angeles')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/monthly-funnel-recap.log'));

        $schedule->command('cleanup:trashed-videos')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-trashed-videos.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
    protected function bootstrappers()
    {
        return array_merge(
            [\Bugsnag\BugsnagLaravel\OomBootstrapper::class],
            parent::bootstrappers(),
        );
    }
}
