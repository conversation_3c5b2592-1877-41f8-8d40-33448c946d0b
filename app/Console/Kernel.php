<?php
namespace App\Console;

use App\Jobs\CleanupDiscardedConnectionRequestsJob;
use App\Jobs\CleanupTrashedQuestionSendsJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('cleanup:trashed-question-sends')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-trashed-question-sends.log'));

        $schedule->command('cleanup:discarded-connections')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-discarded-requests.log'));

        $schedule->command('send:daily-confirmed-users-slack')
            ->dailyAt('00:00')
            ->timezone('America/Los_Angeles')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/daily-funnel-recap-user-registrations.log'));

         $schedule->command('cleanup:trashed-videos')
            ->daily()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/cleanup-trashed-videos.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
    protected function bootstrappers()
    {
        return array_merge(
            [\Bugsnag\BugsnagLaravel\OomBootstrapper::class],
            parent::bootstrappers(),
        );
    }
}
