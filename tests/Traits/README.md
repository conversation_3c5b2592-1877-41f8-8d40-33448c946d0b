# AuthenticatesUsers Trait Documentation

এই trait সব test এর জন্য common authentication functionality provide করে।

## 🚀 Quick Start

### Basic Usage

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\AuthenticatesUsers;
use Illuminate\Foundation\Testing\RefreshDatabase;

class YourTest extends TestCase
{
    use RefreshDatabase, AuthenticatesUsers;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAuthentication(); // Important: Call this in setUp
    }

    /** @test */
    public function test_authenticated_endpoint()
    {
        // Quick authentication
        $auth = $this->authenticateUser();

        $response = $this->getJson('/api/protected-route', $auth['headers']);
        $response->assertStatus(200);
    }
}
```

## 📋 Available Methods

### 1. **authenticateUser(array $userAttributes = [])**
সবচেয়ে common method। একটি user create করে এবং token return করে।

```php
// Basic usage
$auth = $this->authenticateUser();
// Returns: ['user' => User, 'token' => string, 'headers' => array]

// Custom attributes
$auth = $this->authenticateUser([
    'phone_number' => '+1234567890',
    'first_name' => 'Custom',
    'last_name' => 'User'
]);
```

### 2. **authenticateAdminUser(array $userAttributes = [])**
Admin user create করে।

```php
$auth = $this->authenticateAdminUser();
$this->assertTrue($auth['user']->is_admin);
```

### 3. **authenticateUserViaApi(string $phoneNumber = null)**
Real API endpoints দিয়ে authentication (integration tests এর জন্য)।

```php
$auth = $this->authenticateUserViaApi('+1555123456');
// Actually calls /api/register and /api/verify
```

### 4. **Helper HTTP Methods**
Authenticated requests এর জন্য shortcut methods।

```php
// GET request
$response = $this->authenticatedGet('/api/user');

// POST request
$response = $this->authenticatedPost('/api/videos', ['title' => 'Test Video']);

// PUT request
$response = $this->authenticatedPut('/api/user/1', ['name' => 'Updated']);

// DELETE request
$response = $this->authenticatedDelete('/api/user/1');
```

### 5. **createMultipleAuthenticatedUsers(int $count, array $userAttributes = [])**
Multiple users create করে।

```php
$users = $this->createMultipleAuthenticatedUsers(3);
$user1 = $users[0];
$user2 = $users[1];
$user3 = $users[2];
```

### 6. **authenticateUserWithMembership(array $userAttributes = [], array $membershipAttributes = [])**
User with membership create করে।

```php
$auth = $this->authenticateUserWithMembership([
    'first_name' => 'Premium'
], [
    'membership_type_id' => 1
]);
```

## 🎯 Common Use Cases

### Case 1: Simple Protected Route Test
```php
public function test_user_can_view_profile()
{
    $auth = $this->authenticateUser();

    $response = $this->getJson('/api/user', $auth['headers']);
    $response->assertStatus(200)
             ->assertJsonStructure(['user' => ['id', 'phone_number']]);
}
```

### Case 2: Admin Only Route Test
```php
public function test_admin_can_access_admin_panel()
{
    $auth = $this->authenticateAdminUser();

    $response = $this->getJson('/api/admin/dashboard', $auth['headers']);
    $response->assertStatus(200);
}

public function test_regular_user_cannot_access_admin_panel()
{
    $auth = $this->authenticateUser(); // Regular user

    $response = $this->getJson('/api/admin/dashboard', $auth['headers']);
    $response->assertStatus(403); // Forbidden
}
```

### Case 3: User Interaction Test
```php
public function test_user_can_send_message_to_another_user()
{
    $users = $this->createMultipleAuthenticatedUsers(2);
    $sender = $users[0];
    $receiver = $users[1];

    $response = $this->postJson('/api/messages', [
        'to_user_id' => $receiver['user']->id,
        'message' => 'Hello!'
    ], $sender['headers']);

    $response->assertStatus(201);
}
```

### Case 4: Custom User Attributes
```php
public function test_user_with_specific_phone_number()
{
    $auth = $this->authenticateUser([
        'phone_number' => '+8801234567890',
        'first_name' => 'Bangladesh',
        'last_name' => 'User'
    ]);

    $this->assertEquals('+8801234567890', $auth['user']->phone_number);
}
```

### Case 5: Integration Test with Real API
```php
public function test_full_registration_flow()
{
    $auth = $this->authenticateUserViaApi('+1555987654');

    // This actually calls the API endpoints
    $response = $this->getJson('/api/user', $auth['headers']);
    $response->assertStatus(200);
}
```

## 🔧 Advanced Usage

### Custom Token Generation
```php
public function test_with_custom_token()
{
    $user = User::factory()->create();
    $token = $this->generateMockCognitoToken($user);

    $headers = ['Authorization' => 'Bearer ' . $token];
    $response = $this->getJson('/api/user', $headers);
}
```

### Mock Cognito Validation
```php
public function test_with_mocked_cognito()
{
    $user = User::factory()->create();
    $this->mockCognitoTokenValidation($user);

    // Your test code here
}
```

## 🛠️ Setup Requirements

### 1. Test Class Setup
```php
protected function setUp(): void
{
    parent::setUp();
    $this->setUpAuthentication(); // Always call this
}
```

### 2. Required Traits
```php
use RefreshDatabase, AuthenticatesUsers;
```

## 📝 Return Structure

সব authentication methods এই structure return করে:

```php
[
    'user' => User,           // User model instance
    'token' => string,        // JWT token
    'headers' => [            // Ready-to-use headers
        'Authorization' => 'Bearer {token}',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
    ]
]
```

## ⚠️ Important Notes

1. **Always call `setUpAuthentication()`** in your test's `setUp()` method
2. **Use `RefreshDatabase`** trait with this trait
3. **Mock tokens** are used by default for unit tests
4. **Real API calls** are available for integration tests
5. **Test environment** is automatically configured

## 🔍 Debugging

### Check Authentication
```php
public function test_debug_authentication()
{
    $auth = $this->authenticateUser();

    dump($auth['user']->toArray());
    dump($auth['token']);
    dump($auth['headers']);
}
```

### Assert Authentication Status
```php
public function test_authentication_assertions()
{
    $auth = $this->authenticateUser();
    $response = $this->getJson('/api/user', $auth['headers']);

    $this->assertApiAuthenticated($response);

    // Test unauthenticated
    $response = $this->getJson('/api/user');
    $this->assertApiUnauthenticated($response);
}
```

এই trait ব্যবহার করে আপনি সহজেই সব test এ authentication handle করতে পারবেন! 🚀
