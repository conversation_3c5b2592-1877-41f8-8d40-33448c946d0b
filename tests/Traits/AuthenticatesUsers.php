<?php

namespace Tests\Traits;

use App\Models\User;

trait AuthenticatesUsers
{
    /**
     * Register, verify, and login a user using phone number and password.
     * Returns only the AccessToken from the login response.
     * If no phone number is provided, generates a random 10-digit number with '+'.
     */
    public function registerVerifyAndLogin($phoneNumber = null, $password = '4^tyLy8jLs')
    {
        if (!$phoneNumber) {
            $phoneNumber = '+' . rand(1000000000, 9999999999);
        }

        // Register
        $this->postJson('/api/register', [
            'phone_number' => $phoneNumber,
        ])->assertStatus(201);

        // Verify (code is always 123456 in non-production)
        $this->postJson('/api/verify', [
            'phone_number' => $phoneNumber,
            'code' => '123456',
        ])->assertStatus(200);

        // Login
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => $phoneNumber,
            'password' => $password,
        ])->assertStatus(200);

        $json = $loginResponse->json();
        return $json['AccessToken'] ?? null;
    }
}
