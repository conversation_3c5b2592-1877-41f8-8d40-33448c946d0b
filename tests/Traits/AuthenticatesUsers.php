<?php

namespace Tests\Traits;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

trait AuthenticatesUsers
{
    /**
     * Create and authenticate a user for testing
     *
     * @param array $userAttributes
     * @return array ['user' => User, 'token' => string, 'headers' => array]
     */
    protected function authenticateUser(array $userAttributes = []): array
    {
        // Create user with default or custom attributes
        $defaultAttributes = [
            'phone_number' => '+1555' . rand(1000000, 9999999),
            'first_name' => 'Test',
            'last_name' => 'User',
            'full_name' => 'Test User',
            'registration_status' => User::REGISTRATION_STATUS_VERIFIED,
            'sub' => 'test-sub-' . uniqid(),
        ];

        $user = User::factory()->create(array_merge($defaultAttributes, $userAttributes));

        // Generate a mock Cognito token for testing
        $token = $this->generateMockCognitoToken($user);

        // Set up authentication headers
        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        return [
            'user' => $user,
            'token' => $token,
            'headers' => $headers
        ];
    }

    /**
     * Create and authenticate an admin user for testing
     *
     * @param array $userAttributes
     * @return array ['user' => User, 'token' => string, 'headers' => array]
     */
    protected function authenticateAdminUser(array $userAttributes = []): array
    {
        $adminAttributes = array_merge($userAttributes, [
            'is_admin' => true
        ]);

        return $this->authenticateUser($adminAttributes);
    }

    /**
     * Generate a mock Cognito token for testing
     *
     * @param User $user
     * @return string
     */
    protected function generateMockCognitoToken(User $user): string
    {
        // Create a simple mock token structure
        $header = base64_encode(json_encode([
            'typ' => 'JWT',
            'alg' => 'RS256'
        ]));

        $payload = base64_encode(json_encode([
            'sub' => $user->sub,
            'phone_number' => $user->phone_number,
            'aud' => config('cognito.client_id', 'test-client-id'),
            'iss' => 'https://cognito-idp.us-east-1.amazonaws.com/test-pool',
            'exp' => time() + 3600, // 1 hour from now
            'iat' => time(),
            'token_use' => 'access'
        ]));

        $signature = base64_encode('mock-signature');

        return $header . '.' . $payload . '.' . $signature;
    }

    /**
     * Authenticate user via API registration and verification flow
     * This method actually calls the API endpoints
     *
     * @param string $phoneNumber
     * @return array ['user' => array, 'token' => string, 'headers' => array]
     */
    protected function authenticateUserViaApi(string $phoneNumber = null): array
    {
        $phoneNumber = $phoneNumber ?: '+1555' . rand(1000000, 9999999);

        // Step 1: Register user
        $registerPayload = ['phone_number' => $phoneNumber];
        $registerResponse = $this->postJson('/api/register', $registerPayload);
        $registerResponse->assertStatus(201);

        // Step 2: Verify with mock code
        $verifyPayload = [
            'phone_number' => $phoneNumber,
            'code' => '123456' // Mock code that works in non-production
        ];
        $verifyResponse = $this->postJson('/api/verify', $verifyPayload);
        $verifyResponse->assertStatus(200);

        $verifyData = $verifyResponse->json();
        $token = $verifyData['AccessToken'];
        $user = $verifyData['user'];

        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        return [
            'user' => $user,
            'token' => $token,
            'headers' => $headers
        ];
    }

    /**
     * Login user via API login endpoint
     *
     * @param string $phoneNumber
     * @param string $password
     * @return array ['user' => array, 'token' => string, 'headers' => array]
     */
    protected function loginUserViaApi(string $phoneNumber, string $password): array
    {
        $loginPayload = [
            'phone_number' => $phoneNumber,
            'password' => $password
        ];

        $loginResponse = $this->postJson('/api/login', $loginPayload);
        $loginResponse->assertStatus(200);

        $loginData = $loginResponse->json();
        $token = $loginData['AccessToken'];
        $user = $loginData['user'];

        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        return [
            'user' => $user,
            'token' => $token,
            'headers' => $headers
        ];
    }

    /**
     * Make authenticated GET request
     *
     * @param string $uri
     * @param array $headers
     * @return \Illuminate\Testing\TestResponse
     */
    protected function authenticatedGet(string $uri, array $headers = []): \Illuminate\Testing\TestResponse
    {
        if (empty($headers)) {
            $auth = $this->authenticateUser();
            $headers = $auth['headers'];
        }

        return $this->getJson($uri, $headers);
    }

    /**
     * Make authenticated POST request
     *
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return \Illuminate\Testing\TestResponse
     */
    protected function authenticatedPost(string $uri, array $data = [], array $headers = []): \Illuminate\Testing\TestResponse
    {
        if (empty($headers)) {
            $auth = $this->authenticateUser();
            $headers = $auth['headers'];
        }

        return $this->postJson($uri, $data, $headers);
    }

    /**
     * Make authenticated PUT request
     *
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return \Illuminate\Testing\TestResponse
     */
    protected function authenticatedPut(string $uri, array $data = [], array $headers = []): \Illuminate\Testing\TestResponse
    {
        if (empty($headers)) {
            $auth = $this->authenticateUser();
            $headers = $auth['headers'];
        }

        return $this->putJson($uri, $data, $headers);
    }

    /**
     * Make authenticated DELETE request
     *
     * @param string $uri
     * @param array $data
     * @param array $headers
     * @return \Illuminate\Testing\TestResponse
     */
    protected function authenticatedDelete(string $uri, array $data = [], array $headers = []): \Illuminate\Testing\TestResponse
    {
        if (empty($headers)) {
            $auth = $this->authenticateUser();
            $headers = $auth['headers'];
        }

        return $this->deleteJson($uri, $data, $headers);
    }

    /**
     * Create multiple authenticated users for testing
     *
     * @param int $count
     * @param array $userAttributes
     * @return array
     */
    protected function createMultipleAuthenticatedUsers(int $count, array $userAttributes = []): array
    {
        $users = [];

        for ($i = 0; $i < $count; $i++) {
            $users[] = $this->authenticateUser($userAttributes);
        }

        return $users;
    }

    /**
     * Assert user is authenticated in response
     *
     * @param \Illuminate\Testing\TestResponse $response
     * @return void
     */
    protected function assertAuthenticated(\Illuminate\Testing\TestResponse $response): void
    {
        $response->assertStatus(200);
        // Add more authentication assertions as needed
    }

    /**
     * Assert user is not authenticated in response
     *
     * @param \Illuminate\Testing\TestResponse $response
     * @return void
     */
    protected function assertUnauthenticated(\Illuminate\Testing\TestResponse $response): void
    {
        $response->assertStatus(401);
    }

    /**
     * Create user with membership for testing
     *
     * @param array $userAttributes
     * @param array $membershipAttributes
     * @return array ['user' => User, 'token' => string, 'headers' => array, 'membership' => Membership]
     */
    protected function authenticateUserWithMembership(array $userAttributes = [], array $membershipAttributes = []): array
    {
        $auth = $this->authenticateUser($userAttributes);

        // Create membership if needed
        if (class_exists('\App\Models\Membership')) {
            $membership = \App\Models\Membership::factory()->create(array_merge([
                'user_id' => $auth['user']->id
            ], $membershipAttributes));

            $auth['membership'] = $membership;
        }

        return $auth;
    }

    /**
     * Mock Cognito token validation for testing
     * This helps bypass actual Cognito validation in tests
     *
     * @param User $user
     * @return void
     */
    protected function mockCognitoTokenValidation(User $user): void
    {
        // Mock the getCognitoTokeDetails helper function
        $this->app->bind('cognito.token.details', function () use ($user) {
            return [
                'sub' => $user->sub,
                'phone_number' => $user->phone_number,
                'exp' => time() + 3600,
                'iat' => time(),
            ];
        });
    }

    /**
     * Set up test environment for authentication
     * Call this in setUp() method of your test classes
     *
     * @return void
     */
    protected function setUpAuthentication(): void
    {
        // Set testing environment
        config(['app.env' => 'testing']);

        // Mock Cognito configuration
        config([
            'cognito.client_id' => 'test-client-id',
            'cognito.client_secret' => 'test-client-secret',
            'cognito.user_pool_id' => 'test-pool-id',
        ]);
    }

    /**
     * Create a user and get real API token (for integration tests)
     * This actually goes through the full registration/verification flow
     *
     * @param array $userData
     * @return array
     */
    protected function createUserWithRealToken(array $userData = []): array
    {
        $phoneNumber = $userData['phone_number'] ?? '+1555' . rand(1000000, 9999999);

        // Register user
        $this->postJson('/api/register', ['phone_number' => $phoneNumber])
             ->assertStatus(201);

        // Verify user (using test code 123456)
        $verifyResponse = $this->postJson('/api/verify', [
            'phone_number' => $phoneNumber,
            'code' => '123456'
        ])->assertStatus(200);

        $data = $verifyResponse->json();

        return [
            'user' => $data['user'],
            'token' => $data['AccessToken'],
            'headers' => [
                'Authorization' => 'Bearer ' . $data['AccessToken'],
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ]
        ];
    }
}
