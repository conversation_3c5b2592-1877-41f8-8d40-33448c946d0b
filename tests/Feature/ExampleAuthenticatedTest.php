<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\AuthenticatesUsers;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ExampleAuthenticatedTest extends TestCase
{
    use RefreshDatabase, AuthenticatesUsers;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAuthentication();
    }

    /** @test */
    public function test_authenticated_user_can_access_protected_route()
    {
        // Method 1: Using trait helper for quick authentication
        $auth = $this->authenticateUser();
        
        $response = $this->getJson('/api/user', $auth['headers']);
        $response->assertStatus(200);
    }

    /** @test */
    public function test_admin_user_can_access_admin_routes()
    {
        // Method 2: Create admin user
        $auth = $this->authenticateAdminUser();
        
        $response = $this->getJson('/api/admin/users', $auth['headers']);
        // Assert admin access
    }

    /** @test */
    public function test_authenticated_post_request()
    {
        // Method 3: Using helper methods for HTTP requests
        $data = ['name' => 'Test Data'];
        
        $response = $this->authenticatedPost('/api/some-endpoint', $data);
        $response->assertStatus(201);
    }

    /** @test */
    public function test_multiple_users_interaction()
    {
        // Method 4: Create multiple authenticated users
        $users = $this->createMultipleAuthenticatedUsers(3);
        
        $user1 = $users[0];
        $user2 = $users[1];
        
        // Test interaction between users
        $response = $this->postJson('/api/send-message', [
            'to_user_id' => $user2['user']->id,
            'message' => 'Hello!'
        ], $user1['headers']);
        
        $response->assertStatus(200);
    }

    /** @test */
    public function test_real_api_authentication_flow()
    {
        // Method 5: Using real API endpoints (integration test)
        $auth = $this->authenticateUserViaApi();
        
        $response = $this->getJson('/api/user', $auth['headers']);
        $response->assertStatus(200)
                 ->assertJsonStructure(['user' => ['id', 'phone_number']]);
    }

    /** @test */
    public function test_user_with_membership()
    {
        // Method 6: Create user with membership
        $auth = $this->authenticateUserWithMembership([
            'first_name' => 'Premium',
            'last_name' => 'User'
        ], [
            'membership_type_id' => 1
        ]);
        
        $response = $this->getJson('/api/membership', $auth['headers']);
        // Assert membership data
    }

    /** @test */
    public function test_unauthenticated_access_denied()
    {
        // Test without authentication
        $response = $this->getJson('/api/user');
        $this->assertUnauthenticated($response);
    }

    /** @test */
    public function test_custom_user_attributes()
    {
        // Create user with specific attributes
        $auth = $this->authenticateUser([
            'phone_number' => '+1234567890',
            'first_name' => 'Custom',
            'last_name' => 'User'
        ]);
        
        $this->assertEquals('+1234567890', $auth['user']->phone_number);
        $this->assertEquals('Custom', $auth['user']->first_name);
        
        $response = $this->getJson('/api/user', $auth['headers']);
        $response->assertStatus(200);
    }
}
