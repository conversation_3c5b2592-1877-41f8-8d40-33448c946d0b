<?php

namespace Tests\Feature\UserAccountCreation;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;
use Tests\Traits\AuthenticatesUsers;
use App\Models\User;

class UserAccountCreationTest extends TestCase
{
    use RefreshDatabase, AuthenticatesUsers;

    // protected function setUp(): void
    // {
    //     parent::setUp();
    //     $this->registerVerifyAndLogin();
    // }

    /** @test */
    public function test_successful_phone_number_registration()
    {
        $payload = [
            'phone_number' => '+************',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(201)
            ->assertJsonStructure(['data' => ['id', 'phone_number', 'created_at', 'updated_at']]);
    }


    /** @test */
    public function test_duplicate_phone_number_handling()
    {
        $user = User::factory()->create(['phone_number' => '+************']);
        $payload = [
            'phone_number' => '+************',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(422);
    }

    /** @test */
    public function test_invalid_phone_number_formats()
    {
        $payload = [
            'phone_number' => 'invalid-phone',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(422);
    }

    /** @test */
    public function test_sms_verification_code_process_twilio_mock()
    {
        // Simulate registration and verification in non-production (code 123456 is always accepted)
        $registerPayload = [
            'phone_number' => '+155555501040',
        ];
        $registerResponse = $this->postJson('/api/register', $registerPayload);
        $registerResponse->assertStatus(201);

        $verifyPayload = [
            'phone_number' => '+155555501040',
            'code' => '123456',
        ];
        $verifyResponse = $this->postJson('/api/verify', $verifyPayload);
        $verifyResponse->assertStatus(200)
            ->assertJsonStructure(['user']);
    }

    /** @test */
    public function test_user_creation_in_local_database_after_successful_signup()
    {
        $payload = [
            'phone_number' => '+1555555013305',
        ];
        $this->postJson('/api/register', $payload)->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'phone_number' => '+1555555013305',
        ]);
    }

   /** @test */
public function test_admin_user_creation()
{
    // Register, verify, and login using the trait
    $phoneNumber = '+2222334411';
    $password = 'MyStrongPassword123!';
    $token = $this->registerVerifyAndLogin();

    // Get the user from the database
    $user = \App\Models\User::where('phone_number', $phoneNumber)->first();
    $userId = $user->id;

    // Set password (if needed, but already set in registerVerifyAndLogin)
    // $setPasswordPayload = [
    //     'phone_number' => $phoneNumber,
    //     'password' => $password,
    // ];
    // $setPasswordResponse = $this->withHeaders([
    //     'Authorization' => 'Bearer ' . $token,
    // ])->putJson("/api/user/{$userId}", $setPasswordPayload);
    // $setPasswordResponse->assertStatus(200);

    // Promote user to admin using the login token
    $adminPayload = [
        'phone_number' => $phoneNumber,
    ];
    $adminResponse = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
    ])->postJson('/api/add-admin', $adminPayload);
    $adminResponse->assertStatus(200);

    // Verify the user is now an admin
    $this->assertDatabaseHas('users', [
        'phone_number' => $phoneNumber,
        'is_admin' => 1,
    ]);
}
}
