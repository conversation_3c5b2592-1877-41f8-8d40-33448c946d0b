<?php

namespace Tests\Feature\UserAccountCreation;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;
use App\Models\User;

class UserAccountCreationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function test_successful_phone_number_registration()
    {
        $payload = [
            'phone_number' => '+************',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(201)
            ->assertJsonStructure(['data' => ['id', 'phone_number', 'created_at', 'updated_at']]);
    }


    /** @test */
    public function test_duplicate_phone_number_handling()
    {
        $user = User::factory()->create(['phone_number' => '+************']);
        $payload = [
            'phone_number' => '+************',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(422);
    }

    /** @test */
    public function test_invalid_phone_number_formats()
    {
        $payload = [
            'phone_number' => 'invalid-phone',
        ];
        $response = $this->postJson('/api/register', $payload);
        $response->assertStatus(422);
    }

    /** @test */
    public function test_sms_verification_code_process_twilio_mock()
    {
        // Simulate registration and verification in non-production (code 123456 is always accepted)
        $registerPayload = [
            'phone_number' => '+155555501040',
        ];
        $registerResponse = $this->postJson('/api/register', $registerPayload);
        $registerResponse->assertStatus(201);

        $verifyPayload = [
            'phone_number' => '+155555501040',
            'code' => '123456',
        ];
        $verifyResponse = $this->postJson('/api/verify', $verifyPayload);
        $verifyResponse->assertStatus(200)
            ->assertJsonStructure(['user']);
    }

    /** @test */
    public function test_user_creation_in_local_database_after_successful_signup()
    {
        $payload = [
            'phone_number' => '+1555555013305',
        ];
        $this->postJson('/api/register', $payload)->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'phone_number' => '+1555555013305',
        ]);
    }

   /** @test */
public function test_admin_user_creation()
{
    // Register and verify user (get the user model)
    $registerPayload = [
        'phone_number' => '+2222334411',
    ];
    $registerResponse = $this->postJson('/api/register', $registerPayload);
    $registerResponse->assertStatus(201);

    $verifyPayload = [
        'phone_number' => '+2222334411',
        'code' => '123456',
    ];
    $verifyResponse = $this->postJson('/api/verify', $verifyPayload);
    $verifyResponse->assertStatus(200);

    // Extract the real Cognito token and user ID
    $verifyData = $verifyResponse->json();
    $token = $verifyData['AccessToken'];
    $userId = $verifyData['user']['id'];

    // Use the real Cognito token for authenticated requests
    $setPasswordPayload = [
        'phone_number' => '+2222334411',
        'password' => 'MyStrongPassword123!',
    ];

    $setPasswordResponse = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
    ])->putJson("/api/user/{$userId}", $setPasswordPayload);
    $setPasswordResponse->assertStatus(200);

    // // Now log in with the new password to get a fresh token
    // $loginResponse = $this->postJson('/api/login', [
    //     'phone_number' => '+2222334411',
    //     'password' => 'MyStrongPassword123!',
    // ]);
    // $loginResponse->assertStatus(200);
    // $loginToken = $loginResponse->json('AccessToken');

    // // Promote user to admin using the login token
    // $adminPayload = [
    //     'phone_number' => '+2222334411',
    // ];

    // $adminResponse = $this->withHeaders([
    //     'Authorization' => 'Bearer ' . $loginToken,
    // ])->postJson('/api/add-admin', $adminPayload);
    // $adminResponse->assertStatus(200);

    // // Verify the user is now an admin
    // $this->assertDatabaseHas('users', [
    //     'phone_number' => '+2222334411',
    //     'is_admin' => 1,
    // ]);
}
}
