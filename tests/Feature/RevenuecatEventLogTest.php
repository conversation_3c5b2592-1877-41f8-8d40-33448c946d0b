<?php

namespace Tests\Feature;

use App\Models\RevenuecatEventLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RevenuecatEventLogTest extends TestCase
{
    use RefreshDatabase;

    public function test_revenuecat_webhook_creates_and_processes_log()
    {
        // Create a user with proper attributes
        $user = User::factory()->create([
            'phone_number' => '88512211222227',
            'first_name' => 'Test',
            'last_name' => 'User',
            'full_name' => 'Test User'
        ]);

        $payload = [
            "event" => [
                "event_timestamp_ms" => 1752042874768,
                "product_id" => "com.videotc.vtc.annual",
                "period_type" => "NORMAL",
                "purchased_at_ms" => 1752038306000,
                "expiration_at_ms" => 1752059906000,
                "environment" => "SANDBOX",
                "entitlement_id" => null,
                "entitlement_ids" => ["com.videotc.vtc.annual"],
                "presented_offering_id" => null,
                "transaction_id" => "2000000957721715",
                "original_transaction_id" => "2000000933222263",
                "is_family_share" => false,
                "country_code" => "US",
                "app_user_id" => $user->id,
                "aliases" => ["517"],
                "original_app_user_id" => "517",
                "currency" => "USD",
                "is_trial_conversion" => false,
                "price" => 0.99,
                "price_in_purchased_currency" => 0.99,
                // "subscriber_attributes" => [
                //     "$ip" => ["value" => "**************", "updated_at_ms" => 1751974388617],
                //     "$idfa" => ["value" => "BE3CF809-5DE2-4E3F-A6B4-B7771FCD1C7A", "updated_at_ms" => 1751973692821],
                //     "$idfv" => ["value" => "FF311402-5C07-40FC-8E86-A3681CFB622A", "updated_at_ms" => 1751973692822],
                //     "$appsflyerId" => ["value" => "1751524491957-3114025", "updated_at_ms" => 1751973692824],
                //     "$deviceVersion" => ["value" => "iPhone12,1-iOS-Version 17.4.1 (Build 21E236)", "updated_at_ms" => 1751973692823],
                //     "$attConsentStatus" => ["value" => "authorized", "updated_at_ms" => 1751973641566],
                // ],
                "store" => "APP_STORE",
                "takehome_percentage" => 0.7,
                "offer_code" => null,
                "tax_percentage" => 0,
                "commission_percentage" => 0.3,
                "metadata" => null,
                "renewal_number" => 20,
                "type" => "RENEWAL",
                "id" => "3DFE0686-A0C5-46D0-9FE6-F11563D6BA94",
                "app_id" => "app52cf2e1050"
            ],
            "api_version" => "1.0"
        ];

        $response = $this->postJson('/api/revenuecat/webhook', $payload);
        $response->assertStatus(200)->assertJson(['status' => 'success']);

        $log = RevenuecatEventLog::where('external_id', '3DFE0686-A0C5-46D0-9FE6-F11563D6BA94')->first();
        $this->assertNotNull($log);
        $this->assertEquals('renewal', strtolower($log->type));
        $this->assertEquals('processed', $log->status);
        $this->assertEquals($user->id, $log->app_user_id);
        $this->assertEquals('com.videotc.vtc.annual', $log->product_id);
        $this->assertEquals('APP_STORE', $log->store);
        $this->assertEquals(0.99, $log->price);
        $this->assertEquals('NORMAL', $log->period_type);
        $this->assertIsArray($log->data);
        $this->assertEquals($payload['event']['transaction_id'], $log->data['event']['transaction_id']);
    }
}
