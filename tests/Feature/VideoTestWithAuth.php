<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\AuthenticatesUsers;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Video;

class VideoTestWithAuth extends TestCase
{
    use RefreshDatabase, AuthenticatesUsers;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAuthentication();
    }

    /** @test */
    public function test_authenticated_user_can_create_video()
    {
        // Method 1: Quick authentication
        $auth = $this->authenticateUser();

        $videoData = [
            'title' => 'Test Video',
            'description' => 'Test Description'
        ];

        $response = $this->postJson('/api/video', $videoData, $auth['headers']);
        $response->assertStatus(201);
    }

    /** @test */
    public function test_user_can_view_own_videos()
    {
        // Create user and video
        $auth = $this->authenticateUser();
        $video = Video::factory()->create(['owner_user_id' => $auth['user']->id]);

        $response = $this->getJson('/api/video', $auth['headers']);
        $response->assertStatus(200)
                 ->assertJsonFragment(['id' => $video->id]);
    }

    /** @test */
    public function test_user_cannot_access_others_private_videos()
    {
        // Create two users
        $users = $this->createMultipleAuthenticatedUsers(2);
        $user1 = $users[0];
        $user2 = $users[1];

        // User1 creates a private video
        $video = Video::factory()->create([
            'owner_user_id' => $user1['user']->id,
            'is_private' => true
        ]);

        // User2 tries to access it
        $response = $this->getJson("/api/video/{$video->id}", $user2['headers']);
        $response->assertStatus(403); // Forbidden
    }

    /** @test */
    public function test_admin_can_access_all_videos()
    {
        // Create regular user and admin
        $regularUser = $this->authenticateUser();
        $admin = $this->authenticateAdminUser();

        $video = Video::factory()->create(['owner_user_id' => $regularUser['user']->id]);

        // Admin can access any video
        $response = $this->getJson("/api/video/{$video->id}", $admin['headers']);
        $response->assertStatus(200);
    }

    /** @test */
    public function test_unauthenticated_user_cannot_create_video()
    {
        $videoData = [
            'title' => 'Test Video',
            'description' => 'Test Description'
        ];

        $response = $this->postJson('/api/video', $videoData);
        $this->assertApiUnauthenticated($response);
    }

    /** @test */
    public function test_video_upload_with_real_api_flow()
    {
        // Use real API authentication flow
        $auth = $this->authenticateUserViaApi();

        $response = $this->postJson('/api/video/create-upload-url', [
            'question_id' => 1
        ], $auth['headers']);

        $response->assertStatus(200)
                 ->assertJsonStructure(['upload_url', 'videoId']);
    }

    /** @test */
    public function test_video_home_feed_for_authenticated_user()
    {
        $auth = $this->authenticateUser();

        // Using helper method for authenticated GET
        $response = $this->authenticatedGet('/api/home-feed');
        $response->assertStatus(200)
                 ->assertJsonStructure(['data']);
    }

    /** @test */
    public function test_video_comment_functionality()
    {
        $users = $this->createMultipleAuthenticatedUsers(2);
        $videoOwner = $users[0];
        $commenter = $users[1];

        // Create video
        $video = Video::factory()->create(['owner_user_id' => $videoOwner['user']->id]);

        // Add comment
        $response = $this->postJson('/api/video-comment', [
            'video_id' => $video->id,
            'comment' => 'Great video!'
        ], $commenter['headers']);

        $response->assertStatus(201);

        // Verify comment exists
        $this->assertDatabaseHas('video_comments', [
            'video_id' => $video->id,
            'comment' => 'Great video!'
        ]);
    }

    /** @test */
    public function test_video_like_functionality()
    {
        $users = $this->createMultipleAuthenticatedUsers(2);
        $videoOwner = $users[0];
        $liker = $users[1];

        $video = Video::factory()->create(['owner_user_id' => $videoOwner['user']->id]);

        // Like video
        $response = $this->postJson('/api/video-view/like', [
            'video_id' => $video->id
        ], $liker['headers']);

        $response->assertStatus(200);
    }

    /** @test */
    public function test_custom_user_with_specific_attributes()
    {
        $auth = $this->authenticateUser([
            'phone_number' => '+8801712345678',
            'first_name' => 'Bangladesh',
            'last_name' => 'User'
        ]);

        $this->assertEquals('+8801712345678', $auth['user']->phone_number);
        $this->assertEquals('Bangladesh', $auth['user']->first_name);

        // Test video creation with this user
        $response = $this->authenticatedPost('/api/video', [
            'title' => 'Video from Bangladesh'
        ]);

        $response->assertStatus(201);
    }
}
