APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:BbJlOXubvmDAQ/Lm3OLBlG9RGrxZC5yzG0P/3Oa3d4k=
APP_DEBUG=true

DEBUGBAR_ENABLED=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

BUGSNAG_API_KEY=3cf1d707f8298556a194a81ea9d3614d




# DB_CONNECTION=mysql
# DB_HOST=db
# DB_PORT=3306
# DB_DATABASE=restaurant_pos_service2
# DB_USERNAME=restaurant_pos_service2
# DB_PASSWORD=restaurant_pos_service2

   DB_CONNECTION=mysql
   DB_HOST=testdb
   DB_PORT=3306
   DB_DATABASE=restaurant_pos_service2_test
   DB_USERNAME=restaurant_pos_service2_test
   DB_PASSWORD=restaurant_pos_service2_test
# DB_DATABASE=devapp
# DB_USERNAME=devuser
# DB_PASSWORD=s3cret

CORS_ORIGIN = https://api-dev.legacylockapp.com/api/
MUX_TOKEN_ID=************************************
MUX_TOKEN_SECRET=q9ja5jzTc5QOVTavVeljBcU7bX4Hjw439jcvPf7LK1fTjLCH4ulLaFeR7yLAbcL0VhoNY3CySio

TWILIO_SID=**********************************
TWILIO_TOKEN=3300cab49943af4c574c6453719cc64d
TWILIO_FROM_NUMBER=+17072672827
TWILIO_VERIFY_SERVICE_SID=VA04905fecdaafda0e9d0bb1bf3c0e2e1f



COURIER_BASE_URL=https://api.courier.com/send
COURIER_API_KEY=pk_test_E8SP7RG0RD46R9M3KY9N62ARW11V


AWS_COGNITO_ACCESS_KEY_ID=********************
AWS_COGNITO_SECRET_ACCESS_KEY=JnCzEYQPqbAIkOGPj0kNAXvb4JSL+w7GuIqrYesX
AWS_COGNITO_REGION=ap-northeast-1
AWS_COGNITO_APP_CLIENT_ID=360tvu6t6qc1cu8q3npt0n6g4j
AWS_COGNITO_APP_CLIENT_SECRET=jpj75id2pf24n4eljrs1t7e42e6h14ge84h57c72ge1a0osqbel
AWS_COGNITO_USER_POOL_ID=ap-northeast-1_b1pXSxOnK



# AWS_COGNITO_ACCESS_KEY_ID=********************
# AWS_COGNITO_SECRET_ACCESS_KEY=cgNCD7DmbpBJze8I7cI/Glc8fwk4aUHl0tv/Ef+y
# AWS_COGNITO_REGION=us-east-1
# AWS_COGNITO_APP_CLIENT_ID=3638pp3m3607l8dscqkf7eklkh
# # AWS_COGNITO_APP_CLIENT_SECRET=125h6gdcmvj00tvulmtt5dcnm86hv2nlsrsdipjb1ahk046h0ets
# AWS_COGNITO_USER_POOL_ID=us-east-1_XGw6PGl9Z

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=cgNCD7DmbpBJze8I7cI/Glc8fwk4aUHl0tv/Ef+y
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=vtc-profile-pic-upload
AWS_USE_PATH_STYLE_ENDPOINT=false



REVENUECAT_API_KEY=sk_QRWSAUUnlYmyjWboJmSnNBLgggUnw
REVENUECAT_WEBHOOK_SECRET="v)D;_tSjeZttTF2"
REVENUECAT_ENTITLEMENT_ID=premium
REVENUECAT_BASE_URL=https://api.revenuecat.com/v1




# AWS_COGNITO_ACCESS_KEY_ID=********************
# AWS_COGNITO_SECRET_ACCESS_KEY=q/MrmwV3m+FJWU3kQrW5RHtaUmtenH+/9LGxCTGB
# AWS_COGNITO_REGION=us-east-1
# AWS_COGNITO_APP_CLIENT_ID=56mug9nuq8v26u7pakg7tvvn70
# AWS_COGNITO_APP_CLIENT_SECRET=loso99r4p2uqh69kg33ghp37uduihe6aguc7iofd5vmjohhfdal
# AWS_COGNITO_USER_POOL_ID=us-east-1_cA7urW1b6


LOG_CHANNEL=stack


BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS_ACCESS_KEY_ID=********************
# AWS_SECRET_ACCESS_KEY=cgNCD7DmbpBJze8I7cI/Glc8fwk4aUHl0tv/Ef+y
# AWS_DEFAULT_REGION=us-east-1
# AWS_BUCKET=vtc-profile-pic-upload
# AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"



OPENAI_API_KEY = "********************************************************************************************************************************************************************"

DEEPGRAM_API_KEY="****************************************"

SLACK_REV_EVENT_WEBHOOK_URL="*********************************************************************************"

SLACK_USER_CONFIRMED_WEBHOOK_URL="*********************************************************************************"

SLACK_NOTIFICATION_WEBHOOK_URL="*********************************************************************************"

# SLACK_WEBHOOK_URL="*********************************************************************************"

SLACK_WEBHOOK_URL=*********************************************************************************

