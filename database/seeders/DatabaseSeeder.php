<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MembershipType;
use App\Models\ConnectionRequestStatus;
use App\Models\QuestionStatus;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $types = [
            ['name' => MembershipType::TRIAL_MEMBERSHIP, 'days_to_expire' => 7],
            ['name' => MembershipType::FULL_MEMBERSHIP, 'days_to_expire' => 365],
            ['name' => MembershipType::GIFT_MEMBERSHIP, 'days_to_expire' => 30],
        ];

        foreach ($types as $type) {
            MembershipType::firstOrCreate(['name' => $type['name']], $type);
        }

        $this->call([
            ConnectionRequestStatusSeeder::class,
            MembershipTypeSeeder::class,
            QuestionStatusSeeder::class,
            // Add other seeders here
        ]);
    }
}
