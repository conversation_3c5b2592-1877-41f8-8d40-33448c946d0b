<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MembershipType;

class MembershipTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            ['name' => MembershipType::TRIAL_MEMBERSHIP, 'days_to_expire' => 7],
            ['name' => MembershipType::FULL_MEMBERSHIP, 'days_to_expire' => 365],
            ['name' => MembershipType::GIFT_MEMBERSHIP, 'days_to_expire' => 30],
        ];

        foreach ($types as $type) {
            MembershipType::firstOrCreate(['name' => $type['name']], $type);
        }
    }
} 