<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;


/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $firstName = fake()->firstName();
        $lastName = fake()->lastName();

        return [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'full_name' => $firstName . ' ' . $lastName,
            'phone_number' => fake()->phoneNumber(),
            'sub' => fake()->uuid(),
            'registration_status' => 'verified',
            'birthday' => fake()->dateTimeBetween('-50 years', '-18 years'),
            'profile_pic' => null,
        ];
    }

    /**
     * Indicate that the user registration is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'registration_status' => 'unconfirmed',
        ]);
    }
}
