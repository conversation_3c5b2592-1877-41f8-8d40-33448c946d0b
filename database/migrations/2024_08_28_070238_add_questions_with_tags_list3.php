<?php

use App\Models\Question;
use App\Models\Tag;
use App\Models\TagQuestion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Array representing questions and their associated tags
        $questionsWithTags = [
            [
                'id' => 301,
                'question' => 'What do you remember most about your teenage years?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 302,
                'question' => 'Did anyone close to you pass away in your early life? What was that like for you?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 303,
                'question' => 'When and where were you born?',
                'tags' => ['Early Life'],
            ], [
                'id' => 304,
                'question' => 'What were you like as a kid?',
                'tags' => ['Early Life'],
            ], [
                'id' => 305,
                'question' => 'Tell us about a time you got in trouble growing up.',
                'tags' => ['Early Life'],
            ], [
                'id' => 306,
                'question' => 'What did you like to spend your time doing as a kid?',
                'tags' => ['Early Life'],
            ], [
                'id' => 307,
                'question' => 'What did you like to spend your time doing as a teenager?',
                'tags' => ['Early Life'],
            ], [
                'id' => 308,
                'question' => 'Did you move growing up? How was that for you?',
                'tags' => ['Early Life'],
            ], [
                'id' => 309,
                'question' => 'What is the most trouble you ever got into growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 310,
                'question' => 'Did you get into any car accidents growing up? Tell us about it.',
                'tags' => ['Early Life'],
            ], [
                'id' => 311,
                'question' => 'Tell us about your first car.',
                'tags' => ['Early Life'],
            ], [
                'id' => 312,
                'question' => 'Tell us about a famous celebrity you liked growing up.',
                'tags' => ['Early Life'],
            ], [
                'id' => 313,
                'question' => 'How did you overcome challenging times in your life?',
                'tags' => ['Reflections'],
            ], [
                'id' => 314,
                'question' => 'What is the most valuable lesson your parents or grandparents taught you?',
                'tags' => ['Reflections'],
            ], [
                'id' => 315,
                'question' => 'If you could redo something in your life, what would you do differently?',
                'tags' => ['Reflections'],
            ], [
                'id' => 316,
                'question' => 'What advice would you give to your teenage self?',
                'tags' => ['Reflections'],
            ], [
                'id' => 317,
                'question' => 'What advice would you give a teenager now?',
                'tags' => ['Reflections'],
            ], [
                'id' => 318,
                'question' => 'Tell me about a book that really impacted you as a teenager.',
                'tags' => ['Early Life'],
            ], [
                'id' => 319,
                'question' => 'Do you remember learning to read? Tell me about it.',
                'tags' => ['Early Life'],
            ], [
                'id' => 320,
                'question' => 'What kinds of books did you like to read growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 321,
                'question' => 'Tell us about someone you looked up to as a teenager.',
                'tags' => ['Early Life'],
            ], [
                'id' => 322,
                'question' => 'Tell us about the music you liked growing up.',
                'tags' => ['Early Life'],
            ], [
                'id' => 323,
                'question' => 'Tell us what dinnertime was like in your house growing up.',
                'tags' => ['Early Life'],
            ], [
                'id' => 324,
                'question' => 'Do you remember the first album you ever got? What was it?',
                'tags' => ['Early Life'],
            ], [
                'id' => 325,
                'question' => 'Tell us about a movie you loved as a teenager.',
                'tags' => ['Early Life'],
            ], [
                'id' => 326,
                'question' => 'Were you involved in any extracurricular activities in high school? Tell us about them.',
                'tags' => ['Early Life'],
            ], [
                'id' => 327,
                'question' => 'Did you play sports growing up? What was your experience with sports?',
                'tags' => ['Early Life'],
            ], [
                'id' => 328,
                'question' => 'Tell us about your first job.',
                'tags' => ['Early Life'],
            ], [
                'id' => 329,
                'question' => 'Tell us about your first crush.',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 330,
                'question' => 'How did you meet your spouse?',
                'tags' => ['Relationships'],
            ], [
                'id' => 331,
                'question' => 'Tell us about your wedding. What do you remember most about it?',
                'tags' => ['Relationships'],
            ], [
                'id' => 332,
                'question' => 'Do you remember your first date with your spouse? What do you remember most about it?',
                'tags' => ['Relationships'],
            ], [
                'id' => 333,
                'question' => 'What do you love most about your spouse?',
                'tags' => ['Relationships'],
            ], [
                'id' => 334,
                'question' => 'Who were your best friends growing up? Tell us about them.',
                'tags' => ['Early Life'],
            ], [
                'id' => 335,
                'question' => 'Did your relationship with your family change in your teenage years?',
                'tags' => ['Early Life'],
            ], [
                'id' => 336,
                'question' => 'What were your favorite TV shows growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 337,
                'question' => 'Did you compete in any competitions or receive any awards growing up? Tell us about them.',
                'tags' => ['Early Life'],
            ], [
                'id' => 338,
                'question' => 'What was the culture at your high school like?',
                'tags' => ['Early Life'],
            ], [
                'id' => 339,
                'question' => 'What did you want to be when you grew up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 340,
                'question' => 'Did you have any pets as a kid? Tell us about them.',
                'tags' => ['Early Life'],
            ], [
                'id' => 341,
                'question' => 'Tell us about the house(s) you grew up in.',
                'tags' => ['Early Life'],
            ], [
                'id' => 342,
                'question' => 'What holidays did your family celebrate together? What traditions did you have?',
                'tags' => ['Early Life'],
            ], [
                'id' => 343,
                'question' => 'Who did you spend the most time with as a kid?',
                'tags' => ['Early Life'],
            ], [
                'id' => 344,
                'question' => 'How did your family spend time together when you were growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 345,
                'question' => 'What did your parents do for work? Did they talk about it with you? What would they say?',
                'tags' => ['Early Life'],
            ], [
                'id' => 346,
                'question' => 'What were your grandparents like?',
                'tags' => ['Early Life'],
            ], [
                'id' => 347,
                'question' => 'How did your parents meet?',
                'tags' => ['Early Life'],
            ], [
                'id' => 348,
                'question' => 'Where are your parents from?',
                'tags' => ['Early Life'],
            ], [
                'id' => 349,
                'question' => 'What were/are your parents like?',
                'tags' => ['Early Life'],
            ], [
                'id' => 350,
                'question' => 'How did you get your name?',
                'tags' => ['Early Life'],
            ], [
                'id' => 351,
                'question' => 'What were your favorite and least favorite subjects in school?',
                'tags' => ['Early Life'],
            ], [
                'id' => 352,
                'question' => 'Tell us about a memorable teacher or coach you had.',
                'tags' => ['Early Life'],
            ], [
                'id' => 353,
                'question' => 'Where did you attend high school?',
                'tags' => ['Early Life'],
            ], [
                'id' => 354,
                'question' => 'Did you ever get seriously injured or sick growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 355,
                'question' => 'Are you still in touch with any of your childhood friends?',
                'tags' => ['Early Life'],
            ], [
                'id' => 356,
                'question' => 'Did you know your neighbors growing up? What were your relationships like with them?',
                'tags' => ['Early Life'],
            ], [
                'id' => 357,
                'question' => 'What were your favorite childhood games or toys?',
                'tags' => ['Early Life'],
            ], [
                'id' => 358,
                'question' => 'Tell us about your earliest memory that you can remember.',
                'tags' => ['Early Life'],
            ], [
                'id' => 359,
                'question' => 'Where did you go to elementary school? What do you remember most about it?',
                'tags' => ['Early Life'],
            ], [
                'id' => 360,
                'question' => 'What was your relationship like with your extended family growing up? What did you do together?',
                'tags' => ['Early Life'],
            ], [
                'id' => 361,
                'question' => 'What was your relationship like with your sibling(s) growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 362,
                'question' => 'What were your children’s favorite things to do in the summer?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 363,
                'question' => 'What is one of the funniest things your children did when they were little?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 364,
                'question' => 'What did your kids spend time doing together when they were young?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 365,
                'question' => 'Tell us about a time when your kids got in trouble.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 366,
                'question' => 'What do you remember most about your children when they were little?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 367,
                'question' => 'Did anyone close to you pass away in your mid life? What was that like for you?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 368,
                'question' => 'What aspects of being a parent challenged you the most?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 369,
                'question' => 'What was your favorite part about being a parent when your kids were growing up?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 370,
                'question' => 'What did you do after high school?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 371,
                'question' => 'What did you study in college? How did you choose that?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 372,
                'question' => 'Tell me about a professor or mentor who impacted your trajectory.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 373,
                'question' => 'How did you pay for college?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 374,
                'question' => 'Did you serve in the military or have loved ones who served? Tell us about it.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 375,
                'question' => 'Tell us about moving in with your spouse. What was it like when you first lived together?',
                'tags' => ['Relationships'],
            ], [
                'id' => 376,
                'question' => 'What did you do after college?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 377,
                'question' => 'How did you spend your time in your twenties?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 378,
                'question' => 'How did you spend your time in college?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 379,
                'question' => 'Did you date in your twenties? What was it like for you?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 380,
                'question' => 'What was your spouse like when you first met? What attracted you to them?',
                'tags' => ['Relationships'],
            ], [
                'id' => 381,
                'question' => 'Tell us about your marriage proposal.',
                'tags' => ['Relationships'],
            ], [
                'id' => 382,
                'question' => 'What was your dating life like before meeting your significant other?',
                'tags' => ['Relationships'],
            ], [
                'id' => 383,
                'question' => 'What was your family’s attitude towards your marriage?',
                'tags' => ['Relationships'],
            ], [
                'id' => 384,
                'question' => 'How old were you and your spouse when you got married?',
                'tags' => ['Relationships'],
            ], [
                'id' => 385,
                'question' => 'What advice would you give to newly weds?',
                'tags' => ['Reflections'],
            ], [
                'id' => 386,
                'question' => 'Tell us about your honeymoon. What do you remember most from it?',
                'tags' => ['Relationships'],
            ], [
                'id' => 387,
                'question' => 'What were your first years of marriage like?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 388,
                'question' => 'How many children do you have?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 389,
                'question' => 'How old were you when you had your first child? Did you feel ready?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 390,
                'question' => 'What was it like to learn you were going to have a baby?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 391,
                'question' => 'What was pregnancy like for you or your spouse?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 392,
                'question' => 'Tell us about your children’s births. What do you remember most about them?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 393,
                'question' => 'Tell us how you picked your children’s names and where they came from.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 394,
                'question' => 'Tell us about each of your children.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 395,
                'question' => 'What holiday traditions did you have when your kids were young? How did those traditions emerge?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 396,
                'question' => 'Tell us about a memorable family vacation with your children.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 397,
                'question' => 'Tell us one of your favorite memories with each of your children.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 398,
                'question' => 'What were your kids like as teenagers?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 399,
                'question' => 'Tell us about a moment when your kids made you proud.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 400,
                'question' => 'What was it like when your kids left the nest?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 401,
                'question' => 'Tell us about starting out on your professional path. How did you choose it and what was it like?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 402,
                'question' => 'Tell us about a professional accomplishment that you are proud of?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 403,
                'question' => 'What did you do for fun in your twenties?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 404,
                'question' => 'Did you have any hobbies in your twenties?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 405,
                'question' => 'What communities were you a part of in your twenties? Were you part of any groups or clubs?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 406,
                'question' => 'What role, if any, has religion or spirituality played in your life?',
                'tags' => ['Reflections'],
            ], [
                'id' => 407,
                'question' => 'What advice would you give to young parents?',
                'tags' => ['Reflections'],
            ], [
                'id' => 408,
                'question' => 'What are some of the most important things you’ve learned in life?',
                'tags' => ['Reflections'],
            ], [
                'id' => 409,
                'question' => 'What qualities do you value the most in your friends?',
                'tags' => ['Reflections'],
            ], [
                'id' => 410,
                'question' => 'What do you value the most in your life?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 411,
                'question' => 'What is something important that you learned from each of your parents?',
                'tags' => ['Reflections'],
            ], [
                'id' => 412,
                'question' => 'In what ways are you like or different from each of your parents?',
                'tags' => ['Reflections'],
            ], [
                'id' => 413,
                'question' => 'Tell us about someone you admire and take inspiration from.',
                'tags' => ['Reflections'],
            ], [
                'id' => 414,
                'question' => 'Tell us something that you’ve changed your mind about over the years.',
                'tags' => ['Reflections'],
            ], [
                'id' => 415,
                'question' => 'What is the most difficult thing you’ve ever done or been through?',
                'tags' => ['Reflections'],
            ], [
                'id' => 416,
                'question' => 'What surprised you most about parenthood?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 417,
                'question' => 'Tell us about any children you lost. How did you grieve and continue on?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 418,
                'question' => 'Tell us what it was like to become an empty nester.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 419,
                'question' => 'Tell us about a project you worked on in your twenties.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 420,
                'question' => 'What life advice do you have for me or someone my age?',
                'tags' => ['Reflections'],
            ], [
                'id' => 421,
                'question' => 'How do you hope to be remembered?',
                'tags' => ['Reflections'],
            ], [
                'id' => 422,
                'question' => 'What are some mottos or sayings you live by?',
                'tags' => ['Reflections'],
            ], [
                'id' => 423,
                'question' => 'What is a dream you’ve let go of?',
                'tags' => ['Reflections'],
            ], [
                'id' => 424,
                'question' => 'What are some things on your bucket list that you’ve yet to cross off?',
                'tags' => ['Reflections'],
            ], [
                'id' => 425,
                'question' => 'If your house caught fire, what treasured possessions would be hardest to lose?',
                'tags' => ['Reflections'],
            ], [
                'id' => 426,
                'question' => 'What is something in your life that brings you joy?',
                'tags' => ['Reflections'],
            ], [
                'id' => 427,
                'question' => 'Do you have any advice on choosing a career?',
                'tags' => ['Reflections'],
            ], [
                'id' => 428,
                'question' => 'If you could travel anywhere in the world, where would you go and why?',
                'tags' => ['Reflections'],
            ], [
                'id' => 429,
                'question' => 'If you won a million dollars, what would you do with it?',
                'tags' => ['Reflections'],
            ], [
                'id' => 430,
                'question' => 'If you could relive one part of your life, what part would it be and why?',
                'tags' => ['Reflections'],
            ], [
                'id' => 431,
                'question' => 'How is today’s society different from when you were a child?',
                'tags' => ['Reflections'],
            ], [
                'id' => 432,
                'question' => 'Tell us about a memory that embarrasses you.',
                'tags' => ['Reflections'],
            ], [
                'id' => 433,
                'question' => 'Tell us about a memory that you cherish.',
                'tags' => ['Reflections'],
            ], [
                'id' => 434,
                'question' => 'Tell us about a memorable gift you’ve received.',
                'tags' => ['Reflections'],
            ], [
                'id' => 435,
                'question' => 'Tell us about a time when you were brave.',
                'tags' => ['Reflections'],
            ], [
                'id' => 436,
                'question' => 'What is the scariest thing that has ever happened to you?',
                'tags' => ['Reflections'],
            ], [
                'id' => 437,
                'question' => 'Tell us about something that makes you feel excited.',
                'tags' => ['Reflections'],
            ], [
                'id' => 438,
                'question' => 'What is something you like about my generation?',
                'tags' => ['Reflections'],
            ], [
                'id' => 439,
                'question' => 'What are some of your strongest held beliefs?',
                'tags' => ['Reflections'],
            ], [
                'id' => 440,
                'question' => 'How do you hope to be described at your funeral?',
                'tags' => ['Reflections'],
            ], [
                'id' => 441,
                'question' => 'Tell us about your graduate studies and why you chose to pursue an advanced degree.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 442,
                'question' => 'Tell us about a memorable boss you had.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 443,
                'question' => 'Tell us about any jobs that you quit, and why.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 444,
                'question' => 'Tell us about a time(s) you lost your job? What did you do?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 445,
                'question' => 'What was your professional path? How did you choose it?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 446,
                'question' => 'Tell us about the first house you bought.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 447,
                'question' => 'How have your relationships with your children changed over the years?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 448,
                'question' => 'Tell me about your transition to retirement. How was it for you?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 449,
                'question' => 'What do you know about where and when your grandparents were born?',
                'tags' => ['Family'],
            ], [
                'id' => 450,
                'question' => 'Do you know any interesting stories about your grandparents?',
                'tags' => ['Family'],
            ], [
                'id' => 451,
                'question' => 'What do you know about your grandparents’ childhood and upbringing?',
                'tags' => ['Family'],
            ], [
                'id' => 452,
                'question' => 'How did your parents meet?',
                'tags' => ['Family'],
            ], [
                'id' => 453,
                'question' => 'What do you know about your parents’ education?',
                'tags' => ['Family'],
            ], [
                'id' => 454,
                'question' => 'What do you know about your grandparents’ education?',
                'tags' => ['Family'],
            ], [
                'id' => 455,
                'question' => 'Are your grandparents living? If not, what do you recall about their death?',
                'tags' => ['Family'],
            ], [
                'id' => 456,
                'question' => 'Tell me about something you learned from your grandparents.',
                'tags' => ['Family'],
            ], [
                'id' => 457,
                'question' => 'Where were your parents born and raised?',
                'tags' => ['Family'],
            ], [
                'id' => 458,
                'question' => 'What do you know about what your parents’ families were like when they were growing up?',
                'tags' => ['Family'],
            ], [
                'id' => 459,
                'question' => 'Tell us something that is unique about each of your parents.',
                'tags' => ['Family'],
            ], [
                'id' => 460,
                'question' => 'What were you parents’ parenting styles?',
                'tags' => ['Early Life'],
            ], [
                'id' => 461,
                'question' => 'If your parents divorced or separated, how did that affect you and your family?',
                'tags' => ['Early Life'],
            ], [
                'id' => 462,
                'question' => 'How did you spend your summers growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 463,
                'question' => 'What are some of your happiest memories as a child?',
                'tags' => ['Early Life'],
            ], [
                'id' => 464,
                'question' => 'Did you have a nickname growing up?',
                'tags' => ['Early Life'],
            ], [
                'id' => 465,
                'question' => 'Do you know why your parents chose your name?',
                'tags' => ['Early Life'],
            ], [
                'id' => 466,
                'question' => 'How did your college years compare to high school?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 467,
                'question' => 'What did you and your friends do after school?',
                'tags' => ['Early Life'],
            ], [
                'id' => 468,
                'question' => 'Tell us something your family would find surprising about you.',
                'tags' => ['Reflections'],
            ], [
                'id' => 469,
                'question' => 'Who did you live with in college? Tell us about them.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 470,
                'question' => 'Who were your best friends in college and where are they now?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 471,
                'question' => 'Have you ever been arrested? Tell us about it.',
                'tags' => ['Early Life'],
            ], [
                'id' => 472,
                'question' => 'Were you in any clubs or groups in high school or college?',
                'tags' => ['Early Life'],
            ], [
                'id' => 473,
                'question' => 'Tell us something your family doesn’t know about things you did in college.',
                'tags' => ['Adulthood'],
            ], [
                'id' => 474,
                'question' => 'Were you politically active in any way in high school or college?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 475,
                'question' => 'Did your parents have any other marriages or families?',
                'tags' => ['Family'],
            ], [
                'id' => 476,
                'question' => 'Tell me about a time when your parents were proud of you.',
                'tags' => ['Family'],
            ], [
                'id' => 477,
                'question' => 'Tell me about a time when your parents were disappointed in you.',
                'tags' => ['Family'],
            ], [
                'id' => 478,
                'question' => 'Tell me something unique about each of your children.',
                'tags' => ['Family'],
            ], [
                'id' => 479,
                'question' => 'Did you have boyfriends or girlfriends growing up? Tell us about them.',
                'tags' => ['Early Life'],
            ], [
                'id' => 480,
                'question' => 'Tell us about your first kiss.',
                'tags' => ['Early Life'],
            ], [
                'id' => 481,
                'question' => 'Tell us about your first love.',
                'tags' => ['Early Life'],
            ], [
                'id' => 482,
                'question' => 'Who were your most serious relationships with and what do you remember about them?',
                'tags' => ['Relationships'],
            ], [
                'id' => 483,
                'question' => 'Tell us about what you remember of your courtship with your spouse.',
                'tags' => ['Relationships'],
            ], [
                'id' => 484,
                'question' => 'What was challenging about the first years of marriage, and how did you get through it?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 485,
                'question' => 'Tell us about the hardest years of your marriage. How did you overcome them?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 486,
                'question' => 'How did your marriage change after becoming parents?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 487,
                'question' => 'Tell us about your divorce or separation with a partner. What happened? How did that affect you or your family?',
                'tags' => ['Adulthood'],
            ], [
                'id' => 488,
                'question' => 'What is something endearing about your spouse?',
                'tags' => ['Relationships'],
            ], [
                'id' => 489,
                'question' => 'What do you believe is the key to a successful marriage?',
                'tags' => ['Relationships'],
            ], [
                'id' => 490,
                'question' => 'When dating, what areas of life should you discuss and be on the same page about before marrying?',
                'tags' => ['Relationships'],
            ], [
                'id' => 491,
                'question' => 'What do you believe is the key to being a great parent?',
                'tags' => ['Reflections'],
            ], [
                'id' => 492,
                'question' => 'What wisdom do you want to pass to your children and grandchildren?',
                'tags' => ['Reflections'],
            ], [
                'id' => 493,
                'question' => 'What is the secret to a happy and successful life?',
                'tags' => ['Reflections'],
            ], [
                'id' => 494,
                'question' => 'What do you wish for your family in the future?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 495,
                'question' => 'What role has God played in your life?',
                'tags' => ['Reflections'],
            ], [
                'id' => 496,
                'question' => 'Tell us about a time when your faith was tested. What happened? How did you get through it?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 497,
                'question' => 'What was your best date and worst date that you remember?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 498,
                'question' => 'Tell us about the places you traveled as a child.',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 499,
                'question' => 'Tell me about your first girlfriend or boyfriend.',
                'tags' => ['Early Life', 'Relationships'],
            ],
        ];

        foreach ($questionsWithTags as $data) {

            $question = Question::forceCreate(['id' => $data['id'], 'question_text' => $data['question']]);

            foreach ($data['tags'] as $tagTitle) {
                // Find or create the tag instance
                $tag = Tag::firstOrCreate(['title' => $tagTitle]);

                // Associate the question with the tag
                TagQuestion::firstOrCreate([
                    'question_id' => $question->id,
                    'tag_id' => $tag->id,
                ]);
            }
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::disableForeignKeyConstraints();

        TagQuestion::truncate();
        Question::truncate();
        Tag::truncate();

        Schema::enableForeignKeyConstraints();

    }
};
