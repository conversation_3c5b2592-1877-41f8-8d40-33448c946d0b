<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paywall_activity_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default activity types (only name and description)
        $types = [
            ['name' => 'purchase', 'description' => 'Membership purchase'],
            ['name' => 'renewal', 'description' => 'Membership renewal'],
            ['name' => 'cancellation', 'description' => 'Membership cancellation'],
            ['name' => 'gift_sent', 'description' => 'Membership gift sent to another user'],
            ['name' => 'gift_received', 'description' => 'Membership gift received from another user'],
            ['name' => 'gift_rejected', 'description' => 'Membership gift rejected by another user'],
            ['name' => 'gift_accepted', 'description' => 'Membership gift accepted by another user'],
            ['name' => 'gift_cancelled', 'description' => 'Membership gift cancelled by another user'],
            ['name' => 'trial_started', 'description' => 'Free trial period started'],
            ['name' => 'trial_ended', 'description' => 'Free trial period ended'],
            ['name' => 'gift_count_purchase', 'description' => 'Purchase of additional gift counts'],
            ['name' => 'payment_failed', 'description' => 'Payment attempt failed'],
            ['name' => 'refund', 'description' => 'Payment refunded'],
        ];

        foreach ($types as $type) {
            DB::table('paywall_activity_types')->insert([
                'name' => $type['name'],
                'description' => $type['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paywall_activity_types');
    }
}; 