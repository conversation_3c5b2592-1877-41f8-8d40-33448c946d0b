<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Step 1: Delete all data from the `tag_questions` table
        DB::table('tag_questions')->truncate();

        // Step 2: Ensure the 'custom' tag exists and get its ID
        $customTag = DB::table('tags')->where('title', 'custom')->first();

        if (!$customTag) {
            // Insert the 'custom' tag and retrieve the ID
            $customTagId = DB::table('tags')->insertGetId([
                'title' => 'custom',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $customTagId = $customTag->id;
        }

        // Step 3: Update `question_type` to 'custom' for all referenced questions
        $updatedQuestionIds = DB::table('questions')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('question_sends')
                    ->whereColumn('questions.id', 'question_sends.question_id');
            })
            ->pluck('id'); // Collect IDs of referenced questions

        DB::table('questions')->whereIn('id', $updatedQuestionIds)->update(['question_type' => 'custom']);

        // Step 4: Add a row in `tag_questions` for every updated question
        foreach ($updatedQuestionIds as $questionId) {
            DB::table('tag_questions')->insert([
                'question_id' => $questionId,
                'tag_id' => $customTagId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Step 5: Delete questions not referenced in the `question_sends` table
        DB::table('questions')->whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('question_sends')
                ->whereColumn('questions.id', 'question_sends.question_id');
        })->delete();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Reverse logic if needed
    }
};
