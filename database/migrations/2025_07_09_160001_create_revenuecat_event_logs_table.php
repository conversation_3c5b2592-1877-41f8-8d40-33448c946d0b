<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('revenuecat_event_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type')->nullable();
            $table->string('external_id')->unique()->nullable();
            $table->string('status')->default('pending');
            $table->foreignId('app_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('product_id')->nullable();
            $table->string('store')->nullable();
            $table->float('price')->nullable();
            $table->string('period_type')->nullable();
            $table->json('data'); // raw JSON
            $table->timestamps();

            $table->index('external_id');
            $table->index('app_user_id');
            $table->index('type');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('revenuecat_event_logs');
    }
}; 