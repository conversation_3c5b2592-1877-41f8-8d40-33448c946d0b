<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('relationships', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('from_user_id');
            $table->unsignedBigInteger('to_user_id')->nullable();
            $table->unsignedBigInteger('reciprocal_relationship_id')->nullable();
            $table->unsignedBigInteger('to_invitation_id')->nullable();
            $table->boolean('is_hidden')->default(0);
            $table->string('name_as_from');
            $table->string('name_as_outsider');
            $table->timestamps();

            $table->foreign('from_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('to_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reciprocal_relationship_id')->references('id')->on('relationships')->onDelete('set null');
            $table->foreign('to_invitation_id')->references('id')->on('invitations')->onDelete('set null');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('relationships');
    }
};
