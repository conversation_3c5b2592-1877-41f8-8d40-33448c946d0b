<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('question_sends', function (Blueprint $table) {

            $table->unsignedBigInteger('invitation_id')->nullable()->after('video_id');
            $table->foreign('invitation_id')->references('id')->on('invitations')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('question_sends', function (Blueprint $table) {

            $table->dropForeign(['invitation_id']);
            $table->dropColumn('invitation_id');
        });
    }
};
