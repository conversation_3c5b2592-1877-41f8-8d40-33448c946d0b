<?php

use App\Models\Question;
use App\Models\QuestionActivity;
use App\Models\QuestionSend;
use App\Models\TagQuestion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (env('APP_ENV') === 'production') {
            Schema::disableForeignKeyConstraints();

            QuestionActivity::truncate();

            QuestionSend::truncate();

            TagQuestion::truncate();

            Question::truncate();

            Schema::enableForeignKeyConstraints();
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No need to reverse the deletion of records
    }
};
