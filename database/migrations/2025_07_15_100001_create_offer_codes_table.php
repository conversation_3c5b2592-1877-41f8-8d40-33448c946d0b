<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\OfferCode;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->boolean('is_auto_renew')->default(false);
            $table->string('title')->nullable();
            $table->decimal('regular_rate', 8, 2)->nullable();
            $table->string('revenue_cat_product_id')->nullable();
            $table->integer('duration_num')->nullable()->default(1);
            $table->enum('duration_units', OfferCode::DURATION_UNITS)->nullable()->default(OfferCode::DURATION_UNIT_MONTH);
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_codes');
    }
};
