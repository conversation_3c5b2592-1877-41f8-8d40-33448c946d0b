<?php

use App\Models\Question;
use App\Models\Tag;
use App\Models\TagQuestion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Array representing questions and their associated tags
        $questionsWithTags = [
            ['id' => 201, 'question' => 'Tell us about your grandparents and what you know about them?', 'tags' => ['Family']],
            ['id' => 202, 'question' => 'Tell us about your parents, how they met, and their courtship.', 'tags' => ['Family']],
            ['id' => 203, 'question' => 'What do you know about your parents’ childhood?', 'tags' => ['Family']],
            ['id' => 204, 'question' => 'What do you know about your grandparents’ early life?', 'tags' => ['Family']],
            ['id' => 205, 'question' => 'What work did your2 parents do during their adult lives?', 'tags' => ['Family']],
            ['id' => 206, 'question' => 'What work did your2 grandparents do during their adult lives?', 'tags' => ['Family']],
            ['id' => 207, 'question' => 'Tell us any interesting stories about your grandparents.', 'tags' => ['Family']],
            ['id' => 208, 'question' => 'Tell us any interesting stories about your parents.', 'tags' => ['Family']],
            ['id' => 209, 'question' => 'Tell us about your aunts/uncles or great aunts/uncles.', 'tags' => ['Family']],
            ['id' => 210, 'question' => 'Tell us about your siblings and how you spent time growing up.', 'tags' => ['Family']],
            ['id' => 211, 'question' => 'Did your2 family take any fun vacations, tell us about them.', 'tags' => ['Family']],
            ['id' => 212, 'question' => 'What do you know about your families immigration- tell us about it.', 'tags' => ['Family']],
            ['id' => 213, 'question' => 'Can you share some of your favorite childhood memories?', 'tags' => ['Early Life']],
            ['id' => 214, 'question' => 'What do you remember about grade school and junior high?', 'tags' => ['Early Life']],
            ['id' => 215, 'question' => 'What best friends did you 2have and how did you 2spend time together?', 'tags' => ['Early Life']],
            ['id' => 216, 'question' => 'Who was your first girlfriend or boyfriend- tell us about them.', 'tags' => ['Early Life', 'Relationships']],
            ['id' => 217, 'question' => 'What memories do you have of junior high and high school?', 'tags' => ['Early Life']],
            ['id' => 218, 'question' => 'What things did you 2and your friends do outside of 2school?', 'tags' => ['Early Life']],
            ['id' => 219, 'question' => 'What were some of your favorite things to listen to or watch on television as a child?', 'tags' => ['Early Life']],
            ['id' => 220, 'question' => 'Did you 2ever get in big trouble from your parents- tell us about it.', 'tags' => ['Early Life']],
            ['id' => 221, 'question' => 'What was your first job, tell us about it.', 'tags' => ['Early Life']],
            ['id' => 222, 'question' => 'After high school what did you 2do and how did you 2make that decision?', 'tags' => ['Early Life']],
            ['id' => 223, 'question' => 'Tell us about your college years and any memories.', 'tags' => ['Early Life']],
            ['id' => 224, 'question' => 'What did you 2study and did you 2change your major/course?', 'tags' => ['Early Life']],
            ['id' => 225, 'question' => 'What were some fun memories you had during college?', 'tags' => ['Early Life']],
            ['id' => 226, 'question' => 'Tell me about friends you had in High School or after High School.', 'tags' => ['Early Life']],
            ['id' => 227, 'question' => 'What did you 2do during your summers?', 'tags' => ['Early Life']],
            ['id' => 228, 'question' => 'What were some of your first jobs after you finished your education?', 'tags' => ['Adulthood']],
            ['id' => 229, 'question' => 'What were the jobs you liked or disliked and why?', 'tags' => ['Adulthood']],
            ['id' => 230, 'question' => 'Did you 2have any bosses or supervisors that you remember and why?', 'tags' => ['Adulthood']],
            ['id' => 231, 'question' => 'What do you remember about the first time you lived on your own?', 'tags' => ['Adulthood']],
            ['id' => 232, 'question' => 'Did you 2ever live with roommates, tell us about them.', 'tags' => ['Adulthood']],
            ['id' => 233, 'question' => 'What kind of hobbies did you 2have as an adult?', 'tags' => ['Adulthood']],
            ['id' => 234, 'question' => 'Tell us about family vacations of significance.', 'tags' => ['Adulthood']],
            ['id' => 235, 'question' => 'Tell us about any favorite activities with your spouse and family.', 'tags' => ['Adulthood']],
            ['id' => 236, 'question' => 'Tell us about any difficult times you had as an adult.', 'tags' => ['Adulthood']],
            ['id' => 237, 'question' => 'Did you 2ever serve in the armed forces, if so tell us about your service.', 'tags' => ['Adulthood']],
            ['id' => 238, 'question' => 'Where did you 2serve and what do you remember about it?', 'tags' => ['Adulthood']],
            ['id' => 239, 'question' => 'Tell us about your family’s immigration.', 'tags' => ['Adulthood']],
            ['id' => 240, 'question' => 'Why did your2 family immigrate?', 'tags' => ['Adulthood']],
            ['id' => 241, 'question' => 'Do you have family that lives in another country, tell us about them.', 'tags' => ['Adulthood']],
            ['id' => 242, 'question' => 'Tell us about your siblings and your relationship with them as adults.', 'tags' => ['Adulthood']],
            ['id' => 243, 'question' => 'Tell us about any foods you like associated with your ethnicity.', 'tags' => ['Adulthood']],
            ['id' => 244, 'question' => 'Tell us about your boyfriends/girlfriends during school.', 'tags' => ['Relationships']],
            ['id' => 245, 'question' => 'Who was your first boyfriend/girlfriend and what do you remember about dating them?', 'tags' => ['Relationships']],
            ['id' => 246, 'question' => 'Tell us about any hard breakups.', 'tags' => ['Relationships']],
            ['id' => 247, 'question' => 'Tell us how you met your spouse and what you remember about it.', 'tags' => ['Relationships']],
            ['id' => 248, 'question' => 'What was the first date you took with your spouse?', 'tags' => ['Relationships']],
            ['id' => 249, 'question' => 'Tell us about the proposal to be married and how you felt.', 'tags' => ['Relationships']],
            ['id' => 250, 'question' => 'What things did you 2and your spouse do for fun during time you dated?', 'tags' => ['Relationships']],
            ['id' => 251, 'question' => 'Tell us about your wedding, the planning and celebration.', 'tags' => ['Relationships']],
            ['id' => 252, 'question' => 'Tell us about your honeymoon and all the details.', 'tags' => ['Relationships']],
            ['id' => 253, 'question' => 'What do you remember about when you first lived with your spouse?', 'tags' => ['Relationships']],
            ['id' => 254, 'question' => 'What are some of the favorite times you had with your spouse?', 'tags' => ['Relationships']],
            ['id' => 255, 'question' => 'Tell us about what you love/loved about your spouse.', 'tags' => ['Relationships']],
            ['id' => 256, 'question' => 'Tell us about your journey of faith and religion.', 'tags' => ['Reflections']],
            ['id' => 257, 'question' => 'What would you tell your children and grandchildren about your faith and about God?', 'tags' => ['Reflections']],
            ['id' => 258, 'question' => 'Tell us about going to Church through the years.', 'tags' => ['Reflections']],
            ['id' => 259, 'question' => 'What advice would you give your younger self or other young people?', 'tags' => ['Early Life', 'Reflections']],
            ['id' => 260, 'question' => 'What are valuable lessons your parents or grandparents taught you?', 'tags' => ['Reflections']],
            ['id' => 261, 'question' => 'What were difficult times in your life and how did you 2get through them?', 'tags' => ['Reflections']],
            ['id' => 262, 'question' => 'What do you believe is the key to a successful marriage?', 'tags' => ['Reflections', 'Relationships']],
            ['id' => 263, 'question' => 'When you are gone how do you want to be remembered?', 'tags' => ['Reflections']],
            ['id' => 264, 'question' => 'When you are gone what would you want to tell your family?', 'tags' => ['Reflections']],
            ['id' => 265, 'question' => 'What wisdom do you want to pass to your children or grandchildren?', 'tags' => ['Reflections']],
            ['id' => 266, 'question' => 'What do you think is a secret to a happy and successful life?', 'tags' => ['Reflections']],
            ['id' => 267, 'question' => 'Tell us about significant events from your last year.', 'tags' => ['Reflections']],
            ['id' => 268, 'question' => 'Tell us what you hope to accomplish during the next year.', 'tags' => ['Reflections']],
            ['id' => 269, 'question' => 'Are there any things you would have done differently in your life that stand out?', 'tags' => ['Reflections']],
            ['id' => 270, 'question' => 'Are there any significant world events that affected you, explain.', 'tags' => ['Reflections']],
        ];

        foreach ($questionsWithTags as $data) {

            $question = Question::forceCreate(['id' => $data['id'], 'question_text' => $data['question']]);

            foreach ($data['tags'] as $tagTitle) {
                // Find or create the tag instance
                $tag = Tag::firstOrCreate(['title' => $tagTitle]);

                // Associate the question with the tag
                TagQuestion::firstOrCreate([
                    'question_id' => $question->id,
                    'tag_id' => $tag->id,
                ]);
            }
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        TagQuestion::truncate();
        Question::truncate();
        Tag::truncate();
        Schema::enableForeignKeyConstraints();
    }
};
