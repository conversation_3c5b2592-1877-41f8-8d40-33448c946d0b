<?php

use App\Models\Question;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->unsignedBigInteger('to_user_id')->nullable()->after('creator__user_id');
            $table->foreign('to_user_id')->references('id')->on('users')->onDelete('set null');

            $table->string('question_type')->default(Question::PUBLIC_QUESTION_TYPE)->after('to_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropForeign(['to_user_id']);
            $table->dropColumn('to_user_id');
            $table->dropColumn('question_type');
        });
    }
};
