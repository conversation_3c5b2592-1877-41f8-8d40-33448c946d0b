<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\OfferCode;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert preliminary offer codes
        DB::table('offer_codes')->insert([
            [
                'code' => 'OURLEGACY',
                'is_auto_renew' => false,
                'title' => 'Friends and Family',
                'regular_rate' => null,
                'revenue_cat_product_id' => null,
                'duration_num' => 1,
                'duration_units' => OfferCode::DURATION_UNIT_MONTH,
                'expires_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '1WEEK25',
                'is_auto_renew' => false,
                'title' => 'User Testing',
                'regular_rate' => null,
                'revenue_cat_product_id' => null,
                'duration_num' => 1,
                'duration_units' => OfferCode::DURATION_UNIT_WEEK,
                'expires_at' => '2025-12-31 23:59:59',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'SUMMER25',
                'is_auto_renew' => true,
                'title' => "You're about to unlock summer savings!",
                'regular_rate' => 49.99,
                'revenue_cat_product_id' => null,
                'duration_num' => 2,
                'duration_units' => OfferCode::DURATION_UNIT_MONTH,
                'expires_at' => '2025-09-22 23:59:59',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('offer_codes')->whereIn('code', [
            'OURLEGACY',
            '1WEEK25', 
            'SUMMER25'
        ])->delete();
    }
};
