<?php

use App\Models\Question;
use App\Models\Tag;
use App\Models\TagQuestion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        DB::table('questions')->whereExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('question_sends')
                ->whereColumn('questions.id', 'question_sends.question_id');
        })->update([
            'question_type' => Question::CUSTOM_QUESTION_TYPE
        ]);

        DB::table('questions')->whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('question_sends')
                ->whereColumn('questions.id', 'question_sends.question_id');
        })->delete();

        $questionsData = [
            [
                'question' => 'Where were you born and raised? Where do you consider yourself to be from?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'Where were your parents born and raised? What do you know about their cultural roots?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'Where did your family originally come from, and what are their cultural roots?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'Are there any traditions or customs in your family tied to your heritage?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'What languages or dialects have been spoken in your family, and how have they influenced your life?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'What do you know about where your ancestors are from?',
                'tags' => ['Family Heritage & Origins'],
            ],
            [
                'question' => 'What do you know about any remaining family in your country of origin?',
                'tags' => ['Family Heritage & Origins'],
            ],

            [
                'question' => 'Can you tell me about what you remember most about your journey immigrating to this country?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'Do you know the story of why and how our family decided to immigrate?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'What was life like in our homeland before the move?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'Are there any places or landmarks that hold special meaning in our family\'s immigration journey?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'How did our family adapt or change after arriving in a new region?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'Did our family face any challenges when adjusting to life in a new region?',
                'tags' => ['Immigration & Family Journeys'],
            ],
            [
                'question' => 'If your ancestors could see your life today, what do you think they would say?',
                'tags' => ['Immigration & Family Journeys'],
            ],

            [
                'question' => 'What memories or stories do you have about your grandparents or great-grandparents?',
                'tags' => ['Grandparents’ & Ancestors’ Lives'],
            ],
            [
                'question' => 'Where did your grandparents grow up?',
                'tags' => ['Grandparents’ & Ancestors’ Lives'],
            ],
            [
                'question' => 'What did your grandparents do for occupations/employment?',
                'tags' => ['Grandparents’ & Ancestors’ Lives'],
            ],
            [
                'question' => 'What do you know about the daily habits or routines of your grandparents\' lives?',
                'tags' => ['Grandparents’ & Ancestors’ Lives'],
            ],

            [
                'question' => 'Where were you born, and where did you spend your childhood years?',
                'tags' => ['Early Childhood'],
            ],
            [
                'question' => 'What is the earliest memory you can recall?',
                'tags' => ['Early Childhood'],
            ],
            [
                'question' => 'Who were your closest friends as a child?',
                'tags' => ['Early Childhood'],
            ],
            [
                'question' => 'When you were young, what did you dream of becoming when you grew up?',
                'tags' => ['Early Childhood'],
            ],
            [
                'question' => 'Tell me one memory you have of spending time with your family as a child.',
                'tags' => ['Early Childhood'],
            ],

            [
                'question' => 'How would you describe your parents\' personalities?',
                'tags' => ['Memories of My Parents'],
            ],
            [
                'question' => 'What is a favorite memory you have with each of your parents?',
                'tags' => ['Memories of My Parents'],
            ],
            [
                'question' => 'Tell me one way each of your parents has influenced the person you\'ve become.',
                'tags' => ['Memories of My Parents'],
            ],
            [
                'question' => 'Tell me a little about each of your parents\' childhoods.',
                'tags' => ['Memories of My Parents'],
            ],
            [
                'question' => 'What did each of your parents do for occupations/employment during their lives?',
                'tags' => ['Memories of My Parents'],
            ],

            [
                'question' => 'What were some of your favorite subjects or activities during school?',
                'tags' => ['School Years'],
            ],
            [
                'question' => 'Tell me about one of your best friends in school growing up. What did you enjoy doing together?',
                'tags' => ['School Years'],
            ],
            [
                'question' => 'Did you have a teacher who had a big impact on your life? How did they influence you?',
                'tags' => ['School Years'],
            ],
            [
                'question' => 'What were some of the biggest challenges you faced during school?',
                'tags' => ['School Years'],
            ],
            [
                'question' => 'Tell me about a memorable event or achievement from your school years.',
                'tags' => ['School Years'],
            ],

            [
                'question' => 'What values or traditions were important in your family as you were growing up?',
                'tags' => ['Family Life & Traditions'],
            ],
            [
                'question' => 'What family routines or activities do you remember most vividly?',
                'tags' => ['Family Life & Traditions'],
            ],
            [
                'question' => 'Who is someone in your family had a big influence on you, and why?',
                'tags' => ['Family Life & Traditions'],
            ],
            [
                'question' => 'What were some of your favorite meals, holidays, or celebrations with your family?',
                'tags' => ['Family Life & Traditions'],
            ],
            [
                'question' => 'What unique traits or qualities stand out about each of your family members?',
                'tags' => ['Family Life & Traditions'],
            ],

            [
                'question' => 'What are you most grateful for this holiday season, and why?',
                'tags' => ['The Holidays'],
            ],
            [
                'question' => 'What\'s something you loved about the holiday season when you were a kid?',
                'tags' => ['The Holidays'],
            ],
            [
                'question' => 'What\'s something you love about the holidays now, and what traditions are most meaningful to you?',
                'tags' => ['The Holidays'],
            ],
            [
                'question' => 'What advice would you give to parents about creating magical and memorable holidays for their children?',
                'tags' => ['The Holidays'],
            ],
            [
                'question' => 'Is there a holiday memory that stands out as your happiest or most cherished?',
                'tags' => ['The Holidays'],
            ],

            [
                'question' => 'What do you remember most about when you were in high school?',
                'tags' => ['Teen Years'],
            ],
            [
                'question' => 'What\'s something you loved as a teenager?',
                'tags' => ['Teen Years'],
            ],
            [
                'question' => 'What music or movies were you into as a teenager?',
                'tags' => ['Teen Years'],
            ],
            [
                'question' => 'Were you involved in any extra curricular sports, clubs, or activities in high school?',
                'tags' => ['Teen Years'],
            ],
            [
                'question' => 'What were your dreams or plans for the future during your teenage years?',
                'tags' => ['Teen Years'],
            ],

            [
                'question' => 'Who was your first boyfriend, girlfriend, or crush? What do you remember about them?',
                'tags' => ['Relationships & Love'],
            ],
            [
                'question' => 'When did you first fall in love, and what was that experience like?',
                'tags' => ['Relationships & Love'],
            ],
            [
                'question' => 'How did you meet your spouse or long-term partner?',
                'tags' => ['Relationships & Love'],
            ],
            [
                'question' => 'What do you remember about your early dates with your partner?',
                'tags' => ['Relationships & Love'],
            ],
            [
                'question' => 'When did you realize that this person was "the one" for you?',
                'tags' => ['Relationships & Love'],
            ],
            [
                'question' => 'Did you have any relationships that were longer term but ended? What do you remember about them?',
                'tags' => ['Relationships & Love'],
            ],

            [
                'question' => 'What did you study in college, and why did you choose that field?',
                'tags' => ['College / Early Adulthood'],
            ],
            [
                'question' => 'Tell me about one of your closest friends during your college or early adult years. How did you meet them?',
                'tags' => ['College / Early Adulthood'],
            ],
            [
                'question' => 'Tell me one of your favorite memories from college.',
                'tags' => ['College / Early Adulthood'],
            ],
            [
                'question' => 'Tell me about an adventure had as a young adult.',
                'tags' => ['College / Early Adulthood'],
            ],
            [
                'question' => 'How did your views on life start to change over the course of your twenties?',
                'tags' => ['College / Early Adulthood'],
            ],

            [
                'question' => 'Who have been some of your closest friends over the years?',
                'tags' => ['Friendships & Social Life'],
            ],
            [
                'question' => 'What lessons have you learned about friendship over your life?',
                'tags' => ['Friendships & Social Life'],
            ],
            [
                'question' => 'Have you been part of any groups, clubs, or communities that were meaningful to you?',
                'tags' => ['Friendships & Social Life'],
            ],
            [
                'question' => 'Tell me about something you and your friends like to do together.',
                'tags' => ['Friendships & Social Life'],
            ],
            [
                'question' => 'Tell me one of your favorite memories with your friends.',
                'tags' => ['Friendships & Social Life'],
            ],

            [
                'question' => 'Tell me about one personal achievement you are proud of.',
                'tags' => ['Achievements & Milestones'],
            ],
            [
                'question' => 'Tell me about a major goal you set for yourself in life.',
                'tags' => ['Achievements & Milestones'],
            ],
            [
                'question' => 'Tell me about a time your celebrated a big accomplishment or milestone.',
                'tags' => ['Achievements & Milestones'],
            ],
            [
                'question' => 'Tell me about someone who has helped you achieve your dreams.',
                'tags' => ['Achievements & Milestones'],
            ],
            [
                'question' => 'What advice would you give to others who want to achieve similar goals to what you have?',
                'tags' => ['Achievements & Milestones'],
            ],

            [
                'question' => 'Tell me about a big challenge you\'ve faced in your life.',
                'tags' => ['Hardships & Challenges'],
            ],
            [
                'question' => 'Tell me about a challenge you have overcome. What did you learn from the experience?',
                'tags' => ['Hardships & Challenges'],
            ],
            [
                'question' => 'Tell me about a time when you had to make a difficult choice.',
                'tags' => ['Hardships & Challenges'],
            ],
            [
                'question' => 'Who is someone who had supported you during your toughest times?',
                'tags' => ['Hardships & Challenges'],
            ],
            [
                'question' => 'How have the challenges you faced shaped who you are today?',
                'tags' => ['Hardships & Challenges'],
            ],

            [
                'question' => 'What hobbies or activities have brought you the most joy?',
                'tags' => ['Hobbies & Passions'],
            ],
            [
                'question' => 'How did you get started with your hobbies, and who introduced them to you?',
                'tags' => ['Hobbies & Passions'],
            ],
            [
                'question' => 'What skills or talents do you have that make you feel proud?',
                'tags' => ['Hobbies & Passions'],
            ],
            [
                'question' => 'How have your hobbies changed as you\'ve grown older?',
                'tags' => ['Hobbies & Passions'],
            ],
            [
                'question' => 'Tell me about something you are passionate about.',
                'tags' => ['Hobbies & Passions'],
            ],

            [
                'question' => 'Tell me one memory that stands out to you from your wedding day.',
                'tags' => ['Marriage & Commitment'],
            ],
            [
                'question' => 'What advice would you give to a newlywed couple about marriage?',
                'tags' => ['Marriage & Commitment'],
            ],
            [
                'question' => 'Tell me one joyful thing and one challenging thing about marriage.',
                'tags' => ['Marriage & Commitment'],
            ],
            [
                'question' => 'What was your first home together like?',
                'tags' => ['Marriage & Commitment'],
            ],
            [
                'question' => 'How did you and your spouse get through difficult times together?',
                'tags' => ['Marriage & Commitment'],
            ],

            [
                'question' => 'What do you remember most about the day each of your children was born?',
                'tags' => ['Parenthood'],
            ],
            [
                'question' => 'How did becoming a parent change your perspective on life?',
                'tags' => ['Parenthood'],
            ],
            [
                'question' => 'What do you remember about your children during their first few years?',
                'tags' => ['Parenthood'],
            ],
            [
                'question' => 'What is one of your proudest memories as a parent?',
                'tags' => ['Parenthood'],
            ],
            [
                'question' => 'Tell me one of your favorite memories about each of your children.',
                'tags' => ['Parenthood'],
            ],

            [
                'question' => 'What are the core values or beliefs that guide your life?',
                'tags' => ['Personal Values & Beliefs'],
            ],
            [
                'question' => 'Tell me about something or someone that has had a major influence on your values.',
                'tags' => ['Personal Values & Beliefs'],
            ],
            [
                'question' => 'Have your beliefs or values changed over the years? How?',
                'tags' => ['Personal Values & Beliefs'],
            ],
            [
                'question' => 'What advice would you pass on about living a meaningful life?',
                'tags' => ['Personal Values & Beliefs'],
            ],
            [
                'question' => 'What advice do you have about handling conflicts or disagreements?',
                'tags' => ['Personal Values & Beliefs'],
            ],

            [
                'question' => 'What spiritual or religious beliefs are most important to you, and why?',
                'tags' => ['Spirituality & Faith'],
            ],
            [
                'question' => 'Tell me about something or someone that has had a big impact on your spiritual journey.',
                'tags' => ['Spirituality & Faith'],
            ],
            [
                'question' => 'How has your faith evolved throughout your life?',
                'tags' => ['Spirituality & Faith'],
            ],
            [
                'question' => 'How do you express your spirituality or faith in daily life?',
                'tags' => ['Spirituality & Faith'],
            ],
            [
                'question' => 'What role does your spirituality or faith play in finding purpose or meaning in life?',
                'tags' => ['Spirituality & Faith'],
            ],

            [
                'question' => 'What motivated you to enlist in the armed forces?',
                'tags' => ['Military Service'],
            ],
            [
                'question' => 'What was basic training like for you?',
                'tags' => ['Military Service'],
            ],
            [
                'question' => 'Where did you serve, and what were your responsibilities?',
                'tags' => ['Military Service'],
            ],
            [
                'question' => 'Did you form any close relationships during your service?',
                'tags' => ['Military Service'],
            ],
            [
                'question' => 'Tell me one way your military experience shaped who you are today.',
                'tags' => ['Military Service'],
            ],

            [
                'question' => 'What do you hope future generations remember about you?',
                'tags' => ['Legacy & Reflections'],
            ],
            [
                'question' => 'If you could go back in time, what advice would you give to your younger self?',
                'tags' => ['Legacy & Reflections'],
            ],
            [
                'question' => 'Tell me about a hope you have for the future of your family, friends, or community.',
                'tags' => ['Legacy & Reflections'],
            ],
            [
                'question' => 'What lessons or values do you think are most important to pass on to the next generation?',
                'tags' => ['Legacy & Reflections'],
            ],
            [
                'question' => 'What legacy would you be most proud to leave behind?',
                'tags' => ['Legacy & Reflections'],
            ],

            [
                'question' => 'What was your first job?',
                'tags' => ['Career & Professional Journey'],
            ],
            [
                'question' => 'How did you end up choosing your career path?',
                'tags' => ['Career & Professional Journey'],
            ],
            [
                'question' => 'Tell me about someone who influenced or mentored you in your career.',
                'tags' => ['Career & Professional Journey'],
            ],
            [
                'question' => 'What\'s a big lesson you learned from your early years in the workforce?',
                'tags' => ['Career & Professional Journey'],
            ],
            [
                'question' => 'Tell me about a moment when you felt proud of your work.',
                'tags' => ['Career & Professional Journey'],
            ],
        ];

        foreach ($questionsData as $index => $data) {
            $questionId = (env('APP_ENV') === 'production') ? $index + 2000 : $index + 3000;

            // Create question with new required fields
            $question = Question::forceCreate([
                'id' => $questionId,
                'question_text' => $data['question'],
                'to_user_id' => null,
                'question_type' => Question::PUBLIC_QUESTION_TYPE,
            ]);

            // Handle tags
            foreach ($data['tags'] as $tagTitle) {
                $tag = Tag::firstOrCreate(['title' => $tagTitle]);

                TagQuestion::firstOrCreate([
                    'question_id' => $question->id,
                    'tag_id' => $tag->id,
                ]);
            }
        }
    }

    public function down()
    {
        Schema::disableForeignKeyConstraints();

        TagQuestion::truncate();
        Question::truncate();
        Tag::truncate();

        Schema::enableForeignKeyConstraints();
    }
};
