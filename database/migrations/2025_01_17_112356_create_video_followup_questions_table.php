<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('video_followup_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('video_id')->constrained('videos')->onDelete('cascade');
            $table->foreignId('followup_question_id')->constrained('questions')->onDelete('cascade');
            $table->timestamps();

            // Ensure unique combination of video and follow-up question
            $table->unique(['video_id', 'followup_question_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('video_followup_questions');
    }
}; 