<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('username'); // Remove the username column
            $table->string('middle_name')->nullable();
            $table->string('last_name');
            $table->string('full_name')->nullable()->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('username'); // Re-add the username column
            $table->dropColumn('middle_name');
            $table->dropColumn('last_name');
            $table->string('full_name')->nullable(false)->change();
        });

    }
};
