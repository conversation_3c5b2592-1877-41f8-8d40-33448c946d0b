<?php

use App\Models\Question;
use App\Models\Tag;
use App\Models\TagQuestion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Array representing questions and their associated tags
        $questionsWithTags = [
            [
                'id' => 1,
                'question' => 'Tell us about your grandparents. What do you know about them?',
                'tags' => ['Family'],
            ],
            [
                'id' => 2,
                'question' => 'What do you know about how your parents met and their courtship?',
                'tags' => ['Family'],
            ],
            [
                'id' => 3,
                'question' => 'What do you know about your parents’ childhood?',
                'tags' => ['Family'],
            ],
            [
                'id' => 4,
                'question' => 'What do you know about your grandparents’ early life?',
                'tags' => ['Family'],
            ],
            [
                'id' => 5,
                'question' => 'What work did your parents do during their adult lives?',
                'tags' => ['Family'],
            ],
            [
                'id' => 6,
                'question' => 'What work did your grandparents do during their adult lives?',
                'tags' => ['Family'],
            ],
            [
                'id' => 7,
                'question' => 'Do you know any interesting stories about your grandparents? Tell us one.',
                'tags' => ['Family'],
            ],
            [
                'id' => 8,
                'question' => 'When you were growing up, what kind of a relationship did you have with your aunts, uncles, great aunts, or great uncles?',
                'tags' => ['Family'],
            ],
            [
                'id' => 9,
                'question' => 'What were your siblings like growing up? How did you spend time with them?',
                'tags' => ['Family'],
            ],
            [
                'id' => 10,
                'question' => 'Did your family take any vacations when you were growing up? What were they like?',
                'tags' => ['Family'],
            ],
            [
                'id' => 11,
                'question' => 'What do you know about your family’s geographic origins? Where is your family from?',
                'tags' => ['Family'],
            ],
            [
                'id' => 12,
                'question' => 'What are some of your favorite childhood memories?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 13,
                'question' => 'Tell us a little about what you remember of elementary school.',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 14,
                'question' => 'Who were your best friends as a kid? How did you spend time together?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 15,
                'question' => 'Do you remember your first crush? Tell us about them.',
                'tags' => ['Early Life', 'Relationships'],
            ],
            [
                'id' => 16,
                'question' => 'What do you remember most about junior high or high school?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 17,
                'question' => 'When you were a teenager, what were your priorities? What did you care about?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 18,
                'question' => 'What were some of your favorite things to listen to or watch on television as a child?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 19,
                'question' => 'Did you ever get in big trouble with your parents? Tell us about it.',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 20,
                'question' => 'What was your first job? What do you remember most about it?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 21,
                'question' => 'What did you do after high school? How did you make that decision?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 22,
                'question' => 'What were your college years like?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 23,
                'question' => 'What did you study in college? Why did you decide to study that?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 24,
                'question' => 'What were some fun memories you had during college?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 25,
                'question' => 'Tell us about the friends you had in high school.',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 26,
                'question' => 'What did you do during your summers as a kid?',
                'tags' => ['Early Life'],
            ],
            [
                'id' => 27,
                'question' => 'What were some of your first jobs after you finished your education?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 28,
                'question' => 'What jobs in your early adulthood did you like or dislike, and why?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 29,
                'question' => 'Do you remember any of your bosses or supervisors from your early jobs? What do you remember about them?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 30,
                'question' => 'What do you remember about living on your own for the first time?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 31,
                'question' => 'Did you ever live with roommates? Tell us about them.',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 32,
                'question' => 'What kind of hobbies did you have in your 20s?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 33,
                'question' => 'Tell us about a vacation that you’ve gone on as an adult that you remember fondly.',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 34,
                'question' => 'What were your favorite activities to do with your spouse early on in your relationship?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 35,
                'question' => 'Tell us about a difficult time you went through as an adult.',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 36,
                'question' => 'Tell us about your service in the armed forces. Where did you serve, and what do you remember most about it?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 37,
                'question' => 'Tell us about your immigration. What do you remember about the actual journey of immigrating?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 38,
                'question' => 'Why did your family immigrate?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 39,
                'question' => 'Do you have family that lives in another country? Tell us about them. Are you in touch with them?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 40,
                'question' => 'Tell us about your siblings and your relationship with them as adults.',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 41,
                'question' => 'Tell us about foods you like that you associate with your family heritage.',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 42,
                'question' => 'Who have been your closest friends in your adult life? How did you meet them?',
                'tags' => ['Adulthood'],
            ],
            [
                'id' => 43,
                'question' => 'What was your love life like before you met your spouse?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 44,
                'question' => 'Who was your first girlfriend or boyfriend? Tell us about them. What do you remember about dating them?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 45,
                'question' => 'Did you ever go through a hard breakup? What do you remember most about it?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 46,
                'question' => 'How did you meet your spouse? What do you remember about your first encounters?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 47,
                'question' => 'Tell us about your first date with your spouse.',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 48,
                'question' => 'Tell us about your marriage proposal. How did you feel that day?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 49,
                'question' => 'What did you and your spouse do for fun during the time when you were dating?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 50,
                'question' => 'Tell us about planning your wedding and the celebration itself.',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 51,
                'question' => 'Tell us about your honeymoon. Where did you go? What do you remember from the trip? What do you remember from it?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 52,
                'question' => 'What do you remember about when you first moved in with your spouse?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 53,
                'question' => 'In the early days of your relationship, what are some of the favorite times you had with your spouse?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 54,
                'question' => 'What is something about your spouse that impressed you when you were first getting to know each other?',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 55,
                'question' => 'Tell us something you love about your spouse.',
                'tags' => ['Relationships'],
            ],
            [
                'id' => 56,
                'question' => 'Tell us about your journey of faith and religion. How has your faith changed over the years?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 57,
                'question' => 'Do you have any advice you’d like to share for your children and grandchildren about faith?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 58,
                'question' => 'How does your religion show up in your everyday life? Do you have any religious practices or rituals that you engage in?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 59,
                'question' => 'What’s one piece of advice you’d give your younger self?',
                'tags' => ['Early Life', 'Reflections'],
            ],
            [
                'id' => 60,
                'question' => 'What is a valuable lesson your parents or grandparents taught you?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 61,
                'question' => 'Tell us about a difficult period in your life. How did you get through it? How did it change you or your worldview?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 62,
                'question' => 'What do you believe is the key to a successful marriage?',
                'tags' => ['Reflections', 'Relationships'],
            ],
            [
                'id' => 63,
                'question' => 'When you are gone, how do you want to be remembered?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 64,
                'question' => 'When you are gone, what would you want to tell your family?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 65,
                'question' => 'What wisdom do you want to pass to your children or grandchildren?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 66,
                'question' => 'What does it mean to you to live a successful life? What do you think is the key to a successful life?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 67,
                'question' => 'What is one thing that happened this year that you think you’ll remember for the rest of your life?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 68,
                'question' => 'What is one thing you hope to accomplish this year?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 69,
                'question' => 'If you had the opportunity to do it all over again, is there anything in your life that stands out as something you would have done differently?',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 70,
                'question' => 'Are there any significant world events that affected you? Tell us about it.',
                'tags' => ['Reflections'],
            ],
            [
                'id' => 71,
                'question' => 'Are there any sayings or pieces of wisdom that you live by?',
                'tags' => ['Reflections'],
            ],
        ];

        foreach ($questionsWithTags as $data) {

            $question = Question::forceCreate(['id' => $data['id'], 'question_text' => $data['question']]);

            foreach ($data['tags'] as $tagTitle) {
                // Find or create the tag instance
                $tag = Tag::firstOrCreate(['title' => $tagTitle]);

                // Associate the question with the tag
                TagQuestion::firstOrCreate([
                    'question_id' => $question->id,
                    'tag_id' => $tag->id,
                ]);
            }
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::disableForeignKeyConstraints();

        TagQuestion::truncate();
        Question::truncate();
        Tag::truncate();

        Schema::enableForeignKeyConstraints();

    }
};
