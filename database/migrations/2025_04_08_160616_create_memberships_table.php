<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('memberships', function (Blueprint $table) {
            $table->id();
            $table->foreignId('membership_type_id')->constrained('membership_types');
            $table->timestamp('started_at');
            $table->timestamp('end_at');
            $table->boolean('is_autopay_enabled')->default(false);
            $table->integer('free_trials_used_count')->default(0);
            $table->integer('available_gifts_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('memberships');
    }
};
