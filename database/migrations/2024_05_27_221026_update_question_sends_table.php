<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('question_sends', function (Blueprint $table) {
            $table->unsignedBigInteger('relationship_id')->nullable()->change();
            $table->unsignedBigInteger('sent_to_user_id')->nullable();
            $table->unsignedBigInteger('sent_from_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('question_sends', function (Blueprint $table) {
            $table->unsignedBigInteger('relationship_id')->nullable(false)->change();
            $table->dropColumn(['sent_to_user_id', 'sent_from_user_id']);
        });
    }
};
