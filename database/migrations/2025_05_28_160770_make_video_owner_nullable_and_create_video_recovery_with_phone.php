<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Make owner_user_id nullable in videos table
        Schema::table('videos', function (Blueprint $table) {
            $table->foreignId('owner_user_id')->nullable()->change();
        });

        // Create video_recovery_with_phone table
        Schema::create('video_recovery_with_phone', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number');
            $table->foreignId('video_id')->constrained('videos')->onDelete('cascade');
            $table->timestamp('recovered_at')->nullable();
            $table->timestamps();

            // Index for faster lookups
            $table->index('phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop video_recovery_with_phone table
        Schema::dropIfExists('video_recovery_with_phone');

        // Make owner_user_id required again
        Schema::table('videos', function (Blueprint $table) {
            $table->foreignId('owner_user_id')->nullable(false)->change();
        });
    }
}; 