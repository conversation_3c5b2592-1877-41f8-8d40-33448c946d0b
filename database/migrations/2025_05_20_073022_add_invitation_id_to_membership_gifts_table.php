<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membership_gifts', function (Blueprint $table) {
            // Make to_user_id nullable since gift can be sent to either user or invitation
            $table->foreignId('to_user_id')->nullable()->change();
            
            // Add invitation_id column
            $table->foreignId('invitation_id')->nullable()->after('to_user_id')
                  ->constrained('invitations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_gifts', function (Blueprint $table) {
            // Remove invitation_id
            $table->dropForeign(['invitation_id']);
            $table->dropColumn('invitation_id');
            
            // Make to_user_id required again
            $table->foreignId('to_user_id')->nullable(false)->change();
        });
    }
};
