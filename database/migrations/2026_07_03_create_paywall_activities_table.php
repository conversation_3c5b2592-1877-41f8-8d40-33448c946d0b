<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**y
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paywall_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('activity_type_id');
            $table->unsignedBigInteger('membership_gift_id')->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->integer('gift_count_purchased')->nullable(); // Number of gift counts purchased
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('activity_type_id')->references('id')->on('paywall_activity_types');
            $table->foreign('membership_gift_id')->references('id')->on('membership_gifts')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paywall_activities');
    }
};
